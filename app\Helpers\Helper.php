<?php
use Illuminate\Http\Request;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\User;
use App\Post;
use App\Message;
use App\AdSense;
use App\Instagram;
use App\Menu;
use App\Client;
use App\menu_item;   
use App\Category;
use App\Serie;

     /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
    **/
    function Users()
    {

        $Users = User::all();
        return $Users;
    }
    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
    **/
    function AdSenses()
    {

        $AdSenses = AdSense::all();
        return $AdSenses;
    }
     /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
    **/
    function Posts()
    {

        $Posts = Post::all();
        return $Posts;
    }
     /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
    **/
    function roles()
    {

        $roles = Role::all();
        return $roles;
    }
     /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
    **/
    function Messages()
    {

        $Messages = Message::simplePaginate(6);
        return $Messages;
    }
     /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
    **/
    function Instagrams()
    {

        $Instagrams = Instagram::all();
        return $Instagrams;
    }

     /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
    **/
    function Categories()
    {
        $Categories = Category::whereHas('movies')
            ->get();
        return $Categories;
    }
    function Categories_series() {
        // Get all unique category IDs linked to series from the 'category_serie' pivot table
        $categoryIds = \DB::table('category_serie')->distinct()->pluck('category_id');
    
        // Fetch the Category models
        $Categories = Category::whereIn('id', $categoryIds)->get();
    
        return $Categories;
    }

    function getYears() {
        return Post::whereNotNull('year')->distinct()->orderBy('year', 'desc')->pluck('year');
    }
    
 

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
    **/

    function Menus()
    {
        $Menus = menu_item::where('menu_id', '=', 1)->get();
        return $Menus;
    }

    function Main_Menus()
    {
        $Menus = menu_item::where('menu_id', '=', 1)->get();
        return $Menus;
    }

    function Other_Menus()
    {
        $Menus = menu_item::join("menus","menus.id","=","menu_items.menu_id")
        ->select('menu_items.*')
        ->where('menus.Title', '=',"Other")->get();
        return $Menus;
    }
     