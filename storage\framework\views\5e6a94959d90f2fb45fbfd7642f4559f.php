<!DOCTYPE html>
<html lang="zxx">
<head>
<!-- ===================================== Meta Site =============================== -->
<meta charset="utf-8">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1.0, user-scalable=no">
<!-- =================== Laravel description site edit delete From Admin Panel ================== -->
<meta name="description" content="<?php echo option('MetaDescription'); ?>">
<!-- =================== Laravel keywords site edit delete From Admin Panel ===================== -->
<meta name="keywords" content="<?php echo option('MetaKeyWords'); ?>">  
<!-- ========================== Favicon icon ==================================================== -->
<link rel="apple-touch-icon" sizes="57x57" href="<?php echo e(asset('assets/images/favicon/apple-icon-57x57.png')); ?>">
<link rel="apple-touch-icon" sizes="60x60" href="<?php echo e(asset('assets/images/favicon/apple-icon-60x60.png')); ?>">
<link rel="apple-touch-icon" sizes="72x72" href="<?php echo e(asset('assets/images/favicon/apple-icon-72x72.png')); ?>">
<link rel="apple-touch-icon" sizes="76x76" href="<?php echo e(asset('assets/images/favicon/apple-icon-76x76.png')); ?>">
<link rel="apple-touch-icon" sizes="114x114" href="<?php echo e(asset('assets/images/favicon/apple-icon-114x114.png')); ?>">
<link rel="apple-touch-icon" sizes="120x120" href="<?php echo e(asset('assets/images/favicon/apple-icon-120x120.png')); ?>">
<link rel="apple-touch-icon" sizes="144x144" href="<?php echo e(asset('assets/images/favicon/apple-icon-144x144.png')); ?>">
<link rel="apple-touch-icon" sizes="152x152" href="<?php echo e(asset('assets/images/favicon/apple-icon-152x152.png')); ?>">
<link rel="apple-touch-icon" sizes="180x180" href="<?php echo e(asset('assets/images/favicon/apple-icon-180x180.png')); ?>">
<link rel="icon" type="image/png" href="<?php echo e(asset('assets/images/favicon/android-icon-192x192.png')); ?>">
<link rel="icon" type="image/png" sizes="32x32" href="<?php echo e(asset(option('Favicon'))); ?>">
<link rel="icon" type="image/png" sizes="96x96" href="<?php echo e(asset(option('Favicon'))); ?>">
<link rel="icon" type="image/png" sizes="16x16" href="<?php echo e(asset(option('Favicon'))); ?>">
<link rel="manifest" href="<?php echo e(asset('assets/images/favicon/manifest.json')); ?>">
<!-- ===========================================  google apis =========================== -->
<title><?php echo option('SiteTitle'); ?></title> 
<!-- ===========================================  google apis =========================== -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css?family=Montserrat:400,500,600&display=swap" rel="stylesheet">
<!-- ===========================================  googleapis =========================== -->
<link href="https://fonts.googleapis.com/css2?family=Lalezar&display=swap" rel="stylesheet">
<!-- ===========================================  googleapis =========================== -->
<link href="https://fonts.googleapis.com/css?family=Righteous&display=swap" rel="stylesheet">
<!-- =========================================  main =================================== -->
<link href="<?php echo asset('Dashboard/assets/Fontawesome/css/all.css'); ?>" rel="stylesheet"> 

<link rel="stylesheet" href="<?php echo asset('assets/css/videojs2.css'); ?>" />

<link rel="stylesheet" href="<?php echo asset('assets/css/slider.css'); ?>" />
<!-- ==================================  stylesheet =================================== -->
<link rel="stylesheet" href="<?php echo asset('assets/css/uikit.min.css'); ?>" />
<!-- ==========================  uikit ================================================= -->
<link rel="stylesheet" href="<?php echo asset('assets/css/main.css'); ?>" />
<!-- ==========================  LaravelLocalization =================================== -->
<span class="display-zero"><?php echo $Locale = LaravelLocalization::getCurrentLocale(); ?></span>
<?php if($Locale == 'ar'): ?>
<link href="https://fonts.googleapis.com/css2?family=Almarai:wght@300;700&display=swap" rel="stylesheet"> 
<link rel="stylesheet" href="<?php echo asset('assets/css/rtl.css'); ?>"/>
<?php else: ?> 
<?php endif; ?>
<style>
  /* The main container for the language selector */
.language-selector {
  position: relative;
  display: inline-block;
  border: none;
  z-index: 100;
}

/* The button that shows the selected language */
.language-toggle {
  padding: 13px 15px ;
  background-color:transparent;
  cursor: pointer;
  color: white;
  border: 0;
}

/* The dropdown list of language options, hidden by default */
.language-dropdown {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  background-color: black;
  border-radius: 5px;
  margin-top: 5px;
  list-style-type: none;
  padding: 0;
  width: 100%;
}

/* Display the dropdown when the button is clicked */
.language-selector.open .language-dropdown {
  display: block;
}

/* Individual language option styles */
.language-option {
  padding: 8px 20px 10px 10px;
  cursor: pointer;
  font-size: 14px;
}

.language-option a {
  color: white;
  text-decoration: none;
  display: block;
}

.language-option:hover {
  background-color: #242323;
}

/* Highlight the active language */
.language-option a.active-language {
  font-weight: bold;
  color: rgb(116, 8, 8); /* Adjust color for active language */
}

/* Optional: Styling for the button when the dropdown is open */
.language-selector.open .language-toggle {
  background-color: #f0f0f0;
}

/* The main container for the language selector */
.menu-toggle {
  padding: 13px 15px ;
  background-color:transparent;
  cursor: pointer;
  color: white;
  border: 0;
}

.menu-dropdown {
  display: none;
  position: absolute;
  top: 100%;
  right: 20px;
  background-color: black;
  border-radius: 5px;
  margin-top: 2px;
  list-style-type: none;
  padding-left: 10;
  padding-right: 10;
  width: 200px;
}
.menu-link{
  padding: 5px;
  text-decoration: none; /* Removes underline */
  color: white; 
  padding-top: 5px;
  padding-bottom: 5px;
  width: 100%;
}
.menu-link:hover{
  text-decoration: none; /* Removes underline */
  color: rgb(156, 16, 16); 
}
.menu-selector {
  position: relative;
  display: inline-block;
  border: none;
  z-index: 100;
}
/* From Uiverse.io by LightAndy1 */ 
.group {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  line-height: 28px;
  align-items: center;
  position: relative;
  max-width: 100%;
  margin-bottom: -30px;
  margin-top: 10px;
  display: none;
  margin-left: 5px
}

.input {
  font-family: "Montserrat", sans-serif;
  width: 100%;
  height: 45px;
  padding-left: 2.5rem;
  box-shadow: 0 0 0 1.5px #2b2c37, 0 0 25px -17px #000;
  border: 0;
  border-radius: 12px;
  background-color: #191818;
  outline: none;
  color: #bdbecb;
  transition: all 0.25s cubic-bezier(0.19, 1, 0.22, 1);
  cursor: text;
  z-index: 0;
}

.input::placeholder {
  color: #bdbecb;
}

.input:hover {
  box-shadow: 0 0 0 2.5px #2f303d, 0px 0px 25px -15px #000;
}

.input:active {
  transform: scale(0.95);
}

.input:focus {
  box-shadow: 0 0 0 2.5px #2f303d;
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 1rem;
  fill: #bdbecb;
  width: 1rem;
  height: 1rem;
  pointer-events: none;
  z-index: 1;
}
.btn_display{
  border: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: transparent;
  cursor: pointer;
  color: white;
  padding-left: 5px;
  padding-right: 5px;
  height: 45px
}
/* Scrollbar for WebKit Browsers (Chrome, Edge, Safari) */
.menu-dropdown::-webkit-scrollbar {
    width: 8px; /* Width of scrollbar */
}

.menu-dropdown::-webkit-scrollbar-track {
    background: #333; /* Background color of the track */
    border-radius: 4px;
}

.menu-dropdown::-webkit-scrollbar-thumb {
    background: #666; /* Scrollbar color */
    border-radius: 4px;
}

.menu-dropdown::-webkit-scrollbar-thumb:hover {
    background: #888; /* Color when hovering */
}
.menu-dropdown-v2 {
    height: 200px; /* Scrollable area */
    overflow-y: auto;
    overflow-x: hidden;
    display: grid;
    grid-template-columns: repeat(3, 1fr); /* 3 columns */
    gap: 10px; /* Space between items */
    padding: 10px;
    width: 300px
}
.menu-dropdown{
  max-height: 400px;
  overflow-y: auto;
  overflow-x: hidden;
}

.menu-dropdown-v2 .menu-link {
    background: rgba(255, 255, 255, 0.1); /* Light transparent background */
    border-radius: 5px;
    text-align: center;
    padding: 10px;
    font-size: 16px;
    font-weight: bold;
    color: white;
    cursor: pointer;
    max-width: 150px;
}
.menu-dropdown-v2 .menu-link a {
    text-decoration: none;
    color: white;
    display: block;
}
.menu-dropdown-v2 .menu-link a:hover {
    text-decoration: none;
    color: white;
    display: block;
}


/* From Uiverse.io by Mohammad-Rahme-576 */ 
/* Container Styles */
.tooltip-container {
  position: relative;
  display: inline-block;
  font-family: "Arial", sans-serif;
  overflow: visible;
  
}

/* Button Styles */
.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f74141, #510101);
  color: white;
  padding: 14px 28px;
  border-radius: 50px;
  cursor: pointer;
  transition:
    background 0.4s cubic-bezier(0.25, 0.8, 0.25, 1),
    transform 0.3s ease,
    box-shadow 0.4s ease;
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
  overflow: hidden;
}

.button-content::before {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: inherit;
  background: linear-gradient(
    135deg,
    rgba(110, 142, 251, 0.4),
    rgba(167, 119, 227, 0.4)
  );
  filter: blur(15px);
  opacity: 0;
  transition: opacity 0.5s ease;
  z-index: -1;
}

.button-content::after {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0) 70%
  );
  transform: scale(0);
  transition: transform 0.6s ease-out;
  z-index: -1;
}

.button-content:hover::before {
  opacity: 1;
}

.button-content:hover::after {
  transform: scale(1);
}

.button-content:hover {
  background: linear-gradient(135deg, #a777e3, #6e8efb);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2);
  transform: translateY(-4px) scale(1.03);
}

.button-content:active {
  transform: translateY(-2px) scale(0.98);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.15);
}

.text {
  font-size: 18px;
  font-weight: 600;
  margin-right: 12px;
  white-space: nowrap;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  transition: letter-spacing 0.3s ease;
}

.button-content:hover .text {
  letter-spacing: 1px;
}

.share-icon {
  fill: white;
  transition:
    transform 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55),
    fill 0.3s ease;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.button-content:hover .share-icon {
  transform: rotate(180deg) scale(1.1);
  fill: #ffffff;
}

/* Tooltip Styles */
.tooltip-content {
  position: absolute;
  top: 102%;
  left: 50%;
  transform: translateX(-50%) scale(0.8);
  background: white;
  border-radius: 15px;
  padding: 22px;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
  opacity: 0;
  visibility: hidden;
  transition:
    opacity 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55),
    transform 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55),
    visibility 0.5s ease;
  z-index: 100;
  pointer-events: none;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.9);
}

.tooltip-container:hover .tooltip-content {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) scale(1);
  pointer-events: auto;
}

/* Social Icons Styles */
.social-icons {
  display: flex;
  justify-content: space-between;
  gap: 12px;
}

.social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: #f0f0f0;
  transition:
    transform 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55),
    background 0.3s ease,
    box-shadow 0.4s ease;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.social-icon::before {
  content: "";
  position: absolute;
  inset: 0;
  background: radial-gradient(
    circle at center,
    rgba(255, 255, 255, 0.8) 0%,
    rgba(255, 255, 255, 0) 70%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.social-icon:hover::before {
  opacity: 1;
}

.social-icon svg {
  width: 24px;
  height: 24px;
  fill: #333;
  transition:
    transform 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55),
    fill 0.3s ease;
  z-index: 1;
}

.social-icon:hover {
  transform: translateY(-5px) scale(1.1);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

.social-icon:active {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
}

.social-icon:hover svg {
  transform: scale(1.2);
  fill: white;
}

.social-icon.twitter:hover {
  background: linear-gradient(135deg, #1da1f2, #1a91da);
}

.social-icon.facebook:hover {
  background: linear-gradient(135deg, #1877f2, #165ed0);
}

.social-icon.linkedin:hover {
  background: linear-gradient(135deg, #0077b5, #005e94);
}

/* Animation for Pulse Effect */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(110, 142, 251, 0.4);
  }
  70% {
    box-shadow: 0 0 0 20px rgba(110, 142, 251, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(110, 142, 251, 0);
  }
}

.button-content {
  animation: pulse 3s infinite;
}

/* Hover Ripple Effect */
@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

.button-content::before {
  content: "";
  position: absolute;
  inset: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: inherit;
  transform: scale(0);
  opacity: 0;
}

.button-content:active::before {
  animation: ripple 0.6s linear;
}

/* Tooltip Arrow */
.tooltip-content::before {
  content: "";
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  border-width: 0 10px 10px 10px;
  border-style: solid;
  border-color: transparent transparent rgba(255, 255, 255, 0.9) transparent;
  filter: drop-shadow(0 -3px 3px rgba(0, 0, 0, 0.1));
}

/* Accessibility */
.button-content:focus {
  outline: none;
  box-shadow:
    0 0 0 3px rgba(110, 142, 251, 0.5),
    0 8px 15px rgba(0, 0, 0, 0.1);
}

.button-content:focus:not(:focus-visible) {
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .button-content {
    padding: 12px 24px;
    border-radius: 40px;
  }

  .text {
    font-size: 16px;
  }

  .tooltip-content {
    width: 240px;
    padding: 18px;
  }

  .social-icon {
    width: 44px;
    height: 44px;
  }

  .social-icon svg {
    width: 20px;
    height: 20px;
  }
}

@media (max-width: 480px) {
  .button-content {
    padding: 10px 20px;
  }

  .text {
    font-size: 14px;
  }

  .tooltip-content {
    width: 200px;
    padding: 15px;
  }

  .social-icon {
    width: 40px;
    height: 40px;
  }

  .social-icon svg {
    width: 18px;
    height: 18px;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .tooltip-content {
    background: rgba(30, 30, 30, 0.9);
    color: white;
  }

  .tooltip-content::before {
    border-color: transparent transparent rgba(30, 30, 30, 0.9) transparent;
  }

  .social-icon {
    background: #2a2a2a;
  }

  .social-icon svg {
    fill: #e0e0e0;
  }
}

/* Print Styles */
@media print {
  .tooltip-container {
    display: none;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .button-content,
  .share-icon,
  .social-icon,
  .tooltip-content {
    transition: none;
  }

  .button-content {
    animation: none;
  }
}

/* Custom Scrollbar for Tooltip Content */
.tooltip-content::-webkit-scrollbar {
  width: 6px;
}

.tooltip-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.tooltip-content::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;
}

.tooltip-content::-webkit-scrollbar-thumb:hover {
  background: #555;
}


  </style> 
    </style> 
    <!-- ===============================  LaravelLocalization ====================== --> 
        <script>
        var showAds = <?php echo json_encode(!auth()->check() || (auth()->check() && !auth()->user()->hasHiddenAds()), 15, 512) ?>;
        var clickThreshold = <?php echo json_encode(3, 15, 512) ?>;; // Change this value as needed
    document.addEventListener("DOMContentLoaded", function() {
        if (window.Android?.setShowAds) {
            window.Android.setShowAds(showAds);
        }
    
        if (window.Android?.setClickThreshold) {
            window.Android.setClickThreshold(clickThreshold);
        }
    });
    
    document.addEventListener("click", function() {
        if (window.Android?.onUserClick) {
            window.Android.onUserClick();
        }
        
    });
    
    </script>
<!-- ===============================  LaravelLocalization ====================== --> 
</head>
<body>  
<!-- =============================== Content ==================================== -->
<nav id="tm-topbar" class="uk-navbar uk-contrast">
<div class="uk-container uk-container-center">
<ul class="uk-navbar-nav uk-hidden-small">
<li><a href="<?php echo option('Facebook'); ?>"><i class="uk-icon-facebook uk-icon-small"></i></a></li>
<li><a href="<?php echo option('Twitter'); ?>"><i class="uk-icon-twitter uk-icon-small"></i></a></li>
<li><a href="<?php echo option('Instagram'); ?>"><i class="uk-icon-instagram uk-icon-small"></i></a></li>
<li><a href="<?php echo option('youtube'); ?>"><i class="uk-icon-youtube uk-icon-small"></i></a></li>
<li><a href="<?php echo option('LinkedIn'); ?>"><i class="uk-icon-snapchat uk-icon-small"></i></a></li>
<li><a href="<?php echo option('Tumblr'); ?>"><i class="uk-icon-tumblr uk-icon-small"></i></a></li>
</ul>
<div class="uk-navbar-flip">
<ul class="uk-navbar-nav uk-hidden-small">

<div class="menu-selector">
  <button class="menu-toggle" onclick="toggleLanguageDropdown(3)" dir="<?php echo e(app()->getLocale() == 'ar' ? 'rtl' : 'ltr'); ?>" >
    <?php echo __('main.All_Genres'); ?>&ensp; <svg width="15px"  viewBox="0 0 1024 1024" fill="white" class="icon" version="1.1" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"><path d="M478.312 644.16c24.38 26.901 64.507 26.538 88.507-0.89l270.57-309.222c7.758-8.867 6.86-22.344-2.008-30.103-8.866-7.759-22.344-6.86-30.103 2.007L534.71 615.173c-7.202 8.231-17.541 8.325-24.782 0.335L229.14 305.674c-7.912-8.73-21.403-9.394-30.133-1.482s-9.394 21.403-1.482 30.134l280.786 309.833z" fill=""></path></g></svg>
  </button>
  
  <ul id="language-dropdown-3" class="menu-dropdown" style="display: none;">
    <?php $__currentLoopData = Categories(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $genre): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <li class="menu-link" dir="<?php echo e(app()->getLocale() == 'ar' ? 'rtl' : 'ltr'); ?>">
      <a class="menu-link" <?php if($genre->slug == "live-tv" ): ?>
        href="<?php echo url('live'); ?>" 
        <?php else: ?>
        href="<?php echo url('Cat'); ?>/<?php echo $genre->slug; ?>"
      <?php endif; ?> >
        <?php echo $genre->{'Title_'.$Locale}; ?>

      </a>
    </li>
  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</ul>
</div>

<div class="menu-selector">
  <button class="menu-toggle" onclick="toggleLanguageDropdown(4)" dir="<?php echo e(app()->getLocale() == 'ar' ? 'rtl' : 'ltr'); ?>" >
    <?php echo __('main.series_menu'); ?>&ensp; <svg width="15px"  viewBox="0 0 1024 1024" fill="white" class="icon" version="1.1" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"><path d="M478.312 644.16c24.38 26.901 64.507 26.538 88.507-0.89l270.57-309.222c7.758-8.867 6.86-22.344-2.008-30.103-8.866-7.759-22.344-6.86-30.103 2.007L534.71 615.173c-7.202 8.231-17.541 8.325-24.782 0.335L229.14 305.674c-7.912-8.73-21.403-9.394-30.133-1.482s-9.394 21.403-1.482 30.134l280.786 309.833z" fill=""></path></g></svg>
  </button>
  
  <ul id="language-dropdown-4" class="menu-dropdown" style="display: none;">
    <?php $__currentLoopData = Categories_series(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $genre): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <li class="menu-link" dir="<?php echo e(app()->getLocale() == 'ar' ? 'rtl' : 'ltr'); ?>">
      <a class="menu-link" href="<?php echo url('Cat'); ?>/<?php echo $genre->slug; ?>?type=series">
        <?php echo $genre->{'Title_'.$Locale}; ?>

      </a>
    </li>
  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</ul>
</div>

<div class="menu-selector">
  <button class="menu-toggle" onclick="toggleLanguageDropdown(5)" dir="<?php echo e(app()->getLocale() == 'ar' ? 'rtl' : 'ltr'); ?>" >
    <?php echo __('main.Year_release'); ?> &ensp; <svg width="15px"  viewBox="0 0 1024 1024" fill="white" class="icon" version="1.1" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"><path d="M478.312 644.16c24.38 26.901 64.507 26.538 88.507-0.89l270.57-309.222c7.758-8.867 6.86-22.344-2.008-30.103-8.866-7.759-22.344-6.86-30.103 2.007L534.71 615.173c-7.202 8.231-17.541 8.325-24.782 0.335L229.14 305.674c-7.912-8.73-21.403-9.394-30.133-1.482s-9.394 21.403-1.482 30.134l280.786 309.833z" fill=""></path></g></svg>
  </button>
  
  <ul id="language-dropdown-5" class="menu-dropdown menu-dropdown-v2" style="display: none;">
    <?php $__currentLoopData = getYears(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $year): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <li class="menu-link">
      <a href="<?php echo url('Cat'); ?>/all?year=<?php echo $year; ?>">
        <?php echo $year; ?> ◀
      </a>
    </li>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</ul>


</div>

<?php $__currentLoopData = Other_Menus(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $Menu): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
<li class="" dir="<?php echo e(app()->getLocale() == 'ar' ? 'rtl' : 'ltr'); ?>">
   <a class="" href="<?php echo e(url($Menu->url)); ?>" target="<?php echo e($Menu->target); ?>">
    <?php echo e($Menu->{'Title_'.$Locale}); ?>

  </a>
</li>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<li>
  <a href="<?php echo url('live'); ?>" style="color: #ED213A;">
    <?php echo __('main.live'); ?>

  </a>
</li>
<div class="language-selector"> 
<button class="language-toggle" onclick="toggleLanguageDropdown(1)" dir="<?php echo e(app()->getLocale() == 'ar' ? 'rtl' : 'ltr'); ?>">
  <?php echo __('main.Languages'); ?> &ensp; <svg width="15px"  viewBox="0 0 1024 1024" fill="white" class="icon" version="1.1" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"><path d="M478.312 644.16c24.38 26.901 64.507 26.538 88.507-0.89l270.57-309.222c7.758-8.867 6.86-22.344-2.008-30.103-8.866-7.759-22.344-6.86-30.103 2.007L534.71 615.173c-7.202 8.231-17.541 8.325-24.782 0.335L229.14 305.674c-7.912-8.73-21.403-9.394-30.133-1.482s-9.394 21.403-1.482 30.134l280.786 309.833z" fill=""></path></g></svg>
</button>

<ul id="language-dropdown-1" class="language-dropdown" style="display: none;">
    <?php $__currentLoopData = LaravelLocalization::getSupportedLocales(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $localeCode => $properties): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <?php if(in_array($localeCode, ['en', 'ar','fr'])): ?> 
            <li class="language-option">
                <a href="<?php echo e(LaravelLocalization::getLocalizedURL($localeCode, null, [], true)); ?>" 
                   class="<?php echo e(app()->getLocale() === $localeCode ? 'active-language' : ''); ?>">
                    <?php echo $properties['native']; ?>

                </a>
            </li>
        <?php endif; ?>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</ul>
</div>

</ul>
</div>
</div>
</nav>
<nav id="tm-header" class="uk-navbar">
<div class="uk-container uk-container-center">
<!-- ================================ Search Form ================================ -->
<a class="uk-navbar-brand uk-hidden-small" href="<?php echo url(''); ?>">
<img alt="<?php echo e(option('SiteTitle')); ?>" src="<?php echo e(asset(option('logo'))); ?>" width="120px" >
</a>
<!-- ================================ Search Form ================================ -->
<script>
function validateForm() {
var x = document.forms["myForm"]["search"].value;
// if (x == null || x == "" || x.length > 30 ) {
// window.location.href = '<?php echo e(url('')); ?>';
// return false;
// }

}
document.addEventListener('DOMContentLoaded', function () {
        const form = document.forms['myForm']; // Get the form element
        const currentUrl = window.location.pathname; // Get the current URL path

        // Check if the current URL contains '/Series'
        if (currentUrl.includes('/Series')) {
            const baseUrl = form.action; // Get the original action URL
            const newAction = `/Series`; // Append 'Series' to the action URL
            form.action = newAction; // Update the form action
        }
    });


</script>
<!-- ================================ Search Form ================================ -->
<form method="GET" id="search-form2" action="<?php echo url('search'); ?>" role="search" name="myForm" onsubmit="return validateForm()" class="uk-search uk-margin-small-top uk-margin-left uk-hidden-small">
<?php echo csrf_field(); ?>

<input class="uk-search-field" placeholder="<?php echo __('main.Search'); ?>"  oninput="debouncedSubmit()" value="<?php echo e(request('search')); ?>" name="search">
<div class="uk-dropdown uk-dropdown-flip uk-dropdown-search" aria-expanded="false"></div>
<script>
  let debounceTimer;

  function debouncedSubmit(num) {
          clearTimeout(debounceTimer); // Clear any existing timer
      debounceTimer = setTimeout(() => {
        if(num==3){
          document.getElementById('search-form3').submit(); // Submit the form after delay
        }else{
          document.getElementById('search-form2').submit(); // Submit the form after delay
        }
      }, 2000); // Delay in milliseconds (1 second)
  }
</script> 
</form>
<div class="uk-navbar-flip uk-hidden-small">
<?php if(auth()->guard()->guest()): ?>  
<div class="uk-button-group">
<a class="uk-button uk-button-link uk-button-large" href="<?php echo route('login'); ?>"><?php echo e(__('main.Sign_In')); ?></a>
<a class="uk-button uk-button-success uk-button-large uk-margin-left" href="<?php echo e(route('register')); ?>">
  <i class="uk-icon-lock uk-margin-small-right"></i> <?php echo e(__('main.Sign_Up')); ?></a>
</div>
<?php else: ?>
<div class="uk-button-group">
<a class="uk-button uk-button-link uk-button-large"><?php echo Auth::user()->name; ?></a>
<a class="uk-button uk-button-success uk-button-large uk-margin-left" href="<?php echo route('logout'); ?>" onclick="event.preventDefault();document.getElementById('logout-form').submit();">
  <i class="uk-icon-lock uk-margin-small-right"></i> <?php echo e(__('main.Sign_Out')); ?></a>
  <form id="logout-form" action="<?php echo e(route('logout')); ?>" method="POST" class="display-zero">
   <?php echo csrf_field(); ?>
  </form>
</div>
<?php endif; ?> 
</div>
<a href="#offcanvas" class="uk-navbar-toggle uk-visible-small uk-icon-medium" data-uk-offcanvas></a>
<div class="uk-navbar-flip uk-visible-small">
<!-- Search Button -->
<a href="#" id="searchToggle" class="uk-navbar-toggle uk-navbar-toggle-alt uk-icon-medium" data-uk-toggle></a>
</div>
<a class="uk-navbar-brand uk-navbar-center uk-visible-small" href="<?php echo url(''); ?>">

    <img alt="<?php echo e(option('SiteTitle')); ?>" src="<?php echo e(asset(option('logo'))); ?>" width="100px" >

</a>
</div>    
</nav>
<!-- Search Input -->
<form method="GET" id="search-form3" action="<?php echo url('search'); ?>" role="search" name="myForm" onsubmit="return validateForm()">
<div class="group" id="searchInput" <?php if(request('search')): ?>
   style="display: flex"
   <?php else: ?>
   style="display: none"
   <?php endif; ?>>
    <?php echo csrf_field(); ?>

  <svg viewBox="0 0 24 24" aria-hidden="true" class="search-icon">
    <g>
      <path
        d="M21.53 20.47l-3.66-3.66C19.195 15.24 20 13.214 20 11c0-4.97-4.03-9-9-9s-9 4.03-9 9 4.03 9 9 9c2.215 0 4.24-.804 5.808-2.13l3.66 3.66c.*************.53.22s.385-.073.53-.22c.295-.293.295-.767.002-1.06zM3.5 11c0-4.135 3.365-7.5 7.5-7.5s7.5 3.365 7.5 7.5-3.365 7.5-7.5 7.5-7.5-3.365-7.5-7.5z"
      ></path>
    </g>
  </svg>

  <input
    id="query"
    class="input"
    type="search"
    placeholder="<?php echo __('main.Search'); ?>"
    oninput="debouncedSubmit(3)" value="<?php echo e(request('search')); ?>" name="search"
  />
  <button onclick="display_search(event)" class="btn_display">
    &ensp;
    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="currentColor" class="bi bi-x-lg" viewBox="0 0 16 16">
      <path d="M2.146 2.854a.5.5 0 1 1 .708-.708L8 7.293l5.146-5.147a.5.5 0 0 1 .708.708L8.707 8l5.147 5.146a.5.5 0 0 1-.708.708L8 8.707l-5.146 5.147a.5.5 0 0 1-.708-.708L7.293 8z"/>
    </svg>
    &ensp;
  </button>
</div>
</form>

<!--********************************************************-->
<!--********************** SITE Content ********************-->
<!--********************************************************--> 
<script>
  document.getElementById("searchToggle").addEventListener("click", function(event) {
    event.preventDefault(); // Prevent default link behavior
    var searchInput = document.getElementById("searchInput");
    
    // Toggle visibility
    if (searchInput.style.display === "none" || searchInput.style.display === "") {
        searchInput.style.display = "flex"; // Show input
        searchInput.focus(); // Automatically focus the input
    } else {
        searchInput.style.display = "none"; // Hide input
    }
});
document.addEventListener("DOMContentLoaded", function () {
        let container = document.getElementById('searchInput');
        if (window.innerWidth >= 760) {
            container.style.display = "none";
        }
    });

function display_search(event){
  event.preventDefault(); // Prevent default link behavior
    var searchInput = document.getElementById("searchInput");
        searchInput.style.display = "none"; // Hide input
}

</script>
<?php echo $__env->yieldContent('content'); ?> 
<!--******************************************************** -->
<!--********************** SITE Content ******************** -->
<!--******************************************************** --> 
<!-- ***************** Fixed "Download App" link *********** -->
<a href="<?php echo option('downloadapp'); ?>" id="download-app" class="download-app"><?php echo __('main.Download_App'); ?></a>


<!-- ===================== start Footer Section  ====================  -->
<footer id="tm-footer" class="uk-block uk-block-secondary uk-block-small" style="padding-top: 0px ;padding-bottom: 0px">
    <div class="uk-container-center uk-container">
  
        <div class="" style="display: flex;justify-content: center"><div class="copyright-text">&copy;
        <span class="uk-text-bold" ><?php echo option('SiteTitle'); ?></span><?php echo __('main.copyright'); ?></div></div>

        </div>
        </footer>
        <!--  start Offcanvas Menu   -->
        <div id="offcanvas" class="uk-offcanvas">
            <div class="uk-offcanvas-bar">
                <div class="uk-panel">
                    <?php if(auth()->guard()->guest()): ?>  
                    <div class="uk-button-group">
                    <a class="uk-button uk-button-link uk-button-large" href="<?php echo route('login'); ?>"><?php echo e(__('main.Sign_In')); ?></a>
                    <a class="uk-button uk-button-success uk-button-large uk-margin-left" href="<?php echo e(route('register')); ?>">
                      <i class="uk-icon-lock uk-margin-small-right"></i> <?php echo e(__('main.Sign_Up')); ?></a>
                    </div>
                    <?php else: ?>
                    <div class="uk-button-group">
                    <a class="uk-button uk-button-link uk-button-large"><?php echo Auth::user()->name; ?></a>
                    <a class="uk-button uk-button-success uk-button-large uk-margin-left" href="<?php echo route('logout'); ?>" onclick="event.preventDefault();document.getElementById('logout-form').submit();">
                      <i class="uk-icon-lock uk-margin-small-right"></i> <?php echo e(__('main.Sign_Out')); ?></a>
                      <form id="logout-form" action="<?php echo e(route('logout')); ?>" method="POST" class="display-zero">
                       <?php echo csrf_field(); ?>
                      </form>
                    </div>
                    <?php endif; ?> 
                </div>
                <ul class="uk-nav uk-nav-offcanvas uk-nav-parent-icon" data-uk-nav>
                    <?php $__currentLoopData = Main_Menus(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $Menu): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li>
                       <a href="<?php echo e(url($Menu->url)); ?>" target="<?php echo e($Menu->target); ?>">
                        <?php echo e($Menu->{'Title_'.$Locale}); ?>

                      </a>
                    </li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php $__currentLoopData = Other_Menus(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $Menu): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li class="" dir="<?php echo e(app()->getLocale() == 'ar' ? 'rtl' : 'ltr'); ?>">
                      <a class="" href="<?php echo e(url($Menu->url)); ?>" target="<?php echo e($Menu->target); ?>">
                        <?php echo e($Menu->{'Title_'.$Locale}); ?>

                      </a>
                    </li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <div class="language-selector" dir="<?php echo e(LaravelLocalization::getCurrentLocale() === 'ar' ? 'rtl' : 'ltr'); ?>">
                      <button class="language-toggle" onclick="toggleLanguageDropdown(2)" >
                        <?php echo __('main.Languages'); ?> &ensp;<svg width="15px"  viewBox="0 0 1024 1024" fill="white" class="icon" version="1.1" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"><path d="M478.312 644.16c24.38 26.901 64.507 26.538 88.507-0.89l270.57-309.222c7.758-8.867 6.86-22.344-2.008-30.103-8.866-7.759-22.344-6.86-30.103 2.007L534.71 615.173c-7.202 8.231-17.541 8.325-24.782 0.335L229.14 305.674c-7.912-8.73-21.403-9.394-30.133-1.482s-9.394 21.403-1.482 30.134l280.786 309.833z" fill=""></path></g></svg>
                    </button>
                    
                    <ul id="language-dropdown-2" class="language-dropdown" style="display: none;">
                        <?php $__currentLoopData = LaravelLocalization::getSupportedLocales(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $localeCode => $properties): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if(in_array($localeCode, ['en', 'ar','fr'])): ?> 
                                <li class="language-option">
                                    <a href="<?php echo e(LaravelLocalization::getLocalizedURL($localeCode, null, [], true)); ?>" 
                                       class="<?php echo e(app()->getLocale() === $localeCode ? 'active-language' : ''); ?>">
                                        <?php echo $properties['native']; ?>

                                    </a>
                                </li>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                  </div>
                  <ul class="uk-nav uk-nav-side uk-nav-parent-icon uk-margin-bottom" data-uk-nav="">
                    <li class="uk-active">
                    <a><?php echo __('main.All_Genres'); ?></a></li>
                    <?php $__currentLoopData = Categories(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $Genre): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                     <li>
                      <a <?php if($Genre->slug == "live-tv" ): ?>
                        href="<?php echo url('live'); ?>" 
                        <?php else: ?>
                        href="<?php echo url('Cat'); ?>/<?php echo $Genre->slug; ?>"
                      <?php endif; ?>>
                        <?php echo $Genre->{'Title_'.$Locale}; ?>

                      </a>
                    </li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <li class="uk-nav-divider"></li>
                </ul>
                
                </ul>
                <div class="uk-panel uk-text-center">
                <ul class="uk-subnav">
                  <li><a href="<?php echo option('Facebook'); ?>"><i class="uk-icon-facebook uk-icon-small"></i></a></li>
                  <li><a href="<?php echo option('Twitter'); ?>"><i class="uk-icon-twitter uk-icon-small"></i></a></li>
                  <li><a href="<?php echo option('Instagram'); ?>"><i class="uk-icon-instagram uk-icon-small"></i></a></li>
                  <li><a href="<?php echo option('youtube'); ?>"><i class="uk-icon-youtube uk-icon-small"></i></a></li>
                  <li><a href="<?php echo option('LinkedIn'); ?>"><i class="uk-icon-snapchat uk-icon-small"></i></a></li>
                  <li><a href="<?php echo option('Tumblr'); ?>"><i class="uk-icon-tumblr uk-icon-small"></i></a></li>
                </ul>
                </div>

               <!-- From Uiverse.io by Mohammad-Rahme-576 --> 
               <script>
                function isAndroidWebView() {
                  return navigator.userAgent.includes("wv") || navigator.userAgent.includes("Android");
                }
              
                document.addEventListener("DOMContentLoaded", function () {
                  if (isAndroidWebView()) {
                    document.getElementById("share-button").style.display = "flex";
                  }
                });
              
                function shareToWhatsApp() {
                  var url = "<?php echo option('downloadapp'); ?>";
                  var whatsappUrl = "https://wa.me/?text=" + encodeURIComponent("Download our app here: " + url);
                  window.location.href = whatsappUrl;
                }
              
                function copyToClipboard() {
    var url = "<?php echo option('downloadapp'); ?>";

    // Create a temporary input element
    var tempInput = document.createElement("input");
    tempInput.value = url;
    document.body.appendChild(tempInput);
    
    // Select and copy the text
    tempInput.select();
    tempInput.setSelectionRange(0, 99999);
    document.execCommand("copy");
    
    // Remove the temporary input
    document.body.removeChild(tempInput);

    // Show a small notification
    alert("Download link copied to clipboard!");
  }
              
                function shareToTelegram() {
                  var url = "<?php echo option('downloadapp'); ?>";
                  var telegramUrl = "https://t.me/share/url?url=" + encodeURIComponent(url) + "&text=" + encodeURIComponent("Download our app here:");
                  window.open(telegramUrl, "_blank");
                }
              </script>
              
              <div id="share-button" style="display: none; justify-content: center;">
                <div class="tooltip-container">
                  <div class="button-content">
                    <span class="text">Share</span>
                    <svg class="share-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                      <path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .***********.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92s2.92-1.31 2.92-2.92c0-1.61-1.31-2.92-2.92-2.92zM18 4c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zM6 13c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1zm12 7.02c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1z"></path>
                    </svg>
                  </div>
                  <div class="tooltip-content">
                    <div class="social-icons">
                      <a href="javascript:void(0);" onclick="shareToWhatsApp()" class="social-icon whatsapp">
                        <i class="uk-icon-whatsapp uk-icon-small"></i>
                      </a>
                        <a href="javascript:void(0);" onclick="copyToClipboard()" class="social-icon copy">
                          <i class="uk-icon-copy uk-icon-small"></i>
                        </a>
                    </div>
                  </div>
                </div>
              </div>
              
</div>


            </div>
        </div>
        <!--    ========================= ./ Offcanvas Menu =========================  -->
        <!--    ========================= Include JS  ===============================  --> 
        <script src="<?php echo asset('assets/js/jquery.js'); ?>"></script>
        <script src="<?php echo asset('assets/js/uikit.min.js'); ?>"></script>
        <script src="<?php echo asset('assets/js/components/grid.min.js'); ?>"></script>
        <!--    ========================= Include JS  ===============================  -->
        <script>
 function toggleLanguageDropdown($num) {
        const dropdowns = document.querySelectorAll('.language-dropdown');
        dropdowns.forEach(dropdown => dropdown.style.display = 'none'); // Close all

        const targetDropdown = document.getElementById('language-dropdown-'+$num);
        const currentDisplay = targetDropdown.style.display;
        if (currentDisplay === 'none' && $num==5) {
            targetDropdown.style = 'grid-template-columns: repeat(3, 1fr); gap: 10px; ';
            targetDropdown.style.display = currentDisplay === 'grid;' ? 'none' : 'grid';
            return true;
        } 
        targetDropdown.style.display = currentDisplay === 'block' ? 'none' : 'block';
    }
    // Optional: Close dropdown when clicking outside
    document.addEventListener('click', (event) => {
        if (!event.target.closest('.language-selector')) {
            document.querySelectorAll('.language-dropdown').forEach(dropdown => dropdown.style.display = 'none');
       
        }
        if (!event.target.closest('.menu-selector')) {
            document.querySelectorAll('.menu-dropdown').forEach(dropdown => dropdown.style.display = 'none');
        }
    });
    if (window.Android) {
       document.addEventListener("DOMContentLoaded", function () {
        var button = document.getElementById("download-app"); // Change to your button ID
        if (button) {
            button.style.display = "none"; // Hide the button
        }
    });
}
        </script>
    </body>
    
    
</html>
<?php /**PATH C:\xampp\htdocs\ffff\resources\views/layouts/main.blade.php ENDPATH**/ ?>