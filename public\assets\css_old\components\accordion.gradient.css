/*! UIkit 2.26.3 | http://www.getuikit.com | (c) 2014 YOOtheme | MIT License */
/* ========================================================================
   Component: Accordion
 ========================================================================== */
/* Sub-object: `uk-accordion-title`
 ========================================================================== */
.uk-accordion-title {
  margin-top: 0;
  margin-bottom: 15px;
  padding: 5px 15px;
  background: #f7f7f7;
  font-size: 18px;
  line-height: 24px;
  cursor: pointer;
  border: 1px solid #dddddd;
  border-radius: 4px;
}
/* Sub-object: `uk-accordion-content`
 ========================================================================== */
.uk-accordion-content {
  padding: 0 15px 15px 15px;
}
/*
 * Micro clearfix to make panels more robust
 */
.uk-accordion-content:before,
.uk-accordion-content:after {
  content: "";
  display: table;
}
.uk-accordion-content:after {
  clear: both;
}
/*
 * Remove margin from the last-child
 */
.uk-accordion-content > :last-child {
  margin-bottom: 0;
}
