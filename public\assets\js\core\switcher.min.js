/*! UIkit 2.26.3 | http://www.getuikit.com | (c) 2014 YOOtheme | MIT License */
!function(i){"use strict";function t(t,n,e){var s,a=i.$.Deferred(),o=t,r=t;return e[0]===n[0]?(a.resolve(),a.promise()):("object"==typeof t&&(o=t[0],r=t[1]||t[0]),i.$body.css("overflow-x","hidden"),s=function(){n&&n.hide().removeClass("uk-active "+r+" uk-animation-reverse"),e.addClass(o).one(i.support.animation.end,function(){e.removeClass(""+o).css({opacity:"",display:""}),a.resolve(),i.$body.css("overflow-x",""),n&&n.css({opacity:"",display:""})}.bind(this)).show()},e.css("animation-duration",this.options.duration+"ms"),n&&n.length?(n.css("animation-duration",this.options.duration+"ms"),n.css("display","none").addClass(r+" uk-animation-reverse").one(i.support.animation.end,function(){s()}.bind(this)).css("display","")):(e.addClass("uk-active"),s()),a.promise())}var n;i.component("switcher",{defaults:{connect:!1,toggle:">*",active:0,animation:!1,duration:200,swiping:!0},animating:!1,boot:function(){i.ready(function(t){i.$("[data-uk-switcher]",t).each(function(){var t=i.$(this);if(!t.data("switcher")){i.switcher(t,i.Utils.options(t.attr("data-uk-switcher")))}})})},init:function(){var t=this;if(this.on("click.uk.switcher",this.options.toggle,function(i){i.preventDefault(),t.show(this)}),this.options.connect){this.connect=i.$(this.options.connect),this.connect.children().removeClass("uk-active"),this.connect.length&&(this.connect.children().attr("aria-hidden","true"),this.connect.on("click","[data-uk-switcher-item]",function(n){n.preventDefault();var e=i.$(this).attr("data-uk-switcher-item");if(t.index!=e)switch(e){case"next":case"previous":t.show(t.index+("next"==e?1:-1));break;default:t.show(parseInt(e,10))}}),this.options.swiping&&this.connect.on("swipeRight swipeLeft",function(i){i.preventDefault(),window.getSelection().toString()||t.show(t.index+("swipeLeft"==i.type?1:-1))}));var n=this.find(this.options.toggle),e=n.filter(".uk-active");if(e.length)this.show(e,!1);else{if(this.options.active===!1)return;e=n.eq(this.options.active),this.show(e.length?e:n.eq(0),!1)}n.not(e).attr("aria-expanded","false"),e.attr("aria-expanded","true")}},show:function(e,s){if(!this.animating){if(isNaN(e))e=i.$(e);else{var a=this.find(this.options.toggle);e=0>e?a.length-1:e,e=a.eq(a[e]?e:0)}var o=this,a=this.find(this.options.toggle),r=i.$(e),c=n[this.options.animation]||function(i,e){if(!o.options.animation)return n.none.apply(o);var s=o.options.animation.split(",");return 1==s.length&&(s[1]=s[0]),s[0]=s[0].trim(),s[1]=s[1].trim(),t.apply(o,[s,i,e])};s!==!1&&i.support.animation||(c=n.none),r.hasClass("uk-disabled")||(a.attr("aria-expanded","false"),r.attr("aria-expanded","true"),a.filter(".uk-active").removeClass("uk-active"),r.addClass("uk-active"),this.options.connect&&this.connect.length&&(this.index=this.find(this.options.toggle).index(r),-1==this.index&&(this.index=0),this.connect.each(function(){var t=i.$(this),n=i.$(t.children()),e=i.$(n.filter(".uk-active")),s=i.$(n.eq(o.index));o.animating=!0,c.apply(o,[e,s]).then(function(){e.removeClass("uk-active"),s.addClass("uk-active"),e.attr("aria-hidden","true"),s.attr("aria-hidden","false"),i.Utils.checkDisplay(s,!0),o.animating=!1})})),this.trigger("show.uk.switcher",[r]))}}}),n={none:function(){var t=i.$.Deferred();return t.resolve(),t.promise()},fade:function(i,n){return t.apply(this,["uk-animation-fade",i,n])},"slide-bottom":function(i,n){return t.apply(this,["uk-animation-slide-bottom",i,n])},"slide-top":function(i,n){return t.apply(this,["uk-animation-slide-top",i,n])},"slide-vertical":function(i,n){var e=["uk-animation-slide-top","uk-animation-slide-bottom"];return i&&i.index()>n.index()&&e.reverse(),t.apply(this,[e,i,n])},"slide-left":function(i,n){return t.apply(this,["uk-animation-slide-left",i,n])},"slide-right":function(i,n){return t.apply(this,["uk-animation-slide-right",i,n])},"slide-horizontal":function(i,n){var e=["uk-animation-slide-right","uk-animation-slide-left"];return i&&i.index()>n.index()&&e.reverse(),t.apply(this,[e,i,n])},scale:function(i,n){return t.apply(this,["uk-animation-scale-up",i,n])}},i.switcher.animations=n}(UIkit);