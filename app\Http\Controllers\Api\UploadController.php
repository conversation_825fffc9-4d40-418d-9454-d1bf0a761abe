<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Http;
class UploadController extends Controller
{
    public function uploadApk(Request $request)
    {
        if (!$request->hasFile('apk_file')) {
            return response()->json(['error' => '❌ لم يتم رفع الملف'], 400);
        }

        $file = $request->file('apk_file');
        if (strtolower($file->getClientOriginalExtension()) !== 'apk') {
            return response()->json(['error' => '❌ الملف يجب أن يكون بصيغة APK'], 422);
        }

        $filename = uniqid('flicksy_', true) . '.apk';
        $destinationPath = public_path('storage/apks');

        if (!file_exists($destinationPath)) {
            mkdir($destinationPath, 0775, true);
        }

        $file->move($destinationPath, $filename);

        return response()->json([
            'success' => true,
            'url' => url('storage/apks/' . $filename),
            'filename' => $filename
        ]);
    }

    public function uploadVideo(Request $request)
    {
        if (!$request->hasFile('video_file')) {
            return response()->json(['error' => '❌ لم يتم اختيار الفيديو'], 400);
        }

        $file = $request->file('video_file');
        if (strtolower($file->getClientOriginalExtension()) !== 'mp4') {
            return response()->json(['error' => '❌ الملف يجب أن يكون MP4'], 422);
        }

        $filename = uniqid('video_', true) . '.mp4';
        $destinationPath = public_path('storage/videos');

        if (!file_exists($destinationPath)) {
            mkdir($destinationPath, 0775, true);
        }

        $file->move($destinationPath, $filename);

        return response()->json([
            'success' => true,
            'url' => url('storage/videos/' . $filename),
            'filename' => $filename
        ]);
    }

   public function uploadM3U(Request $request)
{
    $filename = uniqid('playlist_', true) . '.m3u';
    $destinationPath = public_path('storage/m3u');

    if (!file_exists($destinationPath)) {
        mkdir($destinationPath, 0775, true);
    }

    try {
        // إذا تم رفع الملف عبر input
        if ($request->hasFile('m3u_file') && $request->file('m3u_file')->isValid()) {
            $file = $request->file('m3u_file');
            $ext = strtolower($file->getClientOriginalExtension());

            if (!in_array($ext, ['m3u', 'm3u8'])) {
                return response()->json(['error' => '❌ الملف يجب أن يكون بصيغة M3U أو M3U8'], 422);
            }

            $filename = uniqid('playlist_', true) . '.' . $ext;
            $file->move($destinationPath, $filename);
        } else {
            // إذا الملف جاء من fetch blob
            $rawContent = $request->getContent();
            if (!$rawContent) {
                return response()->json(['error' => '❌ فشل في قراءة محتوى الملف'], 400);
            }

            file_put_contents($destinationPath . '/' . $filename, $rawContent);
        }

        return response()->json([
            'success' => true,
            'url' => url('storage/m3u/' . $filename),
            'filename' => $filename
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'error' => '❌ حدث خطأ أثناء رفع الملف',
            'details' => $e->getMessage()
        ], 500);
    }
}







 // ✅ الدالة الجديدة لمعالجة بيانات Xtream
    public function uploadXtream(Request $request)
{
    $request->validate([
        'host' => 'required|url',
        'port' => 'required|numeric',
        'username' => 'required|string',
        'password' => 'required|string',
    ]);

    $xtreamUrl = rtrim($request->host, '/') . ':' . $request->port .
        "/get.php?username={$request->username}&password={$request->password}&type=m3u&output=ts";

    // جلب ملف M3U من Xtream
    try {
        $response = Http::timeout(15)->get($xtreamUrl);
        if (!$response->ok()) {
            return response()->json(['error' => '❌ فشل في تحميل ملف M3U من Xtream'], 500);
        }
    } catch (\Exception $e) {
        return response()->json(['error' => '❌ خطأ في الاتصال بـ Xtream API', 'details' => $e->getMessage()], 500);
    }

    // إنشاء مجلد التخزين
    $directory = public_path('storage/xtream');
    if (!file_exists($directory)) {
        mkdir($directory, 0775, true);
    }

    // حفظ ملف M3U
    $filename = 'xtream_' . date('Ymd_His') . '.m3u';
    $filePath = $directory . '/' . $filename;

    file_put_contents($filePath, $response->body());

    return response()->json([
        'success' => true,
        'message' => '✅ تم حفظ ملف M3U من Xtream بنجاح',
        'download_url' => url('storage/xtream/' . $filename),
        'xtream_source_url' => $xtreamUrl
    ]);
}





public function uploadTs(Request $request)
{
    $filename = uniqid('segment_', true) . '.ts';
    $destinationPath = public_path('storage/ts');

    if (!file_exists($destinationPath)) {
        mkdir($destinationPath, 0775, true);
    }

    try {
        // إذا تم الرفع من input
        if ($request->hasFile('ts_file') && $request->file('ts_file')->isValid()) {
            $request->file('ts_file')->move($destinationPath, $filename);
        } else {
            // رفع blob من رابط
            $raw = $request->getContent();
            if (!$raw) {
                return response()->json(['error' => '❌ لم يتم استلام بيانات blob'], 400);
            }
            file_put_contents($destinationPath . '/' . $filename, $raw);
        }

        return response()->json([
            'success' => true,
            'url' => url('storage/ts/' . $filename),
            'filename' => $filename
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'error' => '❌ فشل في حفظ الملف',
            'details' => $e->getMessage()
        ], 500);
    }
}












}