/*! UIkit 2.26.3 | http://www.getuikit.com | (c) 2014 YOOtheme | MIT License */
.uk-sortable{position:relative}.uk-sortable>*{touch-action:none}.uk-sortable a,.uk-sortable img{-webkit-touch-callout:none}.uk-sortable>:last-child{margin-bottom:0}.uk-sortable-dragged{position:absolute;z-index:1050;pointer-events:none}.uk-sortable-placeholder{opacity:0}.uk-sortable-empty{min-height:30px}.uk-sortable-handle{touch-action:none}.uk-sortable-handle:hover{cursor:move}.uk-sortable-moving,.uk-sortable-moving *{cursor:move}