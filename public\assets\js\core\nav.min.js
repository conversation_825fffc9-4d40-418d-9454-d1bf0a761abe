/*! UIkit 2.26.3 | http://www.getuikit.com | (c) 2014 YOOtheme | MIT License */
!function(i){"use strict";function t(t){var s=i.$(t),a="auto";if(s.is(":visible"))a=s.outerHeight();else{var e={position:s.css("position"),visibility:s.css("visibility"),display:s.css("display")};a=s.css({position:"absolute",visibility:"hidden",display:"block"}).outerHeight(),s.css(e)}return a}i.component("nav",{defaults:{toggle:">li.uk-parent > a[href='#']",lists:">li.uk-parent > ul",multiple:!1},boot:function(){i.ready(function(t){i.$("[data-uk-nav]",t).each(function(){var t=i.$(this);if(!t.data("nav")){i.nav(t,i.Utils.options(t.attr("data-uk-nav")))}})})},init:function(){var t=this;this.on("click.uk.nav",this.options.toggle,function(s){s.preventDefault();var a=i.$(this);t.open(a.parent()[0]==t.element[0]?a:a.parent("li"))}),this.find(this.options.lists).each(function(){var s=i.$(this),a=s.parent(),e=a.hasClass("uk-active");s.wrap('<div style="overflow:hidden;height:0;position:relative;"></div>'),a.data("list-container",s.parent()[e?"removeClass":"addClass"]("uk-hidden")),a.attr("aria-expanded",a.hasClass("uk-open")),e&&t.open(a,!0)})},open:function(s,a){var e=this,n=this.element,o=i.$(s),l=o.data("list-container");this.options.multiple||n.children(".uk-open").not(s).each(function(){var t=i.$(this);t.data("list-container")&&t.data("list-container").stop().animate({height:0},function(){i.$(this).parent().removeClass("uk-open").end().addClass("uk-hidden")})}),o.toggleClass("uk-open"),o.attr("aria-expanded",o.hasClass("uk-open")),l&&(o.hasClass("uk-open")&&l.removeClass("uk-hidden"),a?(l.stop().height(o.hasClass("uk-open")?"auto":0),o.hasClass("uk-open")||l.addClass("uk-hidden"),this.trigger("display.uk.check")):l.stop().animate({height:o.hasClass("uk-open")?t(l.find("ul:first")):0},function(){o.hasClass("uk-open")?l.css("height",""):l.addClass("uk-hidden"),e.trigger("display.uk.check")}))}})}(UIkit);