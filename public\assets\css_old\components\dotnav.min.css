/*! UIkit 2.26.3 | http://www.getuikit.com | (c) 2014 YOOtheme | MIT License */
.uk-dotnav{display:-ms-flexbox;display:-webkit-flex;display:flex;-ms-flex-wrap:wrap;-webkit-flex-wrap:wrap;flex-wrap:wrap;margin-left:-15px;margin-top:-15px;padding:0;list-style:none}.uk-dotnav>*{-ms-flex:none;-webkit-flex:none;flex:none;padding-left:15px;margin-top:15px}.uk-dotnav:after,.uk-dotnav:before{content:"";display:block;overflow:hidden}.uk-dotnav:after{clear:both}.uk-dotnav>*{float:left}.uk-dotnav>*>*{display:block;box-sizing:content-box;width:20px;height:20px;border-radius:50%;background:rgba(50,50,50,.1);text-indent:100%;overflow:hidden;white-space:nowrap}.uk-dotnav>*>:focus,.uk-dotnav>*>:hover{background:rgba(50,50,50,.4);outline:0}.uk-dotnav>*>:active{background:rgba(50,50,50,.6)}.uk-dotnav>.uk-active>*{background:rgba(50,50,50,.4)}.uk-dotnav-contrast>*>*{background:rgba(255,255,255,.4)}.uk-dotnav-contrast>*>:focus,.uk-dotnav-contrast>*>:hover{background:rgba(255,255,255,.7)}.uk-dotnav-contrast>*>:active{background:rgba(255,255,255,.9)}.uk-dotnav-contrast>.uk-active>*{background:rgba(255,255,255,.9)}.uk-dotnav-vertical{-ms-flex-direction:column;-webkit-flex-direction:column;flex-direction:column}.uk-dotnav-vertical>*{float:none}