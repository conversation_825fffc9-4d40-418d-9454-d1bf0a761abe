/*! UIkit 2.26.3 | http://www.getuikit.com | (c) 2014 YOOtheme | MIT License */
!function(a){"use strict";var t={x:window.scrollX,y:window.scrollY},n=(a.$win,a.$doc,a.$html),i={show:function(i){if(i=a.$(i),i.length){var o=a.$("body"),s=i.find(".uk-offcanvas-bar:first"),e="right"==a.langdirection,f=s.hasClass("uk-offcanvas-bar-flip")?-1:1,r=f*(e?-1:1),c=window.innerWidth-o.width();t={x:window.pageXOffset,y:window.pageYOffset},i.addClass("uk-active"),o.css({width:window.innerWidth-c,height:window.innerHeight}).addClass("uk-offcanvas-page"),o.css(e?"margin-right":"margin-left",(e?-1:1)*s.outerWidth()*r).width(),n.css("margin-top",-1*t.y),s.addClass("uk-offcanvas-bar-show"),this._initElement(i),s.trigger("show.uk.offcanvas",[i,s]),i.attr("aria-hidden","false")}},hide:function(i){var o=a.$("body"),s=a.$(".uk-offcanvas.uk-active"),e="right"==a.langdirection,f=s.find(".uk-offcanvas-bar:first"),r=function(){o.removeClass("uk-offcanvas-page").css({width:"",height:"","margin-left":"","margin-right":""}),s.removeClass("uk-active"),f.removeClass("uk-offcanvas-bar-show"),n.css("margin-top",""),window.scrollTo(t.x,t.y),f.trigger("hide.uk.offcanvas",[s,f]),s.attr("aria-hidden","true")};s.length&&(a.support.transition&&!i?(o.one(a.support.transition.end,function(){r()}).css(e?"margin-right":"margin-left",""),setTimeout(function(){f.removeClass("uk-offcanvas-bar-show")},0)):r())},_initElement:function(t){t.data("OffcanvasInit")||(t.on("click.uk.offcanvas swipeRight.uk.offcanvas swipeLeft.uk.offcanvas",function(t){var n=a.$(t.target);if(!t.type.match(/swipe/)&&!n.hasClass("uk-offcanvas-close")){if(n.hasClass("uk-offcanvas-bar"))return;if(n.parents(".uk-offcanvas-bar:first").length)return}t.stopImmediatePropagation(),i.hide()}),t.on("click","a[href*='#']",function(){var t=a.$(this),n=t.attr("href");"#"!=n&&(a.$doc.one("hide.uk.offcanvas",function(){var i;try{i=a.$(t[0].hash)}catch(o){i=""}i.length||(i=a.$('[name="'+t[0].hash.replace("#","")+'"]')),i.length&&a.Utils.scrollToElement?a.Utils.scrollToElement(i,a.Utils.options(t.attr("data-uk-smooth-scroll")||"{}")):window.location.href=n}),i.hide())}),t.data("OffcanvasInit",!0))}};a.component("offcanvasTrigger",{boot:function(){n.on("click.offcanvas.uikit","[data-uk-offcanvas]",function(t){t.preventDefault();var n=a.$(this);if(!n.data("offcanvasTrigger")){{a.offcanvasTrigger(n,a.Utils.options(n.attr("data-uk-offcanvas")))}n.trigger("click")}}),n.on("keydown.uk.offcanvas",function(a){27===a.keyCode&&i.hide()})},init:function(){var t=this;this.options=a.$.extend({target:t.element.is("a")?t.element.attr("href"):!1},this.options),this.on("click",function(a){a.preventDefault(),i.show(t.options.target)})}}),a.offcanvas=i}(UIkit);