/*! UIkit 2.26.3 | http://www.getuikit.com | (c) 2014 YOOtheme | MIT License */
/* ========================================================================
   Component: HTML editor
 ========================================================================== */
/* Sub-object `uk-htmleditor-navbar`
 ========================================================================== */
.uk-htmleditor-navbar {
  background: #f7f7f7;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-bottom-color: rgba(0, 0, 0, 0.2);
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  background-origin: border-box;
  background-image: -webkit-linear-gradient(top, #ffffff, #eeeeee);
  background-image: linear-gradient(to bottom, #ffffff, #eeeeee);
}
/*
 * Micro clearfix
 */
.uk-htmleditor-navbar:before,
.uk-htmleditor-navbar:after {
  content: "";
  display: table;
}
.uk-htmleditor-navbar:after {
  clear: both;
}
/* Sub-object `uk-htmleditor-navbar-nav`
 ========================================================================== */
.uk-htmleditor-navbar-nav {
  margin: 0;
  padding: 0;
  list-style: none;
  float: left;
}
.uk-htmleditor-navbar-nav > li {
  float: left;
}
/*
 * 1. Dimensions
 * 2. Style
 */
.uk-htmleditor-navbar-nav > li > a {
  display: block;
  box-sizing: border-box;
  text-decoration: none;
  /* 1 */
  height: 41px;
  padding: 0 15px;
  line-height: 40px;
  /* 2 */
  color: #444444;
  font-size: 11px;
  cursor: pointer;
  margin-top: -1px;
  margin-left: -1px;
  border: 1px solid transparent;
  border-bottom-width: 0;
  text-shadow: 0 1px 0 #ffffff;
}
/*
 * Hover
 * 1. Apply hover style also to focus state
 * 2. Remove default focus style
 */
.uk-htmleditor-navbar-nav > li:hover > a,
.uk-htmleditor-navbar-nav > li > a:focus {
  background-color: transparent;
  color: #444444;
  outline: none;
  /* 2 */
  position: relative;
  z-index: 1;
  border-left-color: rgba(0, 0, 0, 0.1);
  border-right-color: rgba(0, 0, 0, 0.1);
  border-top-color: rgba(0, 0, 0, 0.1);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}
/* OnClick */
.uk-htmleditor-navbar-nav > li > a:active {
  background-color: #f5f5f5;
  color: #444444;
  border-left-color: rgba(0, 0, 0, 0.1);
  border-right-color: rgba(0, 0, 0, 0.1);
  border-top-color: rgba(0, 0, 0, 0.2);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}
/* Active */
.uk-htmleditor-navbar-nav > li.uk-active > a {
  background-color: #fafafa;
  color: #444444;
  border-left-color: rgba(0, 0, 0, 0.1);
  border-right-color: rgba(0, 0, 0, 0.1);
  border-top-color: rgba(0, 0, 0, 0.2);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}
/* Sub-object: `uk-htmleditor-navbar-flip`
 ========================================================================== */
.uk-htmleditor-navbar-flip {
  float: right;
}
/* Sub-object for special buttons
 ========================================================================== */
[data-mode='split'] .uk-htmleditor-button-code,
[data-mode='split'] .uk-htmleditor-button-preview {
  display: none;
}
/* Sub-object `uk-htmleditor-content`
 ========================================================================== */
.uk-htmleditor-content {
  border-left: 1px solid #dddddd;
  border-right: 1px solid #dddddd;
  border-bottom: 1px solid #dddddd;
  background: #ffffff;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}
/*
 * Micro clearfix
 */
.uk-htmleditor-content:before,
.uk-htmleditor-content:after {
  content: "";
  display: table;
}
.uk-htmleditor-content:after {
  clear: both;
}
/* Modifier `uk-htmleditor-fullscreen`
 ========================================================================== */
.uk-htmleditor-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 990;
}
.uk-htmleditor-fullscreen .uk-htmleditor-content {
  position: absolute;
  top: 41px;
  left: 0;
  right: 0;
  bottom: 0;
}
.uk-htmleditor-fullscreen .uk-icon-expand:before {
  content: "\f066";
}
/* Sub-objects `uk-htmleditor-code` and `uk-htmleditor-preview`
 ========================================================================== */
.uk-htmleditor-code,
.uk-htmleditor-preview {
  box-sizing: border-box;
}
.uk-htmleditor-preview {
  padding: 20px;
  overflow-y: scroll;
  position: relative;
}
/*
 * Tab view
 */
[data-mode='tab'][data-active-tab='code'] .uk-htmleditor-preview,
[data-mode='tab'][data-active-tab='preview'] .uk-htmleditor-code {
  display: none;
}
/*
 * Split view
 */
[data-mode='split'] .uk-htmleditor-code,
[data-mode='split'] .uk-htmleditor-preview {
  float: left;
  width: 50%;
}
[data-mode='split'] .uk-htmleditor-code {
  border-right: 1px solid #eeeeee;
}
/* Sub-object `uk-htmleditor-iframe`
 ========================================================================== */
.uk-htmleditor-iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
/* CodeMirror modifications
 ========================================================================== */
.uk-htmleditor .CodeMirror {
  padding: 10px;
  box-sizing: border-box;
}
/*
     * Apply same `border-radius` as `uk-htmleditor-navbar`
     */
.uk-htmleditor-navbar-nav:first-child > li:first-child > a {
  border-top-left-radius: 4px;
}
/*
     * Sub-modifier `uk-htmleditor-navbar-flip`
     */
/* Collapse border */
.uk-htmleditor-navbar-flip .uk-htmleditor-navbar-nav > li > a {
  margin-left: 0;
  margin-right: -1px;
}
/* Apply same `border-radius` as `uk-htmleditor-navbar` */
.uk-htmleditor-navbar-flip .uk-htmleditor-navbar-nav:first-child > li:first-child > a {
  border-top-left-radius: 0;
}
.uk-htmleditor-navbar-flip .uk-htmleditor-navbar-nav:last-child > li:last-child > a {
  border-top-right-radius: 4px;
}
/*
     * Sub-modifier `uk-htmleditor-fullscreen`
     */
.uk-htmleditor-fullscreen .uk-htmleditor-navbar {
  border-top: none;
  border-left: none;
  border-right: none;
  border-radius: 0;
}
.uk-htmleditor-fullscreen .uk-htmleditor-content {
  border: none;
  border-radius: 0;
}
.uk-htmleditor-fullscreen .uk-htmleditor-navbar-nav > li > a {
  border-radius: 0 !important;
}
