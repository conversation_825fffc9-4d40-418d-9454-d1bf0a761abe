<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class UploadController extends Controller
{
    public function uploadApk(Request $request)
    {
        if (!$request->hasFile('apk_file')) {
            return response()->json(['error' => '❌ لم يتم رفع الملف'], 400);
        }

        $file = $request->file('apk_file');
        if (strtolower($file->getClientOriginalExtension()) !== 'apk') {
            return response()->json(['error' => '❌ الملف يجب أن يكون بصيغة APK'], 422);
        }

        $filename = uniqid('flicksy_', true) . '.apk';
        $destinationPath = public_path('storage/apks');

        if (!file_exists($destinationPath)) {
            mkdir($destinationPath, 0775, true);
        }

        $file->move($destinationPath, $filename);

        return response()->json([
            'success' => true,
            'url' => url('storage/apks/' . $filename),
            'filename' => $filename
        ]);
    }

    public function uploadVideo(Request $request)
    {
        if (!$request->hasFile('video_file')) {
            return response()->json(['error' => '❌ لم يتم اختيار الفيديو'], 400);
        }

        $file = $request->file('video_file');
        if (strtolower($file->getClientOriginalExtension()) !== 'mp4') {
            return response()->json(['error' => '❌ الملف يجب أن يكون MP4'], 422);
        }

        $filename = uniqid('video_', true) . '.mp4';
        $destinationPath = public_path('storage/videos');

        if (!file_exists($destinationPath)) {
            mkdir($destinationPath, 0775, true);
        }

        $file->move($destinationPath, $filename);

        return response()->json([
            'success' => true,
            'url' => url('storage/videos/' . $filename),
            'filename' => $filename
        ]);
    }

    public function uploadM3U(Request $request)
    {
        if (!$request->hasFile('m3u_file')) {
            return response()->json(['error' => '❌ لم يتم رفع الملف'], 400);
        }

        $file = $request->file('m3u_file');
        $ext = strtolower($file->getClientOriginalExtension());
        if (!in_array($ext, ['m3u', 'm3u8'])) {
            return response()->json(['error' => '❌ الملف يجب أن يكون بصيغة M3U أو M3U8'], 422);
        }

        $filename = uniqid('playlist_', true) . '.' . $ext;
        $destinationPath = public_path('storage/m3u');

        if (!file_exists($destinationPath)) {
            mkdir($destinationPath, 0775, true);
        }

        $file->move($destinationPath, $filename);

        return response()->json([
            'success' => true,
            'url' => url('storage/m3u/' . $filename),
            'filename' => $filename
        ]);
    }
}
