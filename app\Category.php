<?php

namespace App;
use Illuminate\Database\Eloquent\Model;
use Cviebrock\EloquentSluggable\Sluggable;
use App\ImageUpload;
use App\Post;
use App\User;

class Category extends Model
{

     use Sluggable;
     /**
     * Return the sluggable configuration array for this model.
     *
     * @return array
     */
    public function sluggable(): array
    {
        return [
            'slug' => [
                'source' => 'Title_en'
            ]
        ];
    }
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'parent_id', 'order', 'Title_ar','Title_en','Title_fr','slug','color','ImageUpload_id'
    ];

    // THIS function Posts
    public function Posts()
    {
        return $this->hasMany(Post::class);
    }
    public function movies()
    {
    return $this->belongsToMany(Post::class, 'category_movie', 'category_id', 'movie_id');
    }
    public function series()
    {
    return $this->belongsToMany(Post::class, 'category_serie', 'category_id', 'serie_id');
    }

    // THIS function ImageUpload
    public function ImageUpload()
    {
        return $this->belongsTo(ImageUpload::class,'ImageUpload_id');
    }
}
