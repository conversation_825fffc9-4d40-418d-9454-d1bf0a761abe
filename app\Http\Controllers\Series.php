<?php

namespace App\Http\Controllers;
use Illuminate\Http\Request;
use App\Post;
use App\User;
use App\Category;
use App\Client;
use App\Serie;
use Auth;

class Series extends Controller
{
    /** 
     * 
     *
     * Movie Downloud Size.
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response.
     * 
     * 
     */
public function index()
{
    $search = \Request::get('search'); // Retrieve the search term.

    if ($search != "") {
        // Fetch series with seasons, filter by search, and paginate.
        $Series = Serie::whereHas('seasons') // Ensure series have at least one season.
            ->where(function ($query) use ($search) {
                $query->where('Title_en', 'LIKE', '%' . $search . '%')
                      ->orWhere('Title_ar', 'LIKE', '%' . $search . '%');
            })
            ->where('featured', '=', 'on')
            ->simplePaginate(12);

        if (count($Series) > 0) {
            return view('Pages.Series.index', compact('Series'));
        } else {
            return redirect()->back()->with('message', 'No series found with the given search term.');
        }
    }

    // Fetch all series with seasons and paginate.
    $Series = Serie::whereHas('seasons') // Ensure series have at least one season.
        ->where('featured', 'on')
        ->oldest()
        ->simplePaginate(12);

    return view('Pages.Series.index', compact('Series'));
}


    /**
     * 
     * Display the specified Resource.
     *
     * @param  int  $slug
     * @return \Illuminate\Http\Response
    **/
    public function show($slug)
    {
        $Serie = Serie::where('slug', '=', $slug)
                     ->where("featured", "=", "on")
                     ->firstOrFail();
        /// Outher Series 
        $outher_series = Serie::where('id', '!=', $Serie->id) // Exclude the current series
                     ->where("featured", "on") // Only featured series
                     ->whereHas('genres', function ($query) use ($Serie) {
                         $query->whereIn('category_serie.category_id', $Serie->genres->pluck('id')); // Use correct table name
                     })
                     ->whereHas('Seasons') // Ensure it has at least one season
                     ->simplePaginate(20);
        // Handle different views based on category
            return view('Pages.Series.show_details', compact('Serie',"outher_series"));
    }
    
}
