<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Cviebrock\EloquentSluggable\Sluggable;
use App\ImageUpload;

class Season extends Model
{

    use Sluggable;
    protected $table="seasons";
    /**
     * 
     * 
     * Return the sluggable configuration array for this Model.
     *
     * @return array
     * Google Drive.
     * 
     */
    public function sluggable(): array
    {
        return [
            'slug' => [
                'source' => 'Title_en'
            ]
        ];
    }
    /**
     * The attributes that are mass assignable.
     * Google Drive.
     * @var array.
     */
    protected $fillable = [
    'id_serie',"position",
    'Title_ar','Title_fr','Title_en',
    'body_fr','body_en','body_ar','ImageUpload_id',
    'slug','featured',"year"
    ];

    public function ImageUpload()
    {
        return $this->belongsTo(ImageUpload::class,'ImageUpload_id');
    }

    public function Serie()
    {
        return $this->belongsTo(Serie::class, 'id_serie');
    }
    public function Episodes(){
        return $this->hasMany(Post::class,'id_season');
    }
}
