<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Post;

class UpdatePostsSliderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Update existing featured posts to have slider values
        try {
            // Set some featured posts as slider type 1 (latest sliders)
            $featuredPosts = Post::where('featured', 1)->take(10)->get();
            
            foreach ($featuredPosts as $index => $post) {
                if ($index < 5) {
                    $post->slider = 1; // Latest sliders
                } else {
                    $post->slider = 2; // Oldest sliders
                }
                $post->save();
            }
            
            $this->command->info('✅ Successfully updated posts with slider values!');
            $this->command->info('- 5 posts set as slider type 1 (latest)');
            $this->command->info('- 5 posts set as slider type 2 (oldest)');
            
        } catch (\Exception $e) {
            $this->command->error('❌ Error updating posts: ' . $e->getMessage());
        }
    }
}
