<?php

namespace App\Http\Controllers\Dashboard;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Requests\StorePost;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\ImageUpload;
use App\User; 
use App\Client; 
use App\Post;  
use App\Category;
use App\Instagram;
use App\Audio;
use Auth;
use File;
use Validator;

class Posts extends Controller
{  

    /**
     * 
     * Show the middleware dashboard Super Admin.
     *
     * @return \Illuminate\Contracts\Support\Renderable.
     * 
     */
    public function __construct()
    {
        $this->middleware(['auth','role_or_permission:Super-Admin|Supervisors']);
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        // Build the query
        $query = Post::query()->latest()->whereNull("id_season")->with('Category', 'Client','genres');
    
        // Apply search filter
        if ($request->has('search') && $request->input('search') != '') {
            $searchTerm = $request->input('search');
            $query->where('Title_en', 'LIKE', "%{$searchTerm}%")
                  ->orWhereHas('genres', function ($q) use ($searchTerm) {
                      $q->where('Title_en', 'LIKE', "%{$searchTerm}%");
                  });
        }
    
        // Paginate the filtered results
        $Posts = $query->simplePaginate(10);
    
        return view('Dashboard.Posts.index', compact('Posts'));
    }
    


    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        // GET Artists
        $Artists = Client::all();
        // GET Categores
        $Categores = Category::all();
         // GET Albums
        $Albums = Instagram::all();
        return view('Dashboard.Posts.create',compact('Artists','Categores','Albums'));
    }
 
    /**
     * 
     * Store a newly Created Resource in Storage.
     *
     * @param  \Illuminate\Http\Request  $request.
     * @return \Illuminate\Http\Response
     * 
     */

    public function store(Request $request)
    {
        // GET validate
      $validated=  $request->validate([
        'author_id' => 'required',
        'Title_en' => 'required',
        'body_en' => 'required',
        'category_ids' => 'required|array', // Multiple category IDs
        'category_ids.*' => 'exists:categories,id', // Ensure genres exist
        ]);

        $ImageUpload = ImageUpload::max('id');
        $AudioUpload = Audio::max('id');

        $post= Post::create([
            'author_id' => $request->author_id,  
            // 'category_id' => $request->category_id,   
            'Instagram_id' => $request->Instagram_id,
            'Title_ar' => $request->Title_ar,  
            'Title_en' => $request->Title_en, 
            'Title_fr' => $request->Title_fr,
            'body_ar' => $request->body_ar,  
            'body_en' => $request->body_en,
            'body_fr' => $request->body_fr, 
            'Downloud' => $request->Downloud,
            'link' => $request->link,
            'link_2' => $request->link_2,
            'link_3' => $request->link_3,
            'link_4' => $request->link_4,
            'slider' => $request->slider,
            'year' => $request->year,
            'googledrive' => $request->googledrive,
            'audio_id' => $AudioUpload,
            'featured' => $request->featured == null ? "of": "on"  ,
            'ImageUpload_id' => $ImageUpload
        ]);
            // Attach genres
        $post->genres()->sync($validated['category_ids']);

            return redirect()->route('Posts.index')->with('success','Movie Store successfully.');
    }

    /**
     * Show the form for editing the specified Resource.
     *
     * @param  int  $slug
     * @return \Illuminate\Http\Response
     */
    public function edit($slug)
    {
        //To Get All Post 
        $Post = Post::where('slug', '=', $slug)->firstOrFail();
         // GET Artists
        $Artists = Client::all();
        // GET Categores
        $Categores = Category::all();
         // GET Albums
        $Albums = Instagram::all();
        // ImageUpload
        $ImageUpload = ImageUpload::max('id') + 1;
        // AudioUpload
        $AudioUpload = Audio::max('id') + 1;
        return view('Dashboard.Posts.edit',compact('Post','Artists','Categores','ImageUpload','Albums','AudioUpload'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response 
     */
    public function update(Request $request, $slug)
    {
        // GET Post.
        $Post = Post::where('slug', '=', $slug)->firstOrFail();
        
        // GET validate.
        $data = $request->validate([
        'author_id' => 'required',
        // 'category_id' => 'required',
        'Title_en' => 'required',
        'body_en' => 'required',
        'category_ids' => 'required|array', // Multiple category IDs
        'category_ids.*' => 'exists:categories,id', // Ensure genres exist
        ]);
        
        $Post->author_id = $request->input('author_id');
        // $Post->category_id = $request->input('category_id');
        $Post->Instagram_id = $request->input('Instagram_id');
        $Post->ImageUpload_id = $request->input('ImageUpload_id');
        $Post->audio_id = $request->input('audio_id');
        $Post->Title_ar = $request->input('Title_ar');
        $Post->Title_en = $request->input('Title_en');
        $Post->Title_fr = $request->input('Title_fr');
        $Post->body_ar = $request->input('body_ar');
        $Post->body_en = $request->input('body_en');
        $Post->body_fr = $request->input('body_fr');
        $Post->googledrive = $request->input('googledrive');
        $Post->Downloud = $request->input('Downloud');
        $Post->featured = $request->input('featured') == null ? "of":"on";
        $Post->link = $request->input('link');
        $Post->link_2 = $request->input('link_2');
        $Post->link_3 = $request->input('link_3');
        $Post->link_4 = $request->input('link_4');
        $Post->slider = $request->input('slider');
        $Post->year = $request->input('year');
            // Attach genres
        $Post->genres()->sync($data['category_ids']);
        $Post->save();
        return redirect()->route('Posts.index')->with('success','Movie Updated successfully.');
    }

    /**
     * 
     * Remove the specified resource from storage google Drive.
     *
     * @param  int  $id.
     * @return \Illuminate\Http\Response.
     * 
     */
    public function destroy($id)
    {
        // Post Delete.
        $Post = Post::findOrFail($id);
        // Delete related genres manually from the pivot table.
        $Post->genres()->detach();
        $Post->delete();
        return back()->with('Delete','Tracks deleted successfully');
    }

    public function updateFeatured(Request $request)
{
    $post = Post::find($request->id); // Find the post by ID
    if ($post) {
        $post->featured = $request->featured == null ? "of" :"on" ; // Update the featured status
        $post->save(); // Save the changes to the database

        return back()->with('success', 'Movie updated successfully.');
        }

        return back()->with('success', 'Movie updated successfully.');
}
public function updateSlider(Request $request)
{
    $post = Post::find($request->id); // Find the post by ID
    if ($post) {
        $post->slider = $request->slider ; // Update the featured status
        $post->save(); // Save the changes to the database
        return back()->with('success', 'Movie updated successfully.');
        }

        return back()->with('success', 'Movie updated successfully.');
}
}
