<?php

namespace App\Http\Controllers\Dashboard;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Http\Controllers\Controller;
use App\Http\Requests\StoreRoles;
use App\Http\Requests\StoreUsers;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\User;   
use Auth;
use File;
use Validator;
use App\ImageUpload;


class Users extends Controller
{
    /**
     * Show the middleware dashboard Super-Admin.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function __construct()
    {
        $this->middleware(['auth','role_or_permission:Super-Admin|Supervisors']);
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
       // GET Users
       $Users = User::simplePaginate(10); 
       return view('Dashboard.Users.index',compact('Users')); 
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    { 
        // GET Roles
        $Roles = Role::all();
        // GET dashboardRoles create
        return view('Dashboard.Users.create',compact('Roles'));
    }
    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */ 
    public function store(Request $request)
    {
       $validator = Validator::make($request->all(), [
       // Do not allow any shady characters
           'name' => 'required|max:30|regex:[A-Za-z1-9 ]',
           'email' => 'required|max:100',
           'password' => 'required|confirmed|min:6',
       ]);

        $ImageUpload = ImageUpload::max('id');
        $user = User::create([
            'name' => $request->name,    
            'email' => $request->email,  
            'Phone' => $request->Phone,   
            'ImageUpload_id' => $ImageUpload, 
            'password' => Hash::make($request->password) 

        ]);
       // Checking if a role was selected
        if ($request->has('role')) {
            $roleId = $request->input('role'); // Retrieve the selected role ID
            $role = Role::findOrFail($roleId); // Find the role by ID or fail
            $user->assignRole($role); // Assign the role to the user
        } 
            return redirect()->route('Users.index')
                        ->with('success','User Store successfully.');

    }


    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($slug) 
    { 
         //To Get All User 
        $User = User::where('slug', '=', $slug)->firstOrFail();
        // GET Roles
        $Roles = Role::all();
         // ImageUpload
        $ImageUpload = ImageUpload::max('id') + 1;
        return view('Dashboard.Users.edit',compact('User','Roles','ImageUpload'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $slug)
    {
        // GET User
       $User = User::where('slug', '=', $slug)->firstOrFail();
        // GET validate
         $validator = Validator::make($request->all(), [
        // Do not allow any shady characters
           'password' => 'required|confirmed|min:6',
       ]);
        $User->name = $request->input('name');
        $User->email = $request->input('email');
        $User->Phone = $request->input('Phone');
        $User->ImageUpload_id = $request->input('ImageUpload_id');
        $User->password = Hash::make($request->password);
        $roleId = $request->input('role'); // Retrieve the selected role
        if ($roleId) {
            // Sync the user with the selected role (replaces any existing roles)
            $User->roles()->sync([$roleId]); 
        } else {
            // If no role is selected, detach all roles
            $User->roles()->detach();
        }
        $User->save();
        return redirect()->route('Users.index')

                        ->with('success','User Updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        // User Delete
        $User = User::findOrFail($id);
        $User->delete();
        return back()->with('Delete','User deleted successfully');
    }
    
        public function Hideads(Request $request){
        $days = $request->input('days');
        $userid = $request->input('user_id');
        $user=User::find($userid);
        $user->hide_ads_until=now()->addDays($days);
        $user->save();
        return back()->with('success', "Ads hidden for $days days.");
    }
    
        public function UnhideAds(Request $request)
{
    // Find the user
    $user = User::findOrFail($request->user_id);

    // Remove the ad-free period
    $user->hide_ads_until = null;
    $user->save();

    // Return success response
    return redirect()->back()->with('success', 'Ads are now visible for this user.');
}
}
