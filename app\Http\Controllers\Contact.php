<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Message;
use Validator;

class ContactController extends Controller
{
    public function showForm()
    {
        return view('contact');
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|max:100',
            'Message' => 'required|max:1000',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        Message::create([
            'name' => $request->name,
            'Message' => $request->Message,
        ]);

        return redirect()->back()->with('Messagge', '✅ تم إرسال الرسالة بنجاح');
    }
}
