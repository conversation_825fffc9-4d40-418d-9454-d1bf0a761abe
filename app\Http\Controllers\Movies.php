<?php

namespace App\Http\Controllers;
use Illuminate\Http\Request;
use App\Post;
use App\User;
use App\Category;
use App\Comment;
use App\Client;
use Auth;

class Movies extends Controller
{
    /** 
     * 
     *
     * Movie Downloud Size.
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response.
     * 
     * 
     */
    public function index()
    {
        $Movies = Post::latest()
    ->where('featured', '=', 'on')
    ->whereDoesntHave('genres', function ($query) {
        $query->where('slug', 'live-tv');
    })
    ->whereNull('id_season')
    ->simplePaginate(15);
        return view('Pages.Movies.index',compact('Movies'));
    }

    /**
     * 
     * Display the specified Resource.
     *
     * @param  int  $slug
     * @return \Illuminate\Http\Response
    **/
    public function show($slug)
    {
        // Retrieve the query parameter (e.g., 'id')
        $parameter = request()->query('id'); // Replace 'id' with your parameter name

        $Movie = Post::where('slug', '=', $slug)
                     ->where("featured", "=", "on")
                     ->firstOrFail();
    
        // get outher movies 
     $outher_movies = Post::where('id', '!=', $Movie->id) // Exclude the current movie
    ->where("featured", "on") // Only featured
    ->whereHas('genres', function ($query) use ($Movie) {
        $query->whereIn('categories.id', $Movie->genres->pluck('id')); // Reference 'categories.id'
    })
    ->whereNull('id_season') // Ensure it is not a season
    ->simplePaginate(20);

        // To Get Comments 
        $Comments = $Movie->Comments;
         if($parameter && $Movie->id==$parameter){
            return view('Pages.Movies.show', compact('Movie', 'Comments'));
         }
        // Handle different views based on category
        if($Movie->genres->pluck('slug')->contains('live-tv')) {
            return view('Pages.Movies.show', compact('Movie', 'Comments'));
        } else {
            return view('Pages.Movies.show_details', compact('Movie',"outher_movies"));
        }
    }
    
}
