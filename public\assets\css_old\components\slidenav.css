/*! UIkit 2.26.3 | http://www.getuikit.com | (c) 2014 YOOtheme | MIT License */
/* ========================================================================
   Component: Slidenav
 ========================================================================== */
/*
 * 1. Required for `a` elements
 * 2. Dimension
 * 3. Style
 */
.uk-slidenav {
  /* 1 */
  display: inline-block;
  /* 2 */
  box-sizing: border-box;
  width: 60px;
  height: 60px;
  /* 3 */
  line-height: 60px;
  color: rgba(50, 50, 50, 0.4);
  font-size: 60px;
  text-align: center;
}
/*
 * Hover
 * 1. Apply hover style also to focus state
 * 2. Remove default focus style
 * 3. Required for `a` elements
 * 4. Style
 */
.uk-slidenav:hover,
.uk-slidenav:focus {
  /* 2 */
  outline: none;
  /* 3 */
  text-decoration: none;
  /* 4 */
  color: rgba(50, 50, 50, 0.7);
  cursor: pointer;
}
/* Active */
.uk-slidenav:active {
  color: rgba(50, 50, 50, 0.9);
}
/*
 * Icons
 */
.uk-slidenav-previous:before {
  content: "\f104";
  font-family: FontAwesome;
}
.uk-slidenav-next:before {
  content: "\f105";
  font-family: FontAwesome;
}
/* Sub-object: `uk-slidenav-position`
 ========================================================================== */
/*
 * Create position context
 */
.uk-slidenav-position {
  position: relative;
}
/*
 * Center vertically
 */
.uk-slidenav-position .uk-slidenav {
  display: none;
  position: absolute;
  top: 50%;
  z-index: 1;
  margin-top: -30px;
}
.uk-slidenav-position:hover .uk-slidenav {
  display: block;
}
.uk-slidenav-position .uk-slidenav-previous {
  left: 20px;
}
.uk-slidenav-position .uk-slidenav-next {
  right: 20px;
}
/* Modifier: `uk-slidenav-contrast`
 ========================================================================== */
.uk-slidenav-contrast {
  color: rgba(255, 255, 255, 0.5);
}
/*
 * Hover
 * 1. Apply hover style also to focus state
 */
.uk-slidenav-contrast:hover,
.uk-slidenav-contrast:focus {
  color: rgba(255, 255, 255, 0.7);
}
/* Active */
.uk-slidenav-contrast:active {
  color: rgba(255, 255, 255, 0.9);
}
