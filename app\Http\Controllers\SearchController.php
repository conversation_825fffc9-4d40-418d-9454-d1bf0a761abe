<?php

namespace App\Http\Controllers;
// use Request;
use App\Category;
use App\User;
use App\Post;
use App\Serie;
use Illuminate\Http\Request;
use Session, DB;
use Auth;
use Validator; 

class SearchController extends Controller
{

    /*
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function search(Request $request)
    {   
            // Gets the query string from our form Movies 
            $search = $request->input('search');
            // $category=\Request::get('category');
            // if($category=="series"){
            //    $Series = Serie::where('Title_en', 'LIKE', '%' . $search . '%')
            //   ->orWhere("Title_ar", 'LIKE', '%' . $search . '%')
            //   ->where("featured","=","on")->simplepaginate(12);
            //   return view('Pages.Series.index', compact('Series'));
            //   if(count($Series) > 0){
            //     return view('Pages.Series.index',compact('Series','search'));
            //    }else{
            //      return redirect()->back();
            //    }
            // }
            // Searches for Featured Movies titles //
        $Movies = Post::where("featured", "on") // Ensure "featured" applies to all
    ->where(function ($query) use ($search) {
        $query->orWhere('Title_ar', 'LIKE', '%' . $search . '%')
               ->orWhere('Title_en', 'LIKE', '%' . $search . '%')
              ->orWhere('Title_fr', 'LIKE', '%' . $search . '%');
    })
    ->simplePaginate(30);

return view('Pages.search', compact('Movies', 'search'));
       
            
    } 
}

