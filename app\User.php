<?php

namespace App;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;
use <PERSON><PERSON>brock\EloquentSluggable\Sluggable;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\ImageUpload;
use App\Comment;
use App\Favourite;
use Carbon\Carbon;

class User extends Authenticatable
{
    use Notifiable;
    use HasRoles;
    use Sluggable;
     /**
     * Return the sluggable configuration array for this model.
     *
     * @return array
     */
    public function sluggable(): array
    {
        return [
            'slug' => [
                'source' => 'name'
            ]
        ];
    }
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name','Phone','email','ImageUpload_id','password','slug','hide_ads_until'
    ];
    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password', 'remember_token',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    public function hasHiddenAds()
{
    return $this->hide_ads_until && now()->lt($this->hide_ads_until);
}


public function getRemainingAdFreeDaysAttribute()
{
    if (!$this->hide_ads_until) {
        return 0; // No ad-free period
    }

    $remainingSeconds = Carbon::now()->diffInSeconds(Carbon::parse($this->hide_ads_until), false);

    return $remainingSeconds > 0 ? max(1, ceil($remainingSeconds / 86400)) : 0;
}


    public function ImageUpload()
    {
        return $this->belongsTo(ImageUpload::class,'ImageUpload_id');
    }

    // THIS function Comment TO MAKE RELATHION
    public function Comments()
    {
        return $this->hasMany('App\Comment');
    }

    // THIS function Favourites TO MAKE RELATHION
    public function Favourites()
    {
        return $this->hasMany('App\Favourite');
    }

    
   
}
