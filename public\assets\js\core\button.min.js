/*! UIkit 2.26.3 | http://www.getuikit.com | (c) 2014 YOOtheme | MIT License */
!function(t){"use strict";t.component("buttonRadio",{defaults:{activeClass:"uk-active",target:".uk-button"},boot:function(){t.$html.on("click.buttonradio.uikit","[data-uk-button-radio]",function(i){var a=t.$(this);if(!a.data("buttonRadio")){var e=t.buttonRadio(a,t.Utils.options(a.attr("data-uk-button-radio"))),o=t.$(i.target);o.is(e.options.target)&&o.trigger("click")}})},init:function(){var i=this;this.find(i.options.target).attr("aria-checked","false").filter("."+i.options.activeClass).attr("aria-checked","true"),this.on("click",this.options.target,function(a){var e=t.$(this);e.is('a[href="#"]')&&a.preventDefault(),i.find(i.options.target).not(e).removeClass(i.options.activeClass).blur(),e.addClass(i.options.activeClass),i.find(i.options.target).not(e).attr("aria-checked","false"),e.attr("aria-checked","true"),i.trigger("change.uk.button",[e])})},getSelected:function(){return this.find("."+this.options.activeClass)}}),t.component("buttonCheckbox",{defaults:{activeClass:"uk-active",target:".uk-button"},boot:function(){t.$html.on("click.buttoncheckbox.uikit","[data-uk-button-checkbox]",function(i){var a=t.$(this);if(!a.data("buttonCheckbox")){var e=t.buttonCheckbox(a,t.Utils.options(a.attr("data-uk-button-checkbox"))),o=t.$(i.target);o.is(e.options.target)&&o.trigger("click")}})},init:function(){var i=this;this.find(i.options.target).attr("aria-checked","false").filter("."+i.options.activeClass).attr("aria-checked","true"),this.on("click",this.options.target,function(a){var e=t.$(this);e.is('a[href="#"]')&&a.preventDefault(),e.toggleClass(i.options.activeClass).blur(),e.attr("aria-checked",e.hasClass(i.options.activeClass)),i.trigger("change.uk.button",[e])})},getSelected:function(){return this.find("."+this.options.activeClass)}}),t.component("button",{defaults:{},boot:function(){t.$html.on("click.button.uikit","[data-uk-button]",function(){var i=t.$(this);if(!i.data("button")){{t.button(i,t.Utils.options(i.attr("data-uk-button")))}i.trigger("click")}})},init:function(){var t=this;this.element.attr("aria-pressed",this.element.hasClass("uk-active")),this.on("click",function(i){t.element.is('a[href="#"]')&&i.preventDefault(),t.toggle(),t.trigger("change.uk.button",[t.element.blur().hasClass("uk-active")])})},toggle:function(){this.element.toggleClass("uk-active"),this.element.attr("aria-pressed",this.element.hasClass("uk-active"))}})}(UIkit);