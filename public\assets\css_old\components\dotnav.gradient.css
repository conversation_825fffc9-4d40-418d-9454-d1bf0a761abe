/*! UIkit 2.26.3 | http://www.getuikit.com | (c) 2014 YOOtheme | MIT License */
/* ========================================================================
   Component: Dotnav
 ========================================================================== */
/*
 * 1. Gutter
 * 2. Remove default list style
 */
.uk-dotnav {
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -ms-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  flex-wrap: wrap;
  /* 1 */
  margin-left: -15px;
  margin-top: -15px;
  /* 2 */
  padding: 0;
  list-style: none;
}
/*
 * 1. Space is allocated solely based on content dimensions
 * 2. Horizontal gutter is using `padding` so `uk-width-*` classes can be applied
 */
.uk-dotnav > * {
  /* 1 */
  -ms-flex: none;
  -webkit-flex: none;
  flex: none;
  /* 2 */
  padding-left: 15px;
  margin-top: 15px;
}
/*
 * DEPRECATED IE9 Support
 */
.uk-dotnav:before,
.uk-dotnav:after {
  content: "";
  display: block;
  overflow: hidden;
}
.uk-dotnav:after {
  clear: both;
}
.uk-dotnav > * {
  float: left;
}
/* Items
 ========================================================================== */
/*
 * Items
 * 1. Hide text if present
 */
.uk-dotnav > * > * {
  display: block;
  box-sizing: content-box;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: rgba(50, 50, 50, 0.1);
  /* 1 */
  text-indent: 100%;
  overflow: hidden;
  white-space: nowrap;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}
/*
 * Hover
 * 1. Apply hover style also to focus state
 * 2. Remove default focus style
 */
.uk-dotnav > * > :hover,
.uk-dotnav > * > :focus {
  background: rgba(50, 50, 50, 0.4);
  /* 2 */
  outline: none;
}
/* OnClick */
.uk-dotnav > * > :active {
  background: rgba(50, 50, 50, 0.6);
}
/* Active */
.uk-dotnav > .uk-active > * {
  background: rgba(50, 50, 50, 0.4);
  -webkit-transform: scale(1.3);
  transform: scale(1.3);
}
/* Modifier: `uk-dotnav-contrast`
 ========================================================================== */
.uk-dotnav-contrast > * > * {
  background: rgba(255, 255, 255, 0.4);
}
/*
 * Hover
 * 1. Apply hover style also to focus state
 */
.uk-dotnav-contrast > * > :hover,
.uk-dotnav-contrast > * > :focus {
  background: rgba(255, 255, 255, 0.7);
}
/* OnClick */
.uk-dotnav-contrast > * > :active {
  background: rgba(255, 255, 255, 0.9);
}
/* Active */
.uk-dotnav-contrast > .uk-active > * {
  background: rgba(255, 255, 255, 0.9);
}
/* Modifier: 'uk-dotnav-vertical'
 ========================================================================== */
/*
 * DEPRECATED
 */
.uk-dotnav-vertical {
  -ms-flex-direction: column;
  -webkit-flex-direction: column;
  flex-direction: column;
}
/*
 * DEPRECATED IE9 Support
 */
.uk-dotnav-vertical > * {
  float: none;
}
