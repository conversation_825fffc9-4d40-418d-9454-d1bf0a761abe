<?php

namespace App\Http\Controllers\Dashboard;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Requests\StorePost;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\ImageUpload;
use App\User; 
use App\Client; 
use App\Serie;  
use App\Category;
use App\Instagram;
use App\Audio;
use Auth;
use File;
use Validator;

class Series extends Controller
{  

    /**
     * 
     * Show the middleware dashboard Super Admin.
     *
     * @return \Illuminate\Contracts\Support\Renderable.
     * 
     */
    public function __construct()
    {
        $this->middleware(['auth','role_or_permission:Super-Admin|Supervisors']);
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
 
    public function index(Request $request)
    {
        $query = Serie::query()->latest()->with('Category', 'Client');
    
        // Apply search filter
        if ($request->has('search') && $request->input('search') != '') {
            $searchTerm = $request->input('search');
            
            $query->where('Title_en', 'LIKE', "%{$searchTerm}%")
                  ->orWhereHas('Category', function ($q) use ($searchTerm) {
                      // Replace 'name' with the actual field in the Category model for Series
                      $q->where('Title_en', 'LIKE', "%{$searchTerm}%");
                  });
        }
    
        // Paginate the filtered results
        $Series = $query->simplePaginate(10);
    
        return view('Dashboard.Series.index', compact('Series'));
    }
    

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        // GET Artists
        $Artists = Client::all();
        // GET Categores
        $Categores = Category::all();
         // GET Albums
        $Albums = Instagram::all();
        return view('Dashboard.Series.create',compact('Artists','Categores','Albums'));
    }
 
    /**
     * 
     * Store a newly Created Resource in Storage.
     *
     * @param  \Illuminate\Http\Request  $request.
     * @return \Illuminate\Http\Response
     * 
     */

    public function store(Request $request)
    {
        // GET validate
        $validated= $request->validate([
        'author_id' => 'required',
        'Title_en' => 'required',
        'body_en' => 'required',
        'category_ids' => 'required|array', // Multiple category IDs
        'category_ids.*' => 'exists:categories,id', // Ensure genres exist
        ]);

        $ImageUpload = ImageUpload::max('id');

       $serie= Serie::create([
            'author_id' => $request->author_id,  
            'Instagram_id' => $request->Instagram_id,
            'Title_ar' => $request->Title_ar,  
            'Title_en' => $request->Title_en, 
            'Title_fr' => $request->Title_fr,
            'body_ar' => $request->body_ar,  
            'body_en' => $request->body_en,
            'body_fr' => $request->body_fr, 
            'featured' => $request->featured == null ? "of": "on"  ,
            'ImageUpload_id' => $ImageUpload
        ]);
        $serie->genres()->sync($validated['category_ids']);

            return redirect()->route('Series.index')->with('success','Series Store successfully.');
    }

    /**
     * Show the form for editing the specified Resource.
     *
     * @param  int  $slug
     * @return \Illuminate\Http\Response
     */
    public function edit($slug)
    {
        //To Get All Post 
        $Serie = Serie::where('slug', '=', $slug)->firstOrFail();
         // GET Artists
        $Artists = Client::all();
        // GET Categores
        $Categores = Category::all();
         // GET Albums
        $Albums = Instagram::all();
        // ImageUpload
        $ImageUpload = ImageUpload::max('id') + 1;
        return view('Dashboard.Series.edit',compact('Serie','Artists','Categores','ImageUpload','Albums'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response 
     */
    public function update(Request $request, $slug)
    {
        // GET Post.
        $Serie = Serie::where('slug', '=', $slug)->firstOrFail();
        
        // GET validate.
        $data = $request->validate([
        'author_id' => 'required',
        'Title_en' => 'required',
        'body_en' => 'required',
        'category_ids' => 'required|array', // Multiple category IDs
        'category_ids.*' => 'exists:categories,id', // Ensure genres exist
        ]);
        
        $Serie->author_id = $request->input('author_id');
        $Serie->Instagram_id = $request->input('Instagram_id');
        $Serie->ImageUpload_id = $request->input('ImageUpload_id');
        $Serie->Title_ar = $request->input('Title_ar');
        $Serie->Title_en = $request->input('Title_en');
        $Serie->Title_fr = $request->input('Title_fr');
        $Serie->body_ar = $request->input('body_ar');
        $Serie->body_en = $request->input('body_en');
        $Serie->body_fr = $request->input('body_fr');
        $Serie->featured = $request->input('featured') == null ? "of":"on";
        $Serie->genres()->sync($data['category_ids']);
        $Serie->save();
        return redirect()->route('Series.index')->with('success','Series Updated successfully.');
    }

    /**
     * 
     * Remove the specified resource from storage google Drive.
     *
     * @param  int  $id.
     * @return \Illuminate\Http\Response.
     * 
     */
    public function destroy($id)
    {
        // Post Delete.
        $Serie = Serie::findOrFail($id);
        // Delete related genres manually from the pivot table.
        $Serie->genres()->detach();
        $Serie->delete();
        return back()->with('Delete','Tracks deleted successfully');
    }

    public function updateFeatured(Request $request)
{
    $Serie = Serie::find($request->id); // Find the Serie by ID
    if ($Serie) {
        $Serie->featured = $request->featured == null ? "of" :"on" ; // Update the featured status
        $Serie->save(); // Save the changes to the database

        return redirect()->route('Series.index')->with('success','Series Updated successfully.');
    }

    return redirect()->route('Series.index')->with('success','Series Updated successfully.');
}
}
