<?php

namespace App\Http\Controllers\Dashboard;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Requests\StorePost;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\ImageUpload;
use App\Post;  
use App\Audio;
use App\Season;
use Auth;
use File;
use Validator;

class Episodes extends Controller
{  

    /**
     * 
     * Show the middleware dashboard Super Admin.
     *
     * @return \Illuminate\Contracts\Support\Renderable.
     * 
     */
    public function __construct()
    {
        $this->middleware(['auth','role_or_permission:Super-Admin|Supervisors']);
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $query = Post::query()
                    ->latest()
                    ->whereNotNull('id_season')  // Ensure only posts with a season
                    ->with(['Season', 'Season.Serie']);  // Load the related Season and Serie model
    
        // Apply search filter
        if ($request->has('search') && $request->input('search') != '') {
            $searchTerm = $request->input('search');
    
            $query->where('Title_en', 'LIKE', "%{$searchTerm}%")  // Search in Post title
                  ->orWhereHas('Season', function ($q) use ($searchTerm) {
                      // Search in the related Season model
                      $q->where('position', 'LIKE', "%{$searchTerm}%") // Search in Season's position
                        ->orWhereHas('Serie', function ($q) use ($searchTerm) {
                            // Search in the related Serie model
                            $q->where('Title_en', 'LIKE', "%{$searchTerm}%");  // Search in Serie's Title
                        });
                  });
        }
    
        // Paginate the filtered results
        $Episodes = $query->simplePaginate(15);
    
        return view('Dashboard.Episodes.index', compact('Episodes'));
    }
    

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        // Get the 'id_season' query parameter
        $id_season = request('id_season');
        $title_season=null;
        /// get title of season
        if($id_season){
            $title_season=Season::where("id","=",$id_season)->first()->Title_en;
        }
        $Seasons = Season::with('Serie')->get();
        return view('Dashboard.Episodes.create',compact('Seasons',"id_season","title_season"));
    }
 
    /**
     * 
     * Store a newly Created Resource in Storage.
     *
     * @param  \Illuminate\Http\Request  $request.
     * @return \Illuminate\Http\Response
     * 
     */

    public function store(Request $request)
    {
        // GET validate
        $request->validate([
        'Title_en' => 'required',
        "position"=>"required",
        "id_season"=>'required',
        ]);
        
    // Check if the position already exists in the same season
    $existingEpisode = Post::where('id_season', $request->id_season)
    ->where('position', $request->position)
    ->first();

    if ($existingEpisode) {
        return redirect()->back()
            ->withErrors(['position' => 'This Number of Episode is already taken in the selected season.'])
            ->withInput();
    }

        $Season=Season::where("id","=",$request->id_season)->with("Serie")->first();
        $AudioUpload = Audio::max('id');

        $episode= Post::create([
            'author_id' => $Season->Serie->author_id,    
            'Instagram_id' => $Season->Serie->Instagram_id,
            'Title_ar' => $request->Title_ar,  
            'Title_en' => $request->Title_en, 
            'Title_fr' => $request->Title_fr,
            'body_ar' => $Season->body_ar,  
            'body_en' => $Season->body_en,
            'body_fr' => $Season->body_fr, 
            'Downloud' => $request->Downloud,
            'link' => $request->link,
            'link_2' => $request->link_2,
            'link_3' => $request->link_3,
            'googledrive' => $request->googledrive,
            'audio_id' => $AudioUpload,
            'featured' => $request->featured == null ? "of": "on"  ,
            'ImageUpload_id' =>$Season->ImageUpload_id,
            "id_season"=>$request->id_season,
            "position"=>$request->position,
        ]);
     // Attach genres from the series to the episode
if ($Season->Serie->genres->isNotEmpty()) {
    $episode->genres()->sync($Season->Serie->genres->pluck('id')->toArray());
}
            return redirect()->route('Episodes.index')->with('success','Episode Store successfully.');
    }

    /**
     * Show the form for editing the specified Resource.
     *
     * @param  int  $slug
     * @return \Illuminate\Http\Response
     */
    public function edit($slug)
    {
        //To Get All Post 
        $Episode = Post::where('slug', '=', $slug)->firstOrFail();
    
        // AudioUpload
        $AudioUpload = Audio::max('id') + 1;
        $Seasons = Season::with('Serie')->get();
        return view('Dashboard.Episodes.edit',compact('Episode','AudioUpload',"Seasons"));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response 
     */
    public function update(Request $request, $slug)
    {
        // GET Post.
        $Episode = Post::where('slug', '=', $slug)->firstOrFail();
        
        // GET validate.
        $data = $request->validate([
        'id_season' => 'required',
        "position"=>"required",
        'Title_en' => 'required',
        ]);
    // Check if the position already exists in the same season, excluding the current episode
    $existingEpisode = Post::where('id_season', $request->id_season)
        ->where('position', $request->position)
        ->where('id', '!=', $Episode->id) // Exclude the current episode
        ->first();
        
    if ($existingEpisode) {
        return redirect()->back()
            ->withErrors(['position' => 'This Number of Episode is already taken in the selected season.'])
            ->withInput();
    }
        
        $Episode->author_id = $request->input('author_id');
        $Episode->id_season = $request->input('id_season');
        $Episode->audio_id = $request->input('audio_id');
        $Episode->Title_ar = $request->input('Title_ar');
        $Episode->Title_en = $request->input('Title_en');
        $Episode->Title_fr = $request->input('Title_fr');
        $Episode->googledrive = $request->input('googledrive');
        $Episode->Downloud = $request->input('Downloud');
        $Episode->featured = $request->input('featured') == null ? "of":"on";
        $Episode->link = $request->input('link');
        $Episode->link_2 = $request->input('link_2');
        $Episode->link_3 = $request->input('link_3');
        $Episode->position = $request->input('position');
        $Episode->save();
        return redirect()->route('Episodes.index')->with('success','Episode Updated successfully.');
    }

    /**
     * 
     * Remove the specified resource from storage google Drive.
     *
     * @param  int  $id.
     * @return \Illuminate\Http\Response.
     * 
     */
    public function destroy($id)
    {
        // Post Delete.
        $Episode = Post::findOrFail($id);
        $Episode->delete();
        return back()->with('Delete','Episode deleted successfully');
    }

    public function updateFeatured(Request $request)
{
    $post = Post::find($request->id); // Find the post by ID
    if ($post) {
        $post->featured = $request->featured == null ? "of" :"on" ; // Update the featured status
        $post->save(); // Save the changes to the database

        return redirect()->route('Episodes.index')->with('success','Episode Updated successfully.');
    }

    return redirect()->route('Episodes.index')->with('success','Movie Updated successfully.');
}
}
