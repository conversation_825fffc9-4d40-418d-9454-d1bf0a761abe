/*! UIkit 2.26.3 | http://www.getuikit.com | (c) 2014 YOOtheme | MIT License */
/* ========================================================================
   Component: Tooltip
 ========================================================================== */
/*
 * 1. Hide by default
 * 2. Set fixed position
 * 3. Set dimensions
 * 4. Set style
 */
.uk-tooltip {
  /* 1 */
  display: none;
  /* 2 */
  position: absolute;
  z-index: 1030;
  /* 3 */
  box-sizing: border-box;
  max-width: 200px;
  padding: 5px 8px;
  /* 4 */
  background: #333333;
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  line-height: 18px;
}
/* Triangle
 ========================================================================== */
/*
 * 1. Dashed is less antialised than solid
 */
.uk-tooltip:after {
  content: "";
  display: block;
  position: absolute;
  width: 0;
  height: 0;
  /* 1 */
  border: 5px dashed #333333;
}
/* Direction modifiers
 ========================================================================== */
/*
 * Top
 */
.uk-tooltip-top:after,
.uk-tooltip-top-left:after,
.uk-tooltip-top-right:after {
  bottom: -5px;
  border-top-style: solid;
  border-bottom: none;
  border-left-color: transparent;
  border-right-color: transparent;
  border-top-color: #333333;
}
/*
 * Bottom
 */
.uk-tooltip-bottom:after,
.uk-tooltip-bottom-left:after,
.uk-tooltip-bottom-right:after {
  top: -5px;
  border-bottom-style: solid;
  border-top: none;
  border-left-color: transparent;
  border-right-color: transparent;
  border-bottom-color: #333333;
}
/*
 * Top/Bottom center
 */
.uk-tooltip-top:after,
.uk-tooltip-bottom:after {
  left: 50%;
  margin-left: -5px;
}
/*
 * Top/Bottom left
 */
.uk-tooltip-top-left:after,
.uk-tooltip-bottom-left:after {
  left: 10px;
}
/*
 * Top/Bottom right
 */
.uk-tooltip-top-right:after,
.uk-tooltip-bottom-right:after {
  right: 10px;
}
/*
 * Left
 */
.uk-tooltip-left:after {
  right: -5px;
  top: 50%;
  margin-top: -5px;
  border-left-style: solid;
  border-right: none;
  border-top-color: transparent;
  border-bottom-color: transparent;
  border-left-color: #333333;
}
/*
 * Right
 */
.uk-tooltip-right:after {
  left: -5px;
  top: 50%;
  margin-top: -5px;
  border-right-style: solid;
  border-left: none;
  border-top-color: transparent;
  border-bottom-color: transparent;
  border-right-color: #333333;
}
