/* ===== ملف CSS محسن لصفحة البث المباشر ===== */

/* متغيرات CSS للألوان والمقاسات */
:root {
  --primary-color: #dc2626;
  --secondary-color: #f59e0b;
  --accent-color: #3b82f6;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --dark-bg: #111827;
  --card-bg: #1f2937;
  --border-radius: 12px;
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --glow: 0 0 20px rgba(59, 130, 246, 0.3);
}

/* تحسينات عامة */
html, body {
  overflow-x: hidden;
  scroll-behavior: smooth;
  font-family: '<PERSON><PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* مشغل الفيديو المحسن */
#videoPlayer {
  width: 100%;
  background: linear-gradient(135deg, #000 0%, #1a1a1a 100%);
  aspect-ratio: 16/9;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow);
}

/* أنيميشن محسن للنبض */
.pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  transform-origin: center;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.05); opacity: 0.8; }
}

/* سبينر تحميل محسن */
.spinner {
  border: 3px solid rgba(255, 255, 255, 0.2);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  width: 48px;
  height: 48px;
  animation: spin 1s linear infinite;
  box-shadow: 0 0 20px rgba(220, 38, 38, 0.3);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* إخفاء شريط التمرير مع تحسينات */
.hide-scrollbar::-webkit-scrollbar { display: none; }
.hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
  overscroll-behavior: contain;
}

/* تحسينات التكبير */
#zoomWrapper {
  overflow: hidden;
  touch-action: none;
  position: relative;
  border-radius: var(--border-radius);
  background: #000;
}

#zoomWrapper video {
  transform-origin: center center;
  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border-radius: var(--border-radius);
}

/* أنيميشن ping سريع */
@keyframes ping-fast {
  0% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.6; }
  100% { transform: scale(1); opacity: 1; }
}

.animate-ping-fast { animation: ping-fast 0.8s ease-in-out infinite; }

/* تحسينات الأزرار */
.btn-modern {
  background: linear-gradient(135deg, var(--primary-color) 0%, #b91c1c 100%);
  border: none;
  border-radius: var(--border-radius);
  padding: 12px 24px;
  color: white;
  font-weight: 600;
  transition: var(--transition);
  box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3);
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.btn-modern:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(220, 38, 38, 0.4);
}

.btn-modern:active {
  transform: translateY(0);
}

.btn-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-modern:hover::before {
  left: 100%;
}

/* تحسينات البطاقات */
.channel-card {
  background: var(--card-bg);
  border-radius: var(--border-radius);
  padding: 16px;
  transition: var(--transition);
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.channel-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: var(--shadow);
  border-color: var(--primary-color);
}

.channel-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
}

.channel-card:hover::before {
  left: 100%;
}

/* تحسينات القوائم المنسدلة */
.dropdown-modern {
  background: var(--card-bg);
  border-radius: var(--border-radius);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: var(--shadow);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* تحسينات الإشعارات */
.notification {
  border-radius: var(--border-radius);
  padding: 16px 20px;
  box-shadow: var(--shadow);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* تأثيرات الحركة المتقدمة */
.fade-in {
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.slide-up {
  animation: slideUp 0.4s ease-out;
}

@keyframes slideUp {
  from { transform: translateY(100%); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* تأثير التدرج للنصوص */
.text-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* تحسينات متجاوبة */
@media (max-width: 768px) {
  .btn-modern {
    padding: 10px 16px;
    font-size: 14px;
  }
  
  .channel-card {
    padding: 12px;
  }
  
  #zoomWrapper {
    border-radius: 8px;
  }
  
  .notification {
    padding: 12px 16px;
  }
}

@media (max-width: 640px) {
  :root {
    --border-radius: 8px;
  }
  
  .btn-modern {
    padding: 8px 12px;
    font-size: 13px;
  }
  
  .channel-card {
    padding: 8px;
  }
}

/* تحسينات للوضع المظلم */
@media (prefers-color-scheme: dark) {
  :root {
    --card-bg: #1f2937;
    --dark-bg: #111827;
  }
}

/* تحسينات الأداء */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

/* تأثيرات التركيز للوصولية */
.btn-modern:focus,
.channel-card:focus {
  outline: 2px solid var(--accent-color);
  outline-offset: 2px;
}

/* تحسينات للطباعة */
@media print {
  .btn-modern,
  .notification,
  #zoomWrapper {
    display: none;
  }
}
