[2025-06-09 20:05:53] local.ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'slider' in 'where clause' (Connection: mysql, SQL: select * from `posts` where `featured` = on and `slider` = 1 order by `created_at` desc limit 16 offset 0) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'slider' in 'where clause' (Connection: mysql, SQL: select * from `posts` where `featured` = on and `slider` = 1 order by `created_at` desc limit 16 offset 0) at C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(738): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(722): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(959): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 C:\\xampp\\htdocs\\ffff\\app\\Http\\Controllers\\HomeController.php(32): Illuminate\\Database\\Eloquent\\Builder->simplePaginate(15)
#10 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\HomeController->index()
#11 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#12 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomeController), 'index')
#13 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#30 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#38 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#39 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#40 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#54 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\ffff\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\ffff\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#57 {main}

[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'slider' in 'where clause' at C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:423)
[stacktrace]
#0 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): PDO->prepare('select * from `...')
#1 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#5 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(738): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(722): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#10 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(959): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#11 C:\\xampp\\htdocs\\ffff\\app\\Http\\Controllers\\HomeController.php(32): Illuminate\\Database\\Eloquent\\Builder->simplePaginate(15)
#12 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\HomeController->index()
#13 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#14 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomeController), 'index')
#15 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#16 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#17 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#32 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#40 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#41 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 C:\\xampp\\htdocs\\ffff\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 C:\\xampp\\htdocs\\ffff\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#59 {main}
"} 
[2025-06-09 20:07:14] local.ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'slider' in 'where clause' (Connection: mysql, SQL: select * from `posts` where `featured` = on and `slider` = 1 order by `created_at` desc limit 16 offset 0) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'slider' in 'where clause' (Connection: mysql, SQL: select * from `posts` where `featured` = on and `slider` = 1 order by `created_at` desc limit 16 offset 0) at C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(738): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(722): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(959): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 C:\\xampp\\htdocs\\ffff\\app\\Http\\Controllers\\HomeController.php(32): Illuminate\\Database\\Eloquent\\Builder->simplePaginate(15)
#10 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\HomeController->index()
#11 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#12 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomeController), 'index')
#13 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#30 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#38 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#39 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#40 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#54 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\ffff\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\ffff\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#57 {main}

[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'slider' in 'where clause' at C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:423)
[stacktrace]
#0 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): PDO->prepare('select * from `...')
#1 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#5 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(738): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(722): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#10 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(959): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#11 C:\\xampp\\htdocs\\ffff\\app\\Http\\Controllers\\HomeController.php(32): Illuminate\\Database\\Eloquent\\Builder->simplePaginate(15)
#12 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\HomeController->index()
#13 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#14 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomeController), 'index')
#15 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#16 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#17 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#32 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#40 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#41 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 C:\\xampp\\htdocs\\ffff\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 C:\\xampp\\htdocs\\ffff\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#59 {main}
"} 
[2025-06-09 20:07:34] local.ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'slider' in 'where clause' (Connection: mysql, SQL: select * from `posts` where `featured` = on and `slider` = 1 order by `created_at` desc limit 16 offset 0) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'slider' in 'where clause' (Connection: mysql, SQL: select * from `posts` where `featured` = on and `slider` = 1 order by `created_at` desc limit 16 offset 0) at C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(738): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(722): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(959): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 C:\\xampp\\htdocs\\ffff\\app\\Http\\Controllers\\HomeController.php(32): Illuminate\\Database\\Eloquent\\Builder->simplePaginate(15)
#10 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\HomeController->index()
#11 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#12 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomeController), 'index')
#13 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#30 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#38 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#39 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#40 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#54 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\ffff\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\ffff\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#57 {main}

[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'slider' in 'where clause' at C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:423)
[stacktrace]
#0 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): PDO->prepare('select * from `...')
#1 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#5 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(738): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(722): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#10 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(959): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#11 C:\\xampp\\htdocs\\ffff\\app\\Http\\Controllers\\HomeController.php(32): Illuminate\\Database\\Eloquent\\Builder->simplePaginate(15)
#12 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\HomeController->index()
#13 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#14 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomeController), 'index')
#15 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#16 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#17 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#32 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#40 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#41 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 C:\\xampp\\htdocs\\ffff\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 C:\\xampp\\htdocs\\ffff\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#59 {main}
"} 
[2025-06-09 20:16:36] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'arabjmqf_blockter.category_movie' doesn't exist (Connection: mysql, SQL: select * from `categories` where exists (select * from `posts` inner join `category_movie` on `posts`.`id` = `category_movie`.`movie_id` where `categories`.`id` = `category_movie`.`category_id`) or exists (select * from `posts` inner join `category_serie` on `posts`.`id` = `category_serie`.`serie_id` where `categories`.`id` = `category_serie`.`category_id`)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'arabjmqf_blockter.category_movie' doesn't exist (Connection: mysql, SQL: select * from `categories` where exists (select * from `posts` inner join `category_movie` on `posts`.`id` = `category_movie`.`movie_id` where `categories`.`id` = `category_movie`.`category_id`) or exists (select * from `posts` inner join `category_serie` on `posts`.`id` = `category_serie`.`serie_id` where `categories`.`id` = `category_serie`.`category_id`)) at C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(738): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(722): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\xampp\\htdocs\\ffff\\app\\Http\\Controllers\\HomeController.php(70): Illuminate\\Database\\Eloquent\\Builder->get()
#9 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\HomeController->index()
#10 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#11 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomeController), 'index')
#12 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#13 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#14 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#53 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\ffff\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\ffff\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#56 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'arabjmqf_blockter.category_movie' doesn't exist at C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:423)
[stacktrace]
#0 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): PDO->prepare('select * from `...')
#1 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#5 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(738): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(722): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#10 C:\\xampp\\htdocs\\ffff\\app\\Http\\Controllers\\HomeController.php(70): Illuminate\\Database\\Eloquent\\Builder->get()
#11 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\HomeController->index()
#12 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#13 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomeController), 'index')
#14 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#15 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#16 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#31 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#40 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#55 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\ffff\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#57 C:\\xampp\\htdocs\\ffff\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#58 {main}
"} 
[2025-06-09 20:17:25] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'arabjmqf_blockter.category_movie' doesn't exist (Connection: mysql, SQL: select * from `categories` where exists (select * from `posts` inner join `category_movie` on `posts`.`id` = `category_movie`.`movie_id` where `categories`.`id` = `category_movie`.`category_id`) or exists (select * from `posts` inner join `category_serie` on `posts`.`id` = `category_serie`.`serie_id` where `categories`.`id` = `category_serie`.`category_id`)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'arabjmqf_blockter.category_movie' doesn't exist (Connection: mysql, SQL: select * from `categories` where exists (select * from `posts` inner join `category_movie` on `posts`.`id` = `category_movie`.`movie_id` where `categories`.`id` = `category_movie`.`category_id`) or exists (select * from `posts` inner join `category_serie` on `posts`.`id` = `category_serie`.`serie_id` where `categories`.`id` = `category_serie`.`category_id`)) at C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(738): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(722): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\xampp\\htdocs\\ffff\\app\\Http\\Controllers\\HomeController.php(70): Illuminate\\Database\\Eloquent\\Builder->get()
#9 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\HomeController->index()
#10 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#11 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomeController), 'index')
#12 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#13 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#14 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#53 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\ffff\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\ffff\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#56 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'arabjmqf_blockter.category_movie' doesn't exist at C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:423)
[stacktrace]
#0 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): PDO->prepare('select * from `...')
#1 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#5 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(738): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(722): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#10 C:\\xampp\\htdocs\\ffff\\app\\Http\\Controllers\\HomeController.php(70): Illuminate\\Database\\Eloquent\\Builder->get()
#11 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\HomeController->index()
#12 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#13 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomeController), 'index')
#14 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#15 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#16 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#31 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#40 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#55 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\ffff\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#57 C:\\xampp\\htdocs\\ffff\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#58 {main}
"} 
[2025-06-09 20:17:35] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'arabjmqf_blockter.category_movie' doesn't exist (Connection: mysql, SQL: select * from `categories` where exists (select * from `posts` inner join `category_movie` on `posts`.`id` = `category_movie`.`movie_id` where `categories`.`id` = `category_movie`.`category_id`) or exists (select * from `posts` inner join `category_serie` on `posts`.`id` = `category_serie`.`serie_id` where `categories`.`id` = `category_serie`.`category_id`)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'arabjmqf_blockter.category_movie' doesn't exist (Connection: mysql, SQL: select * from `categories` where exists (select * from `posts` inner join `category_movie` on `posts`.`id` = `category_movie`.`movie_id` where `categories`.`id` = `category_movie`.`category_id`) or exists (select * from `posts` inner join `category_serie` on `posts`.`id` = `category_serie`.`serie_id` where `categories`.`id` = `category_serie`.`category_id`)) at C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(738): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(722): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\xampp\\htdocs\\ffff\\app\\Http\\Controllers\\HomeController.php(70): Illuminate\\Database\\Eloquent\\Builder->get()
#9 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\HomeController->index()
#10 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#11 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomeController), 'index')
#12 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#13 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#14 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#53 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\ffff\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\ffff\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#56 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'arabjmqf_blockter.category_movie' doesn't exist at C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:423)
[stacktrace]
#0 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): PDO->prepare('select * from `...')
#1 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#5 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(738): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(722): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#10 C:\\xampp\\htdocs\\ffff\\app\\Http\\Controllers\\HomeController.php(70): Illuminate\\Database\\Eloquent\\Builder->get()
#11 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\HomeController->index()
#12 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#13 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomeController), 'index')
#14 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#15 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#16 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#31 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#40 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#55 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\ffff\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#57 C:\\xampp\\htdocs\\ffff\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#58 {main}
"} 
[2025-06-09 20:26:08] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'arabjmqf_blockter.category_movie' doesn't exist (Connection: mysql, SQL: select * from `categories` where exists (select * from `posts` inner join `category_movie` on `posts`.`id` = `category_movie`.`movie_id` where `categories`.`id` = `category_movie`.`category_id`) or exists (select * from `posts` inner join `category_serie` on `posts`.`id` = `category_serie`.`serie_id` where `categories`.`id` = `category_serie`.`category_id`)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'arabjmqf_blockter.category_movie' doesn't exist (Connection: mysql, SQL: select * from `categories` where exists (select * from `posts` inner join `category_movie` on `posts`.`id` = `category_movie`.`movie_id` where `categories`.`id` = `category_movie`.`category_id`) or exists (select * from `posts` inner join `category_serie` on `posts`.`id` = `category_serie`.`serie_id` where `categories`.`id` = `category_serie`.`category_id`)) at C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(738): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(722): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\xampp\\htdocs\\ffff\\app\\Http\\Controllers\\HomeController.php(70): Illuminate\\Database\\Eloquent\\Builder->get()
#9 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\HomeController->index()
#10 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#11 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomeController), 'index')
#12 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#13 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#14 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#53 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\ffff\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\ffff\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#56 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'arabjmqf_blockter.category_movie' doesn't exist at C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:423)
[stacktrace]
#0 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): PDO->prepare('select * from `...')
#1 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#5 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(738): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(722): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#10 C:\\xampp\\htdocs\\ffff\\app\\Http\\Controllers\\HomeController.php(70): Illuminate\\Database\\Eloquent\\Builder->get()
#11 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\HomeController->index()
#12 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#13 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomeController), 'index')
#14 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#15 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#16 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#31 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#40 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#55 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\ffff\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#57 C:\\xampp\\htdocs\\ffff\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#58 {main}
"} 
[2025-06-09 20:26:17] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'arabjmqf_blockter.category_movie' doesn't exist (Connection: mysql, SQL: select * from `categories` where exists (select * from `posts` inner join `category_movie` on `posts`.`id` = `category_movie`.`movie_id` where `categories`.`id` = `category_movie`.`category_id`) or exists (select * from `posts` inner join `category_serie` on `posts`.`id` = `category_serie`.`serie_id` where `categories`.`id` = `category_serie`.`category_id`)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'arabjmqf_blockter.category_movie' doesn't exist (Connection: mysql, SQL: select * from `categories` where exists (select * from `posts` inner join `category_movie` on `posts`.`id` = `category_movie`.`movie_id` where `categories`.`id` = `category_movie`.`category_id`) or exists (select * from `posts` inner join `category_serie` on `posts`.`id` = `category_serie`.`serie_id` where `categories`.`id` = `category_serie`.`category_id`)) at C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(738): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(722): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\xampp\\htdocs\\ffff\\app\\Http\\Controllers\\HomeController.php(70): Illuminate\\Database\\Eloquent\\Builder->get()
#9 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\HomeController->index()
#10 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#11 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomeController), 'index')
#12 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#13 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#14 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#53 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\ffff\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\ffff\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#56 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'arabjmqf_blockter.category_movie' doesn't exist at C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:423)
[stacktrace]
#0 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): PDO->prepare('select * from `...')
#1 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#5 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(738): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(722): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#10 C:\\xampp\\htdocs\\ffff\\app\\Http\\Controllers\\HomeController.php(70): Illuminate\\Database\\Eloquent\\Builder->get()
#11 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\HomeController->index()
#12 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#13 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomeController), 'index')
#14 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#15 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#16 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#31 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#40 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#55 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\ffff\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#57 C:\\xampp\\htdocs\\ffff\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#58 {main}
"} 
[2025-06-09 20:26:40] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'arabjmqf_blockter.category_movie' doesn't exist (Connection: mysql, SQL: select * from `categories` where exists (select * from `posts` inner join `category_movie` on `posts`.`id` = `category_movie`.`movie_id` where `categories`.`id` = `category_movie`.`category_id`) or exists (select * from `posts` inner join `category_serie` on `posts`.`id` = `category_serie`.`serie_id` where `categories`.`id` = `category_serie`.`category_id`)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'arabjmqf_blockter.category_movie' doesn't exist (Connection: mysql, SQL: select * from `categories` where exists (select * from `posts` inner join `category_movie` on `posts`.`id` = `category_movie`.`movie_id` where `categories`.`id` = `category_movie`.`category_id`) or exists (select * from `posts` inner join `category_serie` on `posts`.`id` = `category_serie`.`serie_id` where `categories`.`id` = `category_serie`.`category_id`)) at C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(738): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(722): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\xampp\\htdocs\\ffff\\app\\Http\\Controllers\\HomeController.php(70): Illuminate\\Database\\Eloquent\\Builder->get()
#9 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\HomeController->index()
#10 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#11 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomeController), 'index')
#12 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#13 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#14 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#53 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\ffff\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\ffff\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#56 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'arabjmqf_blockter.category_movie' doesn't exist at C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:423)
[stacktrace]
#0 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): PDO->prepare('select * from `...')
#1 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#5 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(738): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(722): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#10 C:\\xampp\\htdocs\\ffff\\app\\Http\\Controllers\\HomeController.php(70): Illuminate\\Database\\Eloquent\\Builder->get()
#11 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\HomeController->index()
#12 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#13 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomeController), 'index')
#14 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#15 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#16 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#31 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#40 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#55 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\ffff\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#57 C:\\xampp\\htdocs\\ffff\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#58 {main}
"} 
[2025-06-09 20:30:13] local.ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'year' in 'field list' (Connection: mysql, SQL: select distinct `year` from `posts` where `year` is not null order by `year` desc) {"view":{"view":"C:\\xampp\\htdocs\\ffff\\resources\\views\\layouts\\main.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-352296382 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1554</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-352296382\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","Genres":"<pre class=sf-dump id=sf-dump-1271382256 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Database\\Eloquent\\Collection</span> {<a class=sf-dump-ref>#1566</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-1271382256\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","Latest_Movies":"<pre class=sf-dump id=sf-dump-422960557 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Database\\Eloquent\\Collection</span> {<a class=sf-dump-ref>#1562</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-422960557\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","Latest_sliders":"<pre class=sf-dump id=sf-dump-1503175987 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Pagination\\Paginator</span> {<a class=sf-dump-ref>#1573</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref>#1574</a><samp data-depth=2 class=sf-dump-compact>
    #<span class=sf-dump-protected title=\"Protected property\">items</span>: []
    #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  </samp>}
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  #<span class=sf-dump-protected title=\"Protected property\">currentPage</span>: <span class=sf-dump-num>1</span>
  #<span class=sf-dump-protected title=\"Protected property\">path</span>: \"<span class=sf-dump-str title=\"24 characters\">http://127.0.0.1:8000/ar</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">query</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fragment</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">pageName</span>: \"<span class=sf-dump-str title=\"4 characters\">page</span>\"
  +<span class=sf-dump-public title=\"Public property\">onEachSide</span>: <span class=sf-dump-num>3</span>
  #<span class=sf-dump-protected title=\"Protected property\">options</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"24 characters\">http://127.0.0.1:8000/ar</span>\"
    \"<span class=sf-dump-key>pageName</span>\" => \"<span class=sf-dump-str title=\"4 characters\">page</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">hasMore</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-1503175987\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","oldest_sliders":"<pre class=sf-dump id=sf-dump-1964453435 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Pagination\\Paginator</span> {<a class=sf-dump-ref>#1576</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref>#1577</a><samp data-depth=2 class=sf-dump-compact>
    #<span class=sf-dump-protected title=\"Protected property\">items</span>: []
    #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  </samp>}
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  #<span class=sf-dump-protected title=\"Protected property\">currentPage</span>: <span class=sf-dump-num>1</span>
  #<span class=sf-dump-protected title=\"Protected property\">path</span>: \"<span class=sf-dump-str title=\"24 characters\">http://127.0.0.1:8000/ar</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">query</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fragment</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">pageName</span>: \"<span class=sf-dump-str title=\"4 characters\">page</span>\"
  +<span class=sf-dump-public title=\"Public property\">onEachSide</span>: <span class=sf-dump-num>3</span>
  #<span class=sf-dump-protected title=\"Protected property\">options</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"24 characters\">http://127.0.0.1:8000/ar</span>\"
    \"<span class=sf-dump-key>pageName</span>\" => \"<span class=sf-dump-str title=\"4 characters\">page</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">hasMore</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-1964453435\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","Locale":"<pre class=sf-dump id=sf-dump-626267428 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"2 characters\">ar</span>\"
</pre><script>Sfdump(\"sf-dump-626267428\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","__currentLoopData":"<pre class=sf-dump id=sf-dump-59851828 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Database\\Eloquent\\Collection</span> {<a class=sf-dump-ref>#1566</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-59851828\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","loop":"<pre class=sf-dump id=sf-dump-712931901 data-indent-pad=\"  \"><span class=sf-dump-const>null</span>
</pre><script>Sfdump(\"sf-dump-712931901\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'year' in 'field list' (Connection: mysql, SQL: select distinct `year` from `posts` where `year` is not null order by `year` desc) at C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select distinct...', Array, Object(Closure))
#1 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select distinct...', Array, Object(Closure))
#2 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select distinct...', Array, true)
#3 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3100): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3096): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(888): Illuminate\\Database\\Query\\Builder->pluck('year', NULL)
#7 C:\\xampp\\htdocs\\ffff\\app\\Helpers\\Helper.php(105): Illuminate\\Database\\Eloquent\\Builder->pluck('year')
#8 C:\\xampp\\htdocs\\ffff\\resources\\views\\layouts\\main.blade.php(764): getYears()
#9 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\xampp\\\\htdocs...')
#10 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#11 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\xampp\\\\htdocs...', Array)
#12 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#13 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#14 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#15 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#16 C:\\xampp\\htdocs\\ffff\\resources\\views\\Pages\\home.blade.php(405): Illuminate\\View\\View->render()
#17 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\xampp\\\\htdocs...')
#18 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#19 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\xampp\\\\htdocs...', Array)
#20 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#21 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#22 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#23 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#24 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#25 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#26 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#27 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#28 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#29 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#44 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#53 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#68 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#69 C:\\xampp\\htdocs\\ffff\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#70 C:\\xampp\\htdocs\\ffff\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#71 {main}

[previous exception] [object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'year' in 'field list' (Connection: mysql, SQL: select distinct `year` from `posts` where `year` is not null order by `year` desc) at C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select distinct...', Array, Object(Closure))
#1 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select distinct...', Array, Object(Closure))
#2 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select distinct...', Array, true)
#3 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3100): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3096): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(888): Illuminate\\Database\\Query\\Builder->pluck('year', NULL)
#7 C:\\xampp\\htdocs\\ffff\\app\\Helpers\\Helper.php(105): Illuminate\\Database\\Eloquent\\Builder->pluck('year')
#8 C:\\xampp\\htdocs\\ffff\\storage\\framework\\views\\5e6a94959d90f2fb45fbfd7642f4559f.php(766): getYears()
#9 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\xampp\\\\htdocs...')
#10 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#11 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\xampp\\\\htdocs...', Array)
#12 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#13 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#14 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#15 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#16 C:\\xampp\\htdocs\\ffff\\storage\\framework\\views\\b0dbca02aab7608da506ca2d5a83b7ac.php(404): Illuminate\\View\\View->render()
#17 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\xampp\\\\htdocs...')
#18 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#19 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\xampp\\\\htdocs...', Array)
#20 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#21 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#22 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#23 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#24 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#25 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#26 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#27 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#28 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#29 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#44 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#53 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#68 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#69 C:\\xampp\\htdocs\\ffff\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#70 C:\\xampp\\htdocs\\ffff\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#71 {main}

[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'year' in 'field list' at C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:423)
[stacktrace]
#0 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): PDO->prepare('select distinct...')
#1 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select distinct...', Array)
#2 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select distinct...', Array, Object(Closure))
#3 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select distinct...', Array, Object(Closure))
#4 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select distinct...', Array, true)
#5 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3100): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3096): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(888): Illuminate\\Database\\Query\\Builder->pluck('year', NULL)
#9 C:\\xampp\\htdocs\\ffff\\app\\Helpers\\Helper.php(105): Illuminate\\Database\\Eloquent\\Builder->pluck('year')
#10 C:\\xampp\\htdocs\\ffff\\storage\\framework\\views\\5e6a94959d90f2fb45fbfd7642f4559f.php(766): getYears()
#11 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\xampp\\\\htdocs...')
#12 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#13 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\xampp\\\\htdocs...', Array)
#14 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#15 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#16 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#17 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#18 C:\\xampp\\htdocs\\ffff\\storage\\framework\\views\\b0dbca02aab7608da506ca2d5a83b7ac.php(404): Illuminate\\View\\View->render()
#19 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\xampp\\\\htdocs...')
#20 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#21 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\xampp\\\\htdocs...', Array)
#22 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#23 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#24 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#25 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#26 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#27 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#28 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#29 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#30 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#31 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#46 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#53 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#55 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#57 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#70 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#71 C:\\xampp\\htdocs\\ffff\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#72 C:\\xampp\\htdocs\\ffff\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#73 {main}
"} 
[2025-06-09 20:30:35] local.ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'year' in 'field list' (Connection: mysql, SQL: select distinct `year` from `posts` where `year` is not null order by `year` desc) {"view":{"view":"C:\\xampp\\htdocs\\ffff\\resources\\views\\layouts\\main.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-1504823292 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1554</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-1504823292\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","Genres":"<pre class=sf-dump id=sf-dump-638257454 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Database\\Eloquent\\Collection</span> {<a class=sf-dump-ref>#1566</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-638257454\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","Latest_Movies":"<pre class=sf-dump id=sf-dump-195025913 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Database\\Eloquent\\Collection</span> {<a class=sf-dump-ref>#1562</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-195025913\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","Latest_sliders":"<pre class=sf-dump id=sf-dump-180460223 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Pagination\\Paginator</span> {<a class=sf-dump-ref>#1573</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref>#1574</a><samp data-depth=2 class=sf-dump-compact>
    #<span class=sf-dump-protected title=\"Protected property\">items</span>: []
    #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  </samp>}
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  #<span class=sf-dump-protected title=\"Protected property\">currentPage</span>: <span class=sf-dump-num>1</span>
  #<span class=sf-dump-protected title=\"Protected property\">path</span>: \"<span class=sf-dump-str title=\"24 characters\">http://127.0.0.1:8000/ar</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">query</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fragment</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">pageName</span>: \"<span class=sf-dump-str title=\"4 characters\">page</span>\"
  +<span class=sf-dump-public title=\"Public property\">onEachSide</span>: <span class=sf-dump-num>3</span>
  #<span class=sf-dump-protected title=\"Protected property\">options</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"24 characters\">http://127.0.0.1:8000/ar</span>\"
    \"<span class=sf-dump-key>pageName</span>\" => \"<span class=sf-dump-str title=\"4 characters\">page</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">hasMore</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-180460223\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","oldest_sliders":"<pre class=sf-dump id=sf-dump-429081438 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Pagination\\Paginator</span> {<a class=sf-dump-ref>#1576</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref>#1577</a><samp data-depth=2 class=sf-dump-compact>
    #<span class=sf-dump-protected title=\"Protected property\">items</span>: []
    #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  </samp>}
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  #<span class=sf-dump-protected title=\"Protected property\">currentPage</span>: <span class=sf-dump-num>1</span>
  #<span class=sf-dump-protected title=\"Protected property\">path</span>: \"<span class=sf-dump-str title=\"24 characters\">http://127.0.0.1:8000/ar</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">query</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fragment</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">pageName</span>: \"<span class=sf-dump-str title=\"4 characters\">page</span>\"
  +<span class=sf-dump-public title=\"Public property\">onEachSide</span>: <span class=sf-dump-num>3</span>
  #<span class=sf-dump-protected title=\"Protected property\">options</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"24 characters\">http://127.0.0.1:8000/ar</span>\"
    \"<span class=sf-dump-key>pageName</span>\" => \"<span class=sf-dump-str title=\"4 characters\">page</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">hasMore</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-429081438\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","Locale":"<pre class=sf-dump id=sf-dump-959848409 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"2 characters\">ar</span>\"
</pre><script>Sfdump(\"sf-dump-959848409\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","__currentLoopData":"<pre class=sf-dump id=sf-dump-95582264 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Database\\Eloquent\\Collection</span> {<a class=sf-dump-ref>#1566</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-95582264\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","loop":"<pre class=sf-dump id=sf-dump-1235876364 data-indent-pad=\"  \"><span class=sf-dump-const>null</span>
</pre><script>Sfdump(\"sf-dump-1235876364\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'year' in 'field list' (Connection: mysql, SQL: select distinct `year` from `posts` where `year` is not null order by `year` desc) at C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select distinct...', Array, Object(Closure))
#1 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select distinct...', Array, Object(Closure))
#2 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select distinct...', Array, true)
#3 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3100): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3096): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(888): Illuminate\\Database\\Query\\Builder->pluck('year', NULL)
#7 C:\\xampp\\htdocs\\ffff\\app\\Helpers\\Helper.php(105): Illuminate\\Database\\Eloquent\\Builder->pluck('year')
#8 C:\\xampp\\htdocs\\ffff\\resources\\views\\layouts\\main.blade.php(764): getYears()
#9 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\xampp\\\\htdocs...')
#10 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#11 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\xampp\\\\htdocs...', Array)
#12 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#13 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#14 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#15 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#16 C:\\xampp\\htdocs\\ffff\\resources\\views\\Pages\\home.blade.php(405): Illuminate\\View\\View->render()
#17 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\xampp\\\\htdocs...')
#18 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#19 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\xampp\\\\htdocs...', Array)
#20 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#21 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#22 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#23 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#24 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#25 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#26 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#27 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#28 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#29 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#44 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#53 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#68 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#69 C:\\xampp\\htdocs\\ffff\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#70 C:\\xampp\\htdocs\\ffff\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#71 {main}

[previous exception] [object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'year' in 'field list' (Connection: mysql, SQL: select distinct `year` from `posts` where `year` is not null order by `year` desc) at C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select distinct...', Array, Object(Closure))
#1 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select distinct...', Array, Object(Closure))
#2 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select distinct...', Array, true)
#3 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3100): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3096): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(888): Illuminate\\Database\\Query\\Builder->pluck('year', NULL)
#7 C:\\xampp\\htdocs\\ffff\\app\\Helpers\\Helper.php(105): Illuminate\\Database\\Eloquent\\Builder->pluck('year')
#8 C:\\xampp\\htdocs\\ffff\\storage\\framework\\views\\5e6a94959d90f2fb45fbfd7642f4559f.php(766): getYears()
#9 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\xampp\\\\htdocs...')
#10 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#11 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\xampp\\\\htdocs...', Array)
#12 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#13 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#14 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#15 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#16 C:\\xampp\\htdocs\\ffff\\storage\\framework\\views\\b0dbca02aab7608da506ca2d5a83b7ac.php(404): Illuminate\\View\\View->render()
#17 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\xampp\\\\htdocs...')
#18 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#19 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\xampp\\\\htdocs...', Array)
#20 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#21 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#22 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#23 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#24 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#25 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#26 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#27 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#28 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#29 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#44 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#53 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#68 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#69 C:\\xampp\\htdocs\\ffff\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#70 C:\\xampp\\htdocs\\ffff\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#71 {main}

[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'year' in 'field list' at C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:423)
[stacktrace]
#0 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): PDO->prepare('select distinct...')
#1 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select distinct...', Array)
#2 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select distinct...', Array, Object(Closure))
#3 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select distinct...', Array, Object(Closure))
#4 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select distinct...', Array, true)
#5 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3100): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3096): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(888): Illuminate\\Database\\Query\\Builder->pluck('year', NULL)
#9 C:\\xampp\\htdocs\\ffff\\app\\Helpers\\Helper.php(105): Illuminate\\Database\\Eloquent\\Builder->pluck('year')
#10 C:\\xampp\\htdocs\\ffff\\storage\\framework\\views\\5e6a94959d90f2fb45fbfd7642f4559f.php(766): getYears()
#11 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\xampp\\\\htdocs...')
#12 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#13 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\xampp\\\\htdocs...', Array)
#14 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#15 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#16 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#17 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#18 C:\\xampp\\htdocs\\ffff\\storage\\framework\\views\\b0dbca02aab7608da506ca2d5a83b7ac.php(404): Illuminate\\View\\View->render()
#19 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\xampp\\\\htdocs...')
#20 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#21 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\xampp\\\\htdocs...', Array)
#22 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#23 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#24 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#25 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#26 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#27 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#28 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#29 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#30 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#31 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#46 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#53 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#55 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#57 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#70 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#71 C:\\xampp\\htdocs\\ffff\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#72 C:\\xampp\\htdocs\\ffff\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#73 {main}
"} 
[2025-06-09 20:34:16] local.ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'year' in 'field list' (Connection: mysql, SQL: select distinct `year` from `posts` where `year` is not null order by `year` desc) {"view":{"view":"C:\\xampp\\htdocs\\ffff\\resources\\views\\layouts\\main.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-22953213 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1554</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-22953213\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","Genres":"<pre class=sf-dump id=sf-dump-1251952659 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Database\\Eloquent\\Collection</span> {<a class=sf-dump-ref>#1565</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"App\\Category
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Category</span> {<a class=sf-dump-ref href=#sf-dump-1251952659-ref21584 title=\"2 occurrences\">#1584</a><samp data-depth=3 id=sf-dump-1251952659-ref21584 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"10 characters\">categories</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [ &#8230;8]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    <span class=sf-dump-index>1</span> => <span class=sf-dump-note title=\"App\\Category
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Category</span> {<a class=sf-dump-ref href=#sf-dump-1251952659-ref21582 title=\"2 occurrences\">#1582</a><samp data-depth=3 id=sf-dump-1251952659-ref21582 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"10 characters\">categories</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [ &#8230;8]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    <span class=sf-dump-index>2</span> => <span class=sf-dump-note title=\"App\\Category
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Category</span> {<a class=sf-dump-ref href=#sf-dump-1251952659-ref21586 title=\"2 occurrences\">#1586</a><samp data-depth=3 id=sf-dump-1251952659-ref21586 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"10 characters\">categories</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [ &#8230;8]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-1251952659\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","Latest_Movies":"<pre class=sf-dump id=sf-dump-1547307717 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Database\\Eloquent\\Collection</span> {<a class=sf-dump-ref>#1562</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-1547307717\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","Latest_sliders":"<pre class=sf-dump id=sf-dump-1115312846 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Pagination\\Paginator</span> {<a class=sf-dump-ref>#1573</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref>#1574</a><samp data-depth=2 class=sf-dump-compact>
    #<span class=sf-dump-protected title=\"Protected property\">items</span>: []
    #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  </samp>}
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  #<span class=sf-dump-protected title=\"Protected property\">currentPage</span>: <span class=sf-dump-num>1</span>
  #<span class=sf-dump-protected title=\"Protected property\">path</span>: \"<span class=sf-dump-str title=\"24 characters\">http://127.0.0.1:8000/ar</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">query</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fragment</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">pageName</span>: \"<span class=sf-dump-str title=\"4 characters\">page</span>\"
  +<span class=sf-dump-public title=\"Public property\">onEachSide</span>: <span class=sf-dump-num>3</span>
  #<span class=sf-dump-protected title=\"Protected property\">options</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"24 characters\">http://127.0.0.1:8000/ar</span>\"
    \"<span class=sf-dump-key>pageName</span>\" => \"<span class=sf-dump-str title=\"4 characters\">page</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">hasMore</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-1115312846\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","oldest_sliders":"<pre class=sf-dump id=sf-dump-1433573492 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Pagination\\Paginator</span> {<a class=sf-dump-ref>#1576</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref>#1577</a><samp data-depth=2 class=sf-dump-compact>
    #<span class=sf-dump-protected title=\"Protected property\">items</span>: []
    #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  </samp>}
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  #<span class=sf-dump-protected title=\"Protected property\">currentPage</span>: <span class=sf-dump-num>1</span>
  #<span class=sf-dump-protected title=\"Protected property\">path</span>: \"<span class=sf-dump-str title=\"24 characters\">http://127.0.0.1:8000/ar</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">query</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fragment</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">pageName</span>: \"<span class=sf-dump-str title=\"4 characters\">page</span>\"
  +<span class=sf-dump-public title=\"Public property\">onEachSide</span>: <span class=sf-dump-num>3</span>
  #<span class=sf-dump-protected title=\"Protected property\">options</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"24 characters\">http://127.0.0.1:8000/ar</span>\"
    \"<span class=sf-dump-key>pageName</span>\" => \"<span class=sf-dump-str title=\"4 characters\">page</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">hasMore</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-1433573492\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","Locale":"<pre class=sf-dump id=sf-dump-1314747842 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"2 characters\">ar</span>\"
</pre><script>Sfdump(\"sf-dump-1314747842\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","__currentLoopData":"<pre class=sf-dump id=sf-dump-1672445012 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Database\\Eloquent\\Collection</span> {<a class=sf-dump-ref>#1589</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-1672445012\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","loop":"<pre class=sf-dump id=sf-dump-1774832348 data-indent-pad=\"  \"><span class=sf-dump-const>null</span>
</pre><script>Sfdump(\"sf-dump-1774832348\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","Genre":"<pre class=sf-dump id=sf-dump-1974946070 data-indent-pad=\"  \"><span class=sf-dump-note>App\\Category</span> {<a class=sf-dump-ref href=#sf-dump-1974946070-ref21586 title=\"2 occurrences\">#1586</a><samp data-depth=1 id=sf-dump-1974946070-ref21586 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"10 characters\">categories</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>Title_ar</span>\" => \"<span class=sf-dump-str title=\"8 characters\">&#1576;&#1579; &#1605;&#1576;&#1575;&#1588;&#1585;</span>\"
    \"<span class=sf-dump-key>Title_en</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Live TV</span>\"
    \"<span class=sf-dump-key>Title_fr</span>\" => \"<span class=sf-dump-str title=\"12 characters\">TV en direct</span>\"
    \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"7 characters\">live-tv</span>\"
    \"<span class=sf-dump-key>color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#0000ff</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-09 20:33:38</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-09 20:33:38</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>Title_ar</span>\" => \"<span class=sf-dump-str title=\"8 characters\">&#1576;&#1579; &#1605;&#1576;&#1575;&#1588;&#1585;</span>\"
    \"<span class=sf-dump-key>Title_en</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Live TV</span>\"
    \"<span class=sf-dump-key>Title_fr</span>\" => \"<span class=sf-dump-str title=\"12 characters\">TV en direct</span>\"
    \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"7 characters\">live-tv</span>\"
    \"<span class=sf-dump-key>color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#0000ff</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-09 20:33:38</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-09 20:33:38</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>movies</span>\" => <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref>#1599</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">parent_id</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"5 characters\">order</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">Title_ar</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">Title_en</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">Title_fr</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"5 characters\">color</span>\"
    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"14 characters\">ImageUpload_id</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
</samp>}
</pre><script>Sfdump(\"sf-dump-1974946070\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","posts":"<pre class=sf-dump id=sf-dump-1066154704 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Database\\Eloquent\\Collection</span> {<a class=sf-dump-ref>#1589</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-1066154704\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'year' in 'field list' (Connection: mysql, SQL: select distinct `year` from `posts` where `year` is not null order by `year` desc) at C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select distinct...', Array, Object(Closure))
#1 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select distinct...', Array, Object(Closure))
#2 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select distinct...', Array, true)
#3 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3100): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3096): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(888): Illuminate\\Database\\Query\\Builder->pluck('year', NULL)
#7 C:\\xampp\\htdocs\\ffff\\app\\Helpers\\Helper.php(105): Illuminate\\Database\\Eloquent\\Builder->pluck('year')
#8 C:\\xampp\\htdocs\\ffff\\resources\\views\\layouts\\main.blade.php(764): getYears()
#9 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\xampp\\\\htdocs...')
#10 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#11 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\xampp\\\\htdocs...', Array)
#12 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#13 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#14 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#15 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#16 C:\\xampp\\htdocs\\ffff\\resources\\views\\Pages\\home.blade.php(405): Illuminate\\View\\View->render()
#17 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\xampp\\\\htdocs...')
#18 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#19 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\xampp\\\\htdocs...', Array)
#20 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#21 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#22 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#23 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#24 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#25 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#26 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#27 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#28 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#29 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#44 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#53 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#68 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#69 C:\\xampp\\htdocs\\ffff\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#70 C:\\xampp\\htdocs\\ffff\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#71 {main}

[previous exception] [object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'year' in 'field list' (Connection: mysql, SQL: select distinct `year` from `posts` where `year` is not null order by `year` desc) at C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select distinct...', Array, Object(Closure))
#1 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select distinct...', Array, Object(Closure))
#2 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select distinct...', Array, true)
#3 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3100): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3096): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(888): Illuminate\\Database\\Query\\Builder->pluck('year', NULL)
#7 C:\\xampp\\htdocs\\ffff\\app\\Helpers\\Helper.php(105): Illuminate\\Database\\Eloquent\\Builder->pluck('year')
#8 C:\\xampp\\htdocs\\ffff\\storage\\framework\\views\\5e6a94959d90f2fb45fbfd7642f4559f.php(766): getYears()
#9 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\xampp\\\\htdocs...')
#10 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#11 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\xampp\\\\htdocs...', Array)
#12 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#13 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#14 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#15 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#16 C:\\xampp\\htdocs\\ffff\\storage\\framework\\views\\b0dbca02aab7608da506ca2d5a83b7ac.php(404): Illuminate\\View\\View->render()
#17 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\xampp\\\\htdocs...')
#18 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#19 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\xampp\\\\htdocs...', Array)
#20 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#21 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#22 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#23 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#24 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#25 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#26 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#27 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#28 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#29 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#44 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#53 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#68 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#69 C:\\xampp\\htdocs\\ffff\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#70 C:\\xampp\\htdocs\\ffff\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#71 {main}

[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'year' in 'field list' at C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:423)
[stacktrace]
#0 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): PDO->prepare('select distinct...')
#1 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select distinct...', Array)
#2 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select distinct...', Array, Object(Closure))
#3 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select distinct...', Array, Object(Closure))
#4 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select distinct...', Array, true)
#5 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3100): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3096): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(888): Illuminate\\Database\\Query\\Builder->pluck('year', NULL)
#9 C:\\xampp\\htdocs\\ffff\\app\\Helpers\\Helper.php(105): Illuminate\\Database\\Eloquent\\Builder->pluck('year')
#10 C:\\xampp\\htdocs\\ffff\\storage\\framework\\views\\5e6a94959d90f2fb45fbfd7642f4559f.php(766): getYears()
#11 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\xampp\\\\htdocs...')
#12 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#13 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\xampp\\\\htdocs...', Array)
#14 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#15 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#16 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#17 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#18 C:\\xampp\\htdocs\\ffff\\storage\\framework\\views\\b0dbca02aab7608da506ca2d5a83b7ac.php(404): Illuminate\\View\\View->render()
#19 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\xampp\\\\htdocs...')
#20 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#21 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\xampp\\\\htdocs...', Array)
#22 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#23 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#24 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#25 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#26 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#27 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#28 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#29 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#30 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#31 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\ffff\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#46 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#53 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#55 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#57 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#70 C:\\xampp\\htdocs\\ffff\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#71 C:\\xampp\\htdocs\\ffff\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#72 C:\\xampp\\htdocs\\ffff\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#73 {main}
"} 
