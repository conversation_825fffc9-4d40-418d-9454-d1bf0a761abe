<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار بسيط - فلكسي TV</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            padding: 20px;
            margin: 0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .button {
            background: #dc2626;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            margin: 10px;
            cursor: pointer;
            transition: background 0.3s;
        }
        .button:hover {
            background: #b91c1c;
        }
        .channels-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .channel-card {
            background: #374151;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: transform 0.2s;
        }
        .channel-card:hover {
            transform: scale(1.05);
            background: #4B5563;
        }
        .channel-logo {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 10px;
        }
        .player {
            width: 100%;
            max-width: 800px;
            margin: 20px auto;
            background: #000;
            border-radius: 8px;
            overflow: hidden;
        }
        .player video {
            width: 100%;
            height: auto;
        }
        .hidden {
            display: none;
        }
        .status {
            background: #065f46;
            color: white;
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 اختبار بسيط - فلكسي TV</h1>
        
        <div class="status" id="status">
            جاهز للاختبار
        </div>
        
        <div>
            <button class="button" onclick="loadTestChannels()">
                📺 تحميل قنوات تجريبية
            </button>
            
            <button class="button" onclick="loadRealChannels()">
                🔄 تحميل القنوات الحقيقية
            </button>
            
            <button class="button" onclick="testPlayer()">
                🎬 اختبار المشغل
            </button>
            
            <button class="button" onclick="clearAll()">
                🗑️ مسح الكل
            </button>
        </div>
        
        <div id="channels" class="channels-grid"></div>
        
        <div id="player" class="player hidden">
            <video id="videoElement" controls>
                متصفحك لا يدعم تشغيل الفيديو
            </video>
        </div>
    </div>

    <script>
        let channels = [];
        
        // قنوات تجريبية مضمونة العمل
        const testChannels = [
            {
                title: 'Big Buck Bunny',
                logo: 'https://via.placeholder.com/100x100/FF6B6B/FFFFFF?text=TEST1',
                url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4'
            },
            {
                title: 'Elephant Dream',
                logo: 'https://via.placeholder.com/100x100/4ECDC4/FFFFFF?text=TEST2',
                url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4'
            },
            {
                title: 'Sample Video',
                logo: 'https://via.placeholder.com/100x100/45B7D1/FFFFFF?text=TEST3',
                url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4'
            }
        ];
        
        function updateStatus(message, color = '#065f46') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.style.background = color;
            console.log('📢', message);
        }
        
        function loadTestChannels() {
            updateStatus('تحميل القنوات التجريبية...', '#1d4ed8');
            channels = testChannels;
            displayChannels();
            updateStatus('✅ تم تحميل ' + channels.length + ' قنوات تجريبية', '#065f46');
        }
        
        function loadRealChannels() {
            updateStatus('تحميل القنوات الحقيقية...', '#1d4ed8');
            
            const url = "https://www.arabseed.stream/storage/m3u/playlist_68203bbd9e6114.72123431.m3u";
            
            fetch(url)
                .then(response => {
                    if (!response.ok) throw new Error('فشل في تحميل الملف');
                    return response.text();
                })
                .then(content => {
                    console.log('📄 تم تحميل الملف، الحجم:', content.length);
                    parseM3U(content);
                    updateStatus('✅ تم تحميل ' + channels.length + ' قناة حقيقية', '#065f46');
                })
                .catch(error => {
                    console.error('❌ خطأ:', error);
                    updateStatus('❌ فشل تحميل القنوات الحقيقية: ' + error.message, '#dc2626');
                    loadTestChannels(); // تحميل تجريبية كبديل
                });
        }
        
        function parseM3U(content) {
            const lines = content.split('\n');
            const parsedChannels = [];
            
            for (let i = 0; i < lines.length; i++) {
                if (lines[i].startsWith('#EXTINF')) {
                    const title = lines[i].split(',')[1]?.trim() || 'بدون اسم';
                    const logo = lines[i].match(/tvg-logo="(.*?)"/)?.[1] || 'https://via.placeholder.com/100x100/6B7280/FFFFFF?text=📺';
                    const url = lines[i + 1]?.trim();
                    
                    if (url && url.startsWith('http')) {
                        parsedChannels.push({ title, logo, url });
                    }
                }
            }
            
            channels = parsedChannels;
            displayChannels();
            console.log('📺 تم تحليل', channels.length, 'قناة');
        }
        
        function displayChannels() {
            const container = document.getElementById('channels');
            container.innerHTML = '';
            
            if (channels.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: #9CA3AF;">لا توجد قنوات</p>';
                return;
            }
            
            channels.forEach((channel, index) => {
                const card = document.createElement('div');
                card.className = 'channel-card';
                card.innerHTML = `
                    <img src="${channel.logo}" class="channel-logo" 
                         onerror="this.src='https://via.placeholder.com/100x100/6B7280/FFFFFF?text=📺'" />
                    <div style="font-weight: bold; margin-bottom: 5px;">${channel.title}</div>
                    <div style="font-size: 12px; color: #9CA3AF;">قناة ${index + 1}</div>
                `;
                
                card.addEventListener('click', () => playChannel(channel));
                container.appendChild(card);
            });
            
            console.log('✅ تم عرض', channels.length, 'قناة');
        }
        
        function playChannel(channel) {
            updateStatus('🎬 تشغيل: ' + channel.title, '#7c3aed');
            
            const player = document.getElementById('player');
            const video = document.getElementById('videoElement');
            
            player.classList.remove('hidden');
            video.src = channel.url;
            video.load();
            
            video.play()
                .then(() => {
                    updateStatus('✅ يتم تشغيل: ' + channel.title, '#065f46');
                    player.scrollIntoView({ behavior: 'smooth' });
                })
                .catch(error => {
                    console.error('❌ خطأ في التشغيل:', error);
                    updateStatus('❌ فشل تشغيل: ' + channel.title, '#dc2626');
                });
        }
        
        function testPlayer() {
            const testVideo = {
                title: 'فيديو اختبار',
                url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4'
            };
            playChannel(testVideo);
        }
        
        function clearAll() {
            channels = [];
            document.getElementById('channels').innerHTML = '';
            document.getElementById('player').classList.add('hidden');
            document.getElementById('videoElement').src = '';
            updateStatus('تم مسح الكل', '#6B7280');
        }
        
        // تحميل تلقائي عند فتح الصفحة
        window.onload = function() {
            updateStatus('مرحباً! اختر نوع القنوات للتحميل', '#1d4ed8');
            console.log('🎯 الصفحة جاهزة للاختبار');
        };
    </script>
</body>
</html>
