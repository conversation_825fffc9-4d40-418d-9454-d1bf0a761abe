use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

public function uploadApk(Request $request)
{
    $request->validate([
        'apk_file' => 'required|file|mimes:apk|max:102400',
    ]);

    // حفظ الملف
    $path = $request->file('apk_file')->store('public/apks');

    // حفظ اسم الملف في ملف config أو JSON أو cache
    $fileName = basename($path);
    file_put_contents(storage_path('app/public/apks/latest_apk.txt'), $fileName);

    return response()->json([
        'message' => 'تم رفع الملف بنجاح',
        'url' => asset(Storage::url($path))
    ]);
}
