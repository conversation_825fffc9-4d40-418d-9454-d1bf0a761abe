<?php

namespace App\Http\Controllers\Dashboard;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Requests\StorePost;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\ImageUpload;
use App\Client; 
use App\Serie;  
use App\Audio;
use App\Season;
use Auth;
use File;
use Validator;

class Seasons extends Controller
{  

    /**
     * 
     * Show the middleware dashboard Super Admin.
     *
     * @return \Illuminate\Contracts\Support\Renderable.
     * 
     */
    public function __construct()
    {
        $this->middleware(['auth','role_or_permission:Super-Admin|Supervisors']);
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $query = Season::query()->latest()->with('Serie');
    
        // Apply search filter
        if ($request->has('search') && $request->input('search') != '') {
            $searchTerm = $request->input('search');
    
            $query->where('Title_en', 'LIKE', "%{$searchTerm}%") // Search in Season title
                  ->orWhereHas('Serie', function ($q) use ($searchTerm) {
                      // Search in the related Serie model
                      $q->where('Title_en', 'LIKE', "%{$searchTerm}%");
                  })
                  ->orWhere('position', 'LIKE', "%{$searchTerm}%");
        }
    
        // Paginate the filtered results
        $Seasons = $query->simplePaginate(10);
    
        return view('Dashboard.Seasons.index', compact('Seasons'));
    }
    

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {    
        // Get the 'id_serie' query parameter
        $id_serie = request('id_serie');
        $title_serie=null;
        if($id_serie){
            $title_serie=Serie::find($id_serie)->Title_en;
        }
    
        // Get all Series
        $Series = Serie::all();
    
        // Pass $Series and $id_serie to the view
        return view('Dashboard.Seasons.create', compact('Series', 'id_serie',"title_serie"));
    }
    
 
    /**
     * 
     * Store a newly Created Resource in Storage.
     *
     * @param  \Illuminate\Http\Request  $request.
     * @return \Illuminate\Http\Response
     * 
     */

    public function store(Request $request)
    {
        // GET validate
        $request->validate([
        'id_serie' => 'required',
        'Title_en' => 'required',
        'body_en' => 'required',
        ]);

        $ImageUpload = ImageUpload::max('id');

        Season::create([
            'id_serie' => $request->id_serie,  
            "position"=>$request->position,
            'Title_ar' => $request->Title_ar,  
            'Title_en' => $request->Title_en, 
            'Title_fr' => $request->Title_fr,
            'body_ar' => $request->body_ar,  
            'body_en' => $request->body_en,
            'body_fr' => $request->body_fr, 
            'featured' => $request->featured == null ? "of": "on"  ,
            'ImageUpload_id' => $ImageUpload,
            "year"=>$request->year
        ]);

            return redirect()->route('Seasons.index')->with('success','Season Store successfully.');
    }

    /**
     * Show the form for editing the specified Resource.
     *
     * @param  int  $slug
     * @return \Illuminate\Http\Response
     */
    public function edit($slug)
    {
        //To Get All Series 
        $Season = Season::where('slug', '=', $slug)->firstOrFail();
        //To Get All Series 
        $Series = Serie::all();
        $ImageUpload = ImageUpload::max('id') + 1;
        return view('Dashboard.Seasons.edit',compact('Season','Series',"ImageUpload"));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response 
     */
    public function update(Request $request, $slug)
    {
        // GET Series.
        $Season = Season::where('slug', '=', $slug)->firstOrFail();
        
        // GET validate.
        $data = $request->validate([
        'id_serie' => 'required',
        'Title_en' => 'required',
        'body_en' => 'required',
        ]);
        
        $Season->id_serie = $request->input('id_serie');
        $Season->ImageUpload_id = $request->input('ImageUpload_id');
        $Season->Title_ar = $request->input('Title_ar');
        $Season->Title_en = $request->input('Title_en');
        $Season->Title_fr = $request->input('Title_fr');
        $Season->body_ar = $request->input('body_ar');
        $Season->body_en = $request->input('body_en');
        $Season->body_fr = $request->input('body_fr');
        $Season->featured = $request->input('featured') == null ? "of":"on";
        $Season->id_serie=$request->input('id_serie');
        $Season->position=$request->input('position');
        $Season->year=$request->input("year");
        $Season->save();
        return redirect()->route('Seasons.index')->with('success','Season Updated successfully.');
    }

    /**
     * 
     * Remove the specified resource from storage google Drive.
     *
     * @param  int  $id.
     * @return \Illuminate\Http\Response.
     * 
     */
    public function destroy($id)
    {
        // Post Delete.
        $Season = Season::findOrFail($id);
        // Delete all associated episodes.
        $Season->Episodes()->delete();
        $Season->delete();
        return back()->with('Delete','Season deleted successfully');
    }

    public function updateFeatured(Request $request)
{
    $Season = Season::find($request->id); // Find the Season by ID
    if ($Season) {
        $Season->featured = $request->featured == null ? "of" :"on" ; // Update the featured status
        $Season->save(); // Save the changes to the database
        return redirect()->route('Seasons.index')->with('success','Season Updated successfully.');
    }

    return redirect()->route('Seasons.index')->with('success','Season Updated successfully.');
}
}
