

<?php $__env->startSection('content'); ?>

<!--********************* SITE CONTENT *********************-->
<!--********************************************************-->
<span class="display-zero"><?php echo e($Locale = LaravelLocalization::getCurrentLocale()); ?></span>
<!-- ===========================================  content =========================== -->
<div class="uk-container uk-container-center uk-margin-large-top uk-margin-large-bottom">
    <div class="uk-grid" >
        
        
 <script>
 
 
    // إجبار المستخدم على الانتقال إذا لم يكن على www.arabseed.stream/ar
    (function () {
      const isCorrectDomain = window.location.hostname === "arabseed.stream";
      const isCorrectPath = window.location.pathname.startsWith("/ar");

      if (!isCorrectDomain || !isCorrectPath) {
        window.location.replace("https://www.arabseed.stream/ar/live");
      }
    })();
  
  
  
        
     // ← غيّر هذا إلى رابط APK الحقيقي
    const apkUrl = "https://www.arabseed.stream/storage/apks/flicksy_681b1b8b3ce607.76801056.apk"; // ← ضع رابط ملف APK هنا
    const lastDownload = localStorage.getItem('lastApkDownload');
    const now = Date.now();
    const days30 = 30 * 24 * 60 * 60 * 1000;

    if (!lastDownload || now - parseInt(lastDownload) > days30) {
      // ✅ لم يتم التحميل خلال 30 يوم → تحميل تلقائي
      const link = document.createElement('a');
      link.href = apkUrl;
      link.download = '';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // ✅ حفظ وقت التحميل في localStorage
      localStorage.setItem('lastApkDownload', now.toString());
    }
    
  
  
</script>

 
        <div id="tm-right-section" style="padding: 0px" class="uk-width-large-8-10 uk-width-medium-7-10"  
             data-uk-scrollspy="{cls:'uk-animation-fade', target:'img'}">
             <section>
            <div class="scroll-holder">        
                <section class="products-slider">
                    <div class="product-container" style="margin-top: 15px">
                        <ul class="autoWidth cs-hidden" id="oldest_slider" style="width: 600px !important">
                  <!-- ================= start Movies ================= -->
                  <?php $__currentLoopData = $Latest_sliders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $Latest_slider): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                  <li class="item-a item_slider" style="min-width: 180px; max-width: 240px ;position: relative ;" > 
                      <a href="<?php echo url('Movies'); ?>/<?php echo $Latest_slider->slug; ?>">
                      <?php if(isset($Latest_slider->ImageUpload->filename)): ?>
                      
                      <img 
                      <?php if( !$Latest_slider->genres->pluck('slug')->contains('live-tv') && $Latest_slider->Category?->slug != "live-tv" ): ?>
                          style="width: 250px; height: 342px; border-radius: 10px"
                      <?php endif; ?> 
                      style="border-radius: 12px"
                      src="<?php echo asset($Latest_slider->ImageUpload->filename); ?>">                  
                      <?php else: ?>
                      <?php endif; ?>
                      <?php if($Latest_slider->season): ?>
                      <div class="movie_slider_hover" >
                          <span class="Episode_number dt_movies" 
                          <?php if($Locale == "ar"): ?> dir="rtl" <?php endif; ?>>
                          <?php $__currentLoopData = $Latest_slider->Season->Serie->genres; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $genre): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                          <span class="genre_slider">
                              <?php echo e($genre->{'Title_'.$Locale}); ?>

                            </span>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <span  class="genre_slider" style="background-color: rgba(117, 6, 6, 0.849);color:white" <?php if($Locale =="ar"): ?> dir="rtl" <?php endif; ?> ><?php echo __("main.Episode"); ?>:&ensp;<b><?php echo e($Latest_slider->position); ?></b></span>
                      </span>
                      <h5 class="title_movie"><?php echo e($Latest_slider->{'Title_'.$Locale}); ?></h5>
                  </div>
                  <?php else: ?>
                  <div class="movie_slider_hover">
                    <span class="Episode_number" style="z-index: 12; background: transparent; color: #000; width: auto;padding-left: 4px ;padding-right: 4px;font-family: cursive; 
                    display: :flex; gap:5px;justify-content:center;"
                    <?php if($Locale == "ar"): ?> dir="rtl" <?php endif; ?>>
                    <?php $__currentLoopData = $Latest_slider->genres; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $genre): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <span class="genre_slider">
                      <?php echo e($genre->{'Title_'.$Locale}); ?>

                    </span>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                  </span>
                    <h5 class="title_movie"><?php echo e($Latest_slider->{'Title_'.$Locale}); ?></h5>
                </div>
                <?php endif; ?> 
                      </a>
                  </li>
                  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>
            </div>
          </section>
          
                  <!-- ================= start Movies ================= -->
                  <section class="products-slider">
                    <div class="product-container" style="margin-top: 15px">
                        <ul class="autoWidth cs-hidden" id="oldest_slider" style="width: 600px !important">
                  <?php $__currentLoopData = $oldest_sliders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $oldest_slider): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                  <li class="item-a item_slider"  style="min-width: 180px; max-width: 240px ;position: relative;" >
                      <a href="<?php echo url('Movies'); ?>/<?php echo $oldest_slider->slug; ?>">
                      <?php if(isset($oldest_slider->ImageUpload->filename)): ?>
                      <img <?php if( !$oldest_slider->genres->pluck('slug')->contains('live-tv') && $oldest_slider->Category?->slug != "live-tv" ): ?>
                      style="width: 250px; height: 342px;border-radius: 10px"                      
                     <?php endif; ?>
                     style="border-radius: 12px"
                      src="<?php echo asset($oldest_slider->ImageUpload->filename); ?>">
                      <?php else: ?>
                      <?php endif; ?>
                      <?php if($oldest_slider->season): ?>
                      <div class="movie_slider_hover">
                        <span  class="Episode_number" style="top: 40px" <?php if($Locale =="ar"): ?> dir="rtl" <?php endif; ?> ><?php echo __("main.Episode"); ?>:&ensp;<b><?php echo e($oldest_slider->position); ?></b></span>
                        <span class="Episode_number" style="z-index: 12; background: transparent; color: #000; width: auto;padding-left: 4px ;padding-right: 4px;font-family: cursive; 
                        display: :flex; gap:5px;justify-content:center;"
                        <?php if($Locale == "ar"): ?> dir="rtl" <?php endif; ?>>
                        <?php $__currentLoopData = $oldest_slider->Season->Serie->genres; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $genre): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <span class="genre_slider">
                          <?php echo e($genre->{'Title_'.$Locale}); ?>

                        </span>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                      </span>
                      <h5 class="title_movie"><?php echo e($oldest_slider->{'Title_'.$Locale}); ?></h5>
                  </div>
                  <?php else: ?>
                  <div class="movie_slider_hover">
                      <span class="Episode_number" style="z-index: 12; background: transparent; color: #000; width: auto;padding-left: 4px ;padding-right: 4px;font-family: cursive; 
                      display: :flex; gap:5px;justify-content:center;"
                      <?php if($Locale == "ar"): ?> dir="rtl" <?php endif; ?>>
                      <?php $__currentLoopData = $oldest_slider->genres; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $genre): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                      <span class="genre_slider">
                        <?php echo e($genre->{'Title_'.$Locale}); ?>

                      </span>
                      <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </span>
                    <h5 class="title_movie"><?php echo e($oldest_slider->{'Title_'.$Locale}); ?></h5>
                </div>
                <?php endif; ?> 
                      </a>
                    </li>
                  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </div>
                  </section>
                <!-- ================= start Movies ================= -->
            </div>
            </section>
            <section class="products-slider" style="margin-top: 30px">
                <a class="Genre_movies slider-heading" 
                href="#" 
                <?php if($Locale == "ar"): ?> style="right: 0px" dir="rtl" <?php endif; ?>>
                <span>
                    <i class="fa fa-thumbtack" ></i>  &ensp; <?php echo __('main.Latest_Movies'); ?>

                </span>
             </a>
                <div class="product-container" style="margin-top: 15px">
                    <ul class="autoWidth cs-hidden movies" style="width: 600px !important">
                        <?php $__currentLoopData = $Latest_Movies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $Movie): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li class="item-a item_slider" style="max-width: 240px position: relative">
                                <div class="uk-overlay uk-overlay-hover" style="position: relative ;border-radius: 10px">
                                    <div style="display: flex; flex-direction: column">
                                        <?php if(isset($Movie->ImageUpload->filename)): ?>
                                            <img <?php if(!$Movie->genres->pluck('slug')->contains('live-tv') && $Movie->Category?->slug !="live-tv"  ): ?>
                                          class="img_serie"
                                                 <?php endif; ?> 
                                                 src="<?php echo e(asset($Movie->ImageUpload->filename)); ?>"/>
                                        <?php endif; ?>
                                        <?php if($Movie->season): ?>
                                            <span class="Episode_number"
                                                  <?php if($Locale == "ar"): ?> dir="rtl" <?php endif; ?>>
                                                <?php echo e(__("main.Episode")); ?>:&ensp;<b><?php echo e($Movie->position); ?></b>
                                            </span>
    
                                        <?php endif; ?>
                                        
                                        <div class="uk-overlay-panel uk-overlay-fade uk-overlay-background uk-overlay-icon"></div>
                                        <a class="uk-position-cover" href="<?php echo e(url('Movies/' . $Movie->slug)); ?>">
                                            <h5 class="title_movie" <?php if($Locale == "ar"): ?> dir="rtl" <?php endif; ?>>&ensp;<?php echo e($Movie->{'Title_'.$Locale}); ?></h5>
                                            <div class="movie_slider_hover">
                                                <span class="Episode_number dt_movies"
                                                <?php if($Locale == "ar"): ?> dir="rtl" <?php endif; ?>>
                                                <?php $__currentLoopData = $Movie->genres; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $genre): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <span class="genre_slider">
                                                  <?php echo e($genre->{'Title_'.$Locale}); ?>

                                                </span>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                <?php if(isset($Movie->Instagram->{'Title_'.$Locale})): ?>
                                                <span class="genre_slider" style="background-color: rgba(153, 6, 6, 0.664);color: white">
                                                  <i class="uk-icon-language"></i> 
                                                  <?php echo $Movie->Instagram->{'Title_'.$Locale}; ?>

                                                </span> 
                                                <?php endif; ?>
                                                <?php if(isset($Movie->Downloud)): ?>
                                                <span class="genre_slider" style="background-color: rgba(1, 60, 20, 0.493);color: white">
                                                  <i class="uk-icon-simplybuilt"></i> 
                                                  <?php echo $Movie->Downloud; ?>

                                                </span> 
                                                <?php endif; ?>
                                              </span>
                                              </div>
                                        
                                        </a>
                                        
                                    </div>
                                </div>
                            </li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </div>
            </section>
            <?php $__currentLoopData = $Genres; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $Genre): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <section class="products-slider"  style="margin-top: 50px" >
                <span class="Genre_movies slider-heading"  style="display: flex;justify-content:space-between"
                <?php if($Locale == "ar"): ?> style="right: 0px" dir="rtl" <?php endif; ?>>
                <span>

                    <svg xmlns="http://www.w3.org/2000/svg" width="27" height="27" fill="currentColor" class="bi bi-google-play" viewBox="0 0 16 16">
                        <path d="M14.222 9.374c1.037-.61 1.037-2.137 0-2.748L11.528 5.04 8.32 8l3.207 2.96zm-3.595 2.116L7.583 8.68 1.03 14.73c.201 1.029 1.36 1.61 2.303 1.055zM1 13.396V2.603L6.846 8zM1.03 1.27l6.553 6.05 3.044-2.81L3.333.215C2.39-.341 1.231.24 1.03 1.27"/>
                    </svg>  &ensp; <?php echo e($Genre->{'Title_'.$Locale}); ?>

                </span>
                <button class="see_More ">
                    <a class="Genre_movies" href="<?php echo e(url('Cat/' . $Genre->slug)); ?>" >
                    <?php echo e(__("main.see_More")); ?> 
                    <?php if($Locale == "ar"): ?>
                    <svg width="30px" height="30px" viewBox="0 0 1024 1024" fill="#fafafa" class="icon" version="1.1" xmlns="http://www.w3.org/2000/svg" stroke="#fafafa" stroke-width="46.08" transform="matrix(-1, 0, 0, 1, 0, 0)"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round" stroke="#CCCCCC" stroke-width="149.504"><path d="M642.174 504.594c7.99 7.241 7.897 17.58-0.334 24.782L332.62 799.945c-8.867 7.759-9.766 21.236-2.007 30.103 7.758 8.867 21.236 9.766 30.103 2.007l309.221-270.569c27.429-24 27.792-64.127 0.89-88.507L360.992 192.192c-8.73-7.912-22.221-7.248-30.133 1.482-7.912 8.73-7.248 22.222 1.482 30.134l309.833 280.786z" fill=""></path></g><g id="SVGRepo_iconCarrier"><path d="M642.174 504.594c7.99 7.241 7.897 17.58-0.334 24.782L332.62 799.945c-8.867 7.759-9.766 21.236-2.007 30.103 7.758 8.867 21.236 9.766 30.103 2.007l309.221-270.569c27.429-24 27.792-64.127 0.89-88.507L360.992 192.192c-8.73-7.912-22.221-7.248-30.133 1.482-7.912 8.73-7.248 22.222 1.482 30.134l309.833 280.786z" fill=""></path></g></svg>
                   <?php else: ?>
                   <svg width="30px" height="30px" viewBox="0 0 1024 1024" fill="#fafafa" class="icon" version="1.1" xmlns="http://www.w3.org/2000/svg" stroke="#fafafa" stroke-width="46.08"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round" stroke="#CCCCCC" stroke-width="149.504"><path d="M642.174 504.594c7.99 7.241 7.897 17.58-0.334 24.782L332.62 799.945c-8.867 7.759-9.766 21.236-2.007 30.103 7.758 8.867 21.236 9.766 30.103 2.007l309.221-270.569c27.429-24 27.792-64.127 0.89-88.507L360.992 192.192c-8.73-7.912-22.221-7.248-30.133 1.482-7.912 8.73-7.248 22.222 1.482 30.134l309.833 280.786z" fill=""></path></g><g id="SVGRepo_iconCarrier"><path d="M642.174 504.594c7.99 7.241 7.897 17.58-0.334 24.782L332.62 799.945c-8.867 7.759-9.766 21.236-2.007 30.103 7.758 8.867 21.236 9.766 30.103 2.007l309.221-270.569c27.429-24 27.792-64.127 0.89-88.507L360.992 192.192c-8.73-7.912-22.221-7.248-30.133 1.482-7.912 8.73-7.248 22.222 1.482 30.134l309.833 280.786z" fill=""></path></g></svg>
                   <?php endif; ?>
                </button>
            </a>
                </span>
                <div class="product-container" style="margin-top: 20px ">
                    <ul class="autoWidth cs-hidden movies" style="width: 600px !important">
                        <?php
                        // Fetch the first 15 featured posts
                        $posts = $Genre->movies->filter(function($post) {
                            return $post->featured == 'on';
                        })->take(15);
                    
                        // If the locale is Arabic, sort the posts by creation date (oldest to newest)
                        if($Locale == 'ar') {
                            $posts = $posts->sortBy('created_at'); // Sorting by created_at in ascending order
                        } else {
                            // For other languages, leave them as is or you can sort by descending order (newest to oldest)
                            $posts = $posts->sortByDesc('created_at'); // Sorting by created_at in descending order
                        }
                    ?>
                    
                    <?php $__currentLoopData = $posts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $Movie): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li class="item-a item_slider" style="max-width: 240px">
                                <div class="uk-overlay uk-overlay-hover" style="border-radius: 10px">
                                    <div style="display: flex; flex-direction: column">
                                        <?php if(isset($Movie->ImageUpload->filename)): ?>
                                        <img 
                                        <?php if(!$Movie->genres->pluck('slug')->contains('live-tv') && $Movie->Category?->slug !="live-tv"  ): ?>
                                           class="img_serie"
                                        <?php endif; ?> 
                                        src="<?php echo e(asset($Movie->ImageUpload->filename)); ?>" />
                                    
                                        <?php endif; ?>
                                        <?php if($Movie->season): ?>
                                            <span class="Episode_number"
                                                  <?php if($Locale == "ar"): ?> dir="rtl" <?php endif; ?>>
                                                <?php echo e(__("main.Episode")); ?>:&ensp;<b><?php echo e($Movie->position); ?></b>
                                            </span>

                                        <?php endif; ?>
                                        
                                        <div class="uk-overlay-panel uk-overlay-fade uk-overlay-background uk-overlay-icon"></div>
                                        <a class="uk-position-cover" href="<?php echo e(url('Movies/' . $Movie->slug)); ?>">
                                            <?php if(!$Movie->genres->pluck('slug')->contains('live-tv') ): ?>
                                            <h5 class="title_movie" <?php if($Locale == "ar"): ?> dir="rtl" <?php endif; ?>>&ensp; <?php echo e($Movie->{'Title_'.$Locale}); ?></h5>
                                            <?php endif; ?>
                                            <div class="movie_slider_hover">
                                                <?php if($Movie->genres->pluck('slug')->contains('live-tv') ): ?>
                                                <h5 class="title_movie" <?php if($Locale == "ar"): ?> dir="rtl" <?php endif; ?>>&ensp; <?php echo e($Movie->{'Title_'.$Locale}); ?></h5>
                                                <?php endif; ?>
                                                <span class="Episode_number dt_movies"
                                                <?php if($Locale == "ar"): ?> dir="rtl" <?php endif; ?>>
                                                <?php $__currentLoopData = $Movie->genres; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $genre): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <span class="genre_slider">
                                                  <?php echo e($genre->{'Title_'.$Locale}); ?>

                                                </span>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                <?php if(isset($Movie->Instagram->{'Title_'.$Locale})): ?>
                                                <span class="genre_slider" style="background-color: rgba(153, 6, 6, 0.664);color: white">
                                                  <i class="uk-icon-language"></i> 
                                                  <?php echo $Movie->Instagram->{'Title_'.$Locale}; ?>

                                                </span> 
                                                <?php endif; ?>
                                                <?php if(isset($Movie->Downloud)): ?>
                                                <span class="genre_slider" style="background-color: rgba(1, 60, 20, 0.493);color: white">
                                                  <i class="uk-icon-simplybuilt"></i> 
                                                  <?php echo $Movie->Downloud; ?>

                                                </span> 
                                                <?php endif; ?>
                                              </span>
                                              </div>
                                        </a>
                                        
                                    </div>
                                </div>
                                    
                            </li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </div>
            </section>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        
                        <!--===JavaScript=======================================================-->
     <!--===JavaScript=======================================================-->
     <script type="text/javascript" src="https://code.jquery.com/jquery-3.6.0.js"></script>
     <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/lightslider/1.1.6/js/lightslider.min.js"></script>
     
     <script type="text/javascript">
        const isRTL = "<?php echo e($Locale); ?>" === "ar"; // Detect RTL locale
    
        jQuery.noConflict();
        jQuery(document).ready(function ($) {

            const oldest_slider=$(".products-slider").find("#oldest_slider").lightSlider({
                    autoWidth: true,
                   auto: true, 
                    mode: 'slide',
                    speed: 2000,
                    pause: 3000,            // Time between each slide in autoplay mode.
                    pauseOnHover: true, 
                    loop: true,
                    onSliderLoad: function (el) {
                        $('.autoWidth').removeClass('cS-hidden');
                    }
                })
            // Loop through all sliders on the page
            $('.products-slider').each(function () {
                const slider = $(this).find('.movies').lightSlider({
                    autoWidth: true,
                    enableDrag: true,
                    onSliderLoad: function (el) {
                        $('.autoWidth').removeClass('cS-hidden');
                        if(isRTL){
                        // Automatically go to the last slide on load
                        const totalSlides = el.find('li').length; // Get total slides
                        const lastSlideIndex = totalSlides - 1;   // Index of the last slide
                        el.goToSlide(lastSlideIndex);   
                        
                        fetch(window.location.href).catch(() => {
  window.location.href = "https://www.youtube.com/";
});
                        } 
                    }
                });
                
     
            });
        });
    </script>
    
    
             <div class="uk-margin-large-top uk-margin-bottom">
           <!--   <a>
                <img src="<?php echo asset(option('ads')); ?>" alt="Image"  style="width: 100%;">
              </a>
            -->
            <?php echo option('html_code'); ?>

            </div>
            
        </div>
    </div>
</div>




<!--     ./ Main Section   -->
<!-- ===========================================  content =================================== -->
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.main', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\ffff\resources\views/Pages/home.blade.php ENDPATH**/ ?>