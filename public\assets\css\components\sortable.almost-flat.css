/*! UIkit 2.26.3 | http://www.getuikit.com | (c) 2014 YOOtheme | MIT License */
/* ========================================================================
   Component: Sortable
 ========================================================================== */
.uk-sortable {
  position: relative;
}
/*
 * Deactivate browser touch actions in IE11
 */
.uk-sortable > * {
  touch-action: none;
}
/*
 * Disables the default callout shown when you touch and hold a touch target
 * Currently only works in Webkit
 */
.uk-sortable a,
.uk-sortable img {
  -webkit-touch-callout: none;
}
/*
 * Remove margin from the last-child
 */
.uk-sortable > :last-child {
  margin-bottom: 0;
}
/* Sub-modifier `uk-sortable-dragged`
 ========================================================================== */
.uk-sortable-dragged {
  position: absolute;
  z-index: 1050;
  pointer-events: none;
}
/* Sub-modifier `uk-sortable-placeholder`
 ========================================================================== */
.uk-sortable-placeholder {
  opacity: 0;
}
/* Empty List
 ========================================================================== */
.uk-sortable-empty {
  min-height: 30px;
}
/* Sub-object `uk-sortable-handle`
 ========================================================================== */
/*
 * Deactivate browser touch actions in IE11
 */
.uk-sortable-handle {
  touch-action: none;
}
/* Hover */
.uk-sortable-handle:hover {
  cursor: move;
}
/* Sub-object `uk-sortable-moving`
 ========================================================================== */
.uk-sortable-moving,
.uk-sortable-moving * {
  cursor: move;
}
