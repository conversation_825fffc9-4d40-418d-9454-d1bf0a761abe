@import url(https://fonts.googleapis.com/css?family=Montserrat:400,700);
@import url(http://fonts.googleapis.com/css?family=RobotoDraft:regular,bold,italic,thin,light,bolditalic,black,medium&amp;lang=en);

@import url('https://fonts.googleapis.com/css2?family=Ubuntu:ital,wght@0,300;0,400;0,500;0,700;1,300;1,400;1,500;1,700&display=swap');

.display-zero{
 display: none !important;
}

.about_img{
  float: left;
  display: inline-block;
  margin-right: 40px;
}


.uk-Movies span{
  padding: 10px;
  margin-right: 5px;
  border-radius: 5px;
  background: #ED213A;
  background: -webkit-linear-gradient(to right, #93291E, #ED213A);
  background: linear-gradient(to right, #93291E, #ED213A);
  background-color: rgba(0, 0, 0, 0);
  color: #fff;
}

.uk-Movies a{
  color: #fff;
  padding: 10px;
  margin-right: 5px;
  border-radius: 5px;
  background: #ED213A;
  background: -webkit-linear-gradient(to right, #93291E, #ED213A);
  background: linear-gradient(to right, #93291E, #ED213A);
  background-color: rgba(0, 0, 0, 0);
}

body,html {
  background: #191818; color: #999999;
   /* font-family: RobotoDraft,"Helvetica Neue",sans-serif; */
   font-family: "Ubuntu", serif;
   font-style: normal;
}

.min-h{
  height: 100%
}

.min-h2{
  height: 800px
}

.min-w{
  width: 100%
}

.color-y{
  background: #ed213a;
  background: -webkit-linear-gradient(to right, #ffcc33, #ed213a);
  background: linear-gradient(to right, #ffcc33, #ed213a);
}

.mt-50{
  margin-top: 50px !important
}

.uk-panel {
  display: block;
  position: relative;
}

.uk-panel,
.uk-panel:hover {
  text-decoration: none;
}

.uk-panel:before,
.uk-panel:after {
  content: "";
  display: table;
}

.uk-panel:after {
  clear: both;
}

.uk-panel > :not(.uk-panel-title):last-child {
  margin-bottom: 0;
}
.uk-panel-title {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 19px;
  line-height: 25px;
  font-weight: normal;
  text-transform: none;
  color: #bebdbd;
}
.uk-panel-badge {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 1;
}
.uk-panel-teaser {
  margin-bottom: 15px;
}
.uk-panel-body {
  padding: 15px;
}
.uk-panel-box {
  padding: 15px;
  background: #191818;
  color: #bebdbd;
}
.uk-panel-box-hover:hover {
  color: #bebdbd;
}
.uk-panel-box .uk-panel-title {
  color: #bebdbd;
}
.uk-panel-box .uk-panel-badge {
  top: 10px;
  right: 10px;
}
.uk-panel-box > .uk-panel-teaser {
  margin-top: -15px;
  margin-left: -15px;
  margin-right: -15px;
}
.uk-panel-box > .uk-nav-side {
  margin: 0 -15px;
}
.uk-panel-box-primary {
  background-color: #00a8e6;
  color: rgba(255, 255, 255, 0.8);
}
.uk-panel-box-primary-hover:hover {
  color: rgba(255, 255, 255, 0.8);
}
.uk-panel-box-primary .uk-panel-title {
  color: #ffffff;
}
.uk-panel-box-secondary {
  background-color: #222121;
  color: rgba(255, 255, 255, 0.68);
}
.uk-panel-box-secondary-hover:hover {
  color: rgba(255, 255, 255, 0.68);
}
.uk-panel-box-secondary .uk-panel-title {
  color: #ffffff;
}
.uk-panel-hover {
  padding: 15px;
  color: #bebdbd;
}
.uk-panel-hover:hover {
  background: #f5f5f5;
  color: #bebdbd;
}
.uk-panel-hover .uk-panel-badge {
  top: 10px;
  right: 10px;
}
.uk-panel-hover > .uk-panel-teaser {
  margin-top: -15px;
  margin-left: -15px;
  margin-right: -15px;
}
.uk-panel-header .uk-panel-title {
  padding-bottom: 10px;
  border-bottom: 1px solid #434343;
  color: #bebdbd;
}
.uk-panel-space {
  padding: 30px;
}
.uk-panel-space .uk-panel-badge {
  top: 30px;
  right: 30px;
}
.uk-panel + .uk-panel-divider {
  margin-top: 50px !important;
}
.uk-panel + .uk-panel-divider:before {
  content: "";
  display: block;
  position: absolute;
  top: -25px;
  left: 0;
  right: 0;
  border-top: 1px solid #434343;
}
@media (min-width: 1220px) {
  .uk-panel + .uk-panel-divider {
    margin-top: 70px !important;
  }
  .uk-panel + .uk-panel-divider:before {
    top: -35px;
  }
}
.uk-block {
  position: relative;
  box-sizing: border-box;
  padding-top: 20px;
  padding-bottom: 20px;
}
@media (min-width: 768px) {
  .uk-block {
    padding-top: 50px;
    padding-bottom: 50px;
  }
}
.uk-block:before,
.uk-block:after {
  content: "";
  display: table;
}
.uk-block:after {
  clear: both;
}
.uk-block > :last-child {
  margin-bottom: 0;
}
.uk-block-large {
  padding-top: 20px;
  padding-bottom: 20px;
}
@media (min-width: 768px) {
  .uk-block-large {
    padding-top: 50px;
    padding-bottom: 50px;
  }
}
@media (min-width: 960px) {
  .uk-block-large {
    padding-top: 100px;
    padding-bottom: 100px;
  }
}
.uk-block-default {
  background: #191818;
}
.uk-block-muted {
  background: #222121;
}
.uk-block-primary {
  background: #2197ef;
}
.uk-block-secondary {
  background: #222121;
}
.uk-block-default + .uk-block-default,
.uk-block-muted + .uk-block-muted,
.uk-block-primary + .uk-block-primary,
.uk-block-secondary + .uk-block-secondary {
  padding-top: 0;
}

.uk-nav-offcanvas > .uk-open > a,
html:not(.uk-touch) .uk-nav-offcanvas > li > a:hover,
html:not(.uk-touch) .uk-nav-offcanvas > li > a:focus {
  background: #1d1b1b;
  color: #ffffff;
  outline: none;
}
html .uk-nav.uk-nav-offcanvas > li.uk-active > a {
  background: #000000;
  color: #ffffff;
}
.uk-nav-offcanvas .uk-nav-header {
  color: #777777;
}
.uk-nav-offcanvas .uk-nav-divider {
  border-top: 1px solid #000000;
}
.uk-nav-offcanvas ul a {
  color: #cccccc;
}
html:not(.uk-touch) .uk-nav-offcanvas ul a:hover {
  color: #ffffff;
}
.uk-navbar {
  background: #eeeeee;
  color: #bebdbd;
}
.uk-navbar:before,
.uk-navbar:after {
  content: "";
  display: table;
}
.uk-navbar:after {
  clear: both;
}

.uk-form input,
.uk-form select,
.uk-form textarea {
  box-sizing: border-box;
  margin: 0;
  border-radius: 0;
  font: inherit;
  color: inherit;
}
.uk-form select {
  text-transform: none;
}
.uk-form optgroup {
  font: inherit;
  font-weight: bold;
}
.uk-form input::-moz-focus-inner {
  border: 0;
  padding: 0;
}
.uk-form input[type="checkbox"],
.uk-form input[type="radio"] {
  padding: 0;
}
.uk-form input[type="checkbox"]:not(:disabled),
.uk-form input[type="radio"]:not(:disabled) {
  cursor: pointer;
}
.uk-form textarea,
.uk-form input:not([type]),
.uk-form input[type="text"],
.uk-form input[type="password"],
.uk-form input[type="email"],
.uk-form input[type="url"],
.uk-form input[type="search"],
.uk-form input[type="tel"],
.uk-form input[type="number"],
.uk-form input[type="datetime"] {
  -webkit-appearance: none;
}
.uk-form input[type="search"]::-webkit-search-cancel-button,
.uk-form input[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}
.uk-form input[type="number"]::-webkit-inner-spin-button,
.uk-form input[type="number"]::-webkit-outer-spin-button {
  height: auto;
}
.uk-form fieldset {
  border: none;
  margin: 0;
  padding: 0;
}
.uk-form textarea {
  overflow: auto;
  vertical-align: top;
}
.uk-form ::-moz-placeholder {
  opacity: 1;
}
.uk-form :invalid {
  box-shadow: none;
}
.uk-form input:not([type="radio"]):not([type="checkbox"]),
.uk-form select {
  vertical-align: middle;
}
.uk-form > :last-child {
  margin-bottom: 0;
}
.uk-form select,
.uk-form textarea,
.uk-form input:not([type]),
.uk-form input[type="text"],
.uk-form input[type="password"],
.uk-form input[type="datetime"],
.uk-form input[type="datetime-local"],
.uk-form input[type="date"],
.uk-form input[type="month"],
.uk-form input[type="time"],
.uk-form input[type="week"],
.uk-form input[type="number"],
.uk-form input[type="email"],
.uk-form input[type="url"],
.uk-form input[type="search"],
.uk-form input[type="tel"],
.uk-form input[type="color"] {
  height: 35px;
    max-width: 100%;
    padding: 4px 10px;
    border: 1px solid #222121;
    background: #222121;
    color: #A5A5A5;
-webkit-border-radius: 3px;
-moz-border-radius: 3px;
border-radius: 3px;
}
.uk-form select:focus,
.uk-form textarea:focus,
.uk-form input:not([type]):focus,
.uk-form input[type="text"]:focus,
.uk-form input[type="password"]:focus,
.uk-form input[type="datetime"]:focus,
.uk-form input[type="datetime-local"]:focus,
.uk-form input[type="date"]:focus,
.uk-form input[type="month"]:focus,
.uk-form input[type="time"]:focus,
.uk-form input[type="week"]:focus,
.uk-form input[type="number"]:focus,
.uk-form input[type="email"]:focus,
.uk-form input[type="url"]:focus,
.uk-form input[type="search"]:focus,
.uk-form input[type="tel"]:focus,
.uk-form input[type="color"]:focus {
  border-color: #2197ef;
  outline: 0;
  background: #222121;
  color: #bebdbd;
}
.uk-form select:disabled,
.uk-form textarea:disabled,
.uk-form input:not([type]):disabled,
.uk-form input[type="text"]:disabled,
.uk-form input[type="password"]:disabled,
.uk-form input[type="datetime"]:disabled,
.uk-form input[type="datetime-local"]:disabled,
.uk-form input[type="date"]:disabled,
.uk-form input[type="month"]:disabled,
.uk-form input[type="time"]:disabled,
.uk-form input[type="week"]:disabled,
.uk-form input[type="number"]:disabled,
.uk-form input[type="email"]:disabled,
.uk-form input[type="url"]:disabled,
.uk-form input[type="search"]:disabled,
.uk-form input[type="tel"]:disabled,
.uk-form input[type="color"]:disabled {
  border-color: #434343;
  background-color: #3a3a3a;
  color: #999999;
}
.uk-form :-ms-input-placeholder {
  color: #999999 !important;
}
.uk-form ::-moz-placeholder {
  color: #999999;
}
.uk-form ::-webkit-input-placeholder {
  color: #999999;
}
.uk-form :disabled:-ms-input-placeholder {
  color: #999999 !important;
}
.uk-form :disabled::-moz-placeholder {
  color: #999999;
}
.uk-form :disabled::-webkit-input-placeholder {
  color: #999999;
}
.uk-form legend {
  width: 100%;
  border: 0;
  padding: 0;
  padding-bottom: 15px;
  font-size: 19px;
  line-height: 32px;
}
.uk-form legend:after {
  content: "";
  display: block;
  border-bottom: 1px solid #434343;
  width: 100%;
}
select.uk-form-small,
textarea.uk-form-small,
input[type].uk-form-small,
input:not([type]).uk-form-small {
  height: 25px;
  padding: 3px 3px;
  font-size: 13px;
}
select.uk-form-large,
textarea.uk-form-large,
input[type].uk-form-large,
input:not([type]).uk-form-large {
  height: 40px;
  padding: 8px 6px;
  font-size: 17px;
}
.uk-form textarea,
.uk-form select[multiple],
.uk-form select[size] {
  height: auto;
}
.uk-form-danger {
  border-color: #d65b49 !important;
  background: #222121 !important;
  color: #d85030 !important;
}
.uk-form-success {
  border-color: #66b92d !important;
  background: #222121 !important;
  color: #659f13 !important;
}
.uk-form-blank {
  border-color: transparent !important;
  border-style: dashed !important;
  background: none !important;
}
.uk-form-blank:focus {
  border-color: #434343 !important;
}
input.uk-form-width-mini {
  width: 50px;
}
select.uk-form-width-mini {
  width: 75px;
}
.uk-form-width-small {
  width: 130px;
}
.uk-form-width-medium {
  width: 200px;
}
.uk-form-width-large {
  width: 500px;
}
.uk-form-row:before,
.uk-form-row:after {
  content: "";
  display: table;
}
.uk-form-row:after {
  clear: both;
}
.uk-form-row + .uk-form-row {
  margin-top: 15px;
}
.uk-form-help-inline {
  display: inline-block;
  margin: 0 0 0 10px;
}
.uk-form-help-block {
  margin: 5px 0 0 0;
}
.uk-form-controls > :first-child {
  margin-top: 0;
}
.uk-form-controls > :last-child {
  margin-bottom: 0;
}
.uk-form-controls-condensed {
  margin: 5px 0;
}
.uk-form-stacked .uk-form-label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}
@media (max-width: 959px) {
  .uk-form-horizontal .uk-form-label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
  }
}
@media (min-width: 960px) {
  .uk-form-horizontal .uk-form-label {
    width: 200px;
    margin-top: 5px;
    float: left;
  }
  .uk-form-horizontal .uk-form-controls {
    margin-left: 215px;
  }
  .uk-form-horizontal .uk-form-controls-text {
    padding-top: 5px;
  }
}
.uk-form-icon {
  display: inline-block;
  position: relative;
  max-width: 100%;
}
.uk-form-icon > [class*='uk-icon-'] {
  position: absolute;
  top: 50%;
  width: 30px;
  margin-top: -8px;
  font-size: 15px;
  color: #999999;
  text-align: center;
  pointer-events: none;
}
.uk-form-icon:not(.uk-form-icon-flip) > input {
  padding-left: 30px !important;
}
.uk-form-icon-flip > [class*='uk-icon-'] {
  right: 0;
}
.uk-form-icon-flip > input {
  padding-right: 30px !important;
}


.uk-form input[type="radio"],
.uk-form input[type="checkbox"] {
height: 20px;
    width: 20px;
    border: 1px solid #339ae7;
    overflow: hidden;
    margin-top: -4px;
    vertical-align: middle;
    -webkit-appearance: none;
    outline: 0;
    background: #339ae7;
    margin-right: 5px;
}
/* Radio */
.uk-form input[type="radio"] {
  border-radius: 50%;
}
/*
 * Checked
 */
.uk-form input[type=radio]:before,
.uk-form input[type=checkbox]:before {
  display: block;
}
/* Radio */
.uk-form input[type=radio]:checked:before {
  content: '';
  width: 8px;
  height: 8px;
  margin: 2px auto 0;
  border-radius: 50%;
  background: #00a8e6;
}
/* Checkbox */
.uk-form input[type=checkbox]:checked:before,
.uk-form input[type=checkbox]:indeterminate:before {
content: "\f00c";
    font-family: "Ubuntu", serif;
    font-size: 12px;
    -webkit-font-smoothing: antialiased;
    text-align: center;
    line-height: 18px;
    color: #fff;
 
}
.uk-form input[type=checkbox]:indeterminate:before {
  content: "\f068";
}
/*
 * Disabled
 */
.uk-form input[type=radio]:disabled,
.uk-form input[type=checkbox]:disabled {
  border-color: #dddddd;
}
.uk-form input[type=radio]:disabled:checked:before {
  background-color: #aaaaaa;
}


.uk-form input[type=checkbox]
 {
  -webkit-border-radius: 2px;
-moz-border-radius: 2px;
border-radius: 2px;
}

.uk-form input[type=checkbox]:disabled:checked:before,
.uk-form input[type=checkbox]:disabled:indeterminate:before {
  color: #aaaaaa;
}

.uk-search {
  display: inline-block;
  /* 1 */
  position: relative;
  /* 2 */
  margin: 0;
}
/*
 * Icon
 */
.uk-search:before {
  content: "\f002";
  position: absolute;
  top: 0;
  left: 0;
  width: 30px;
  line-height: 30px;
  text-align: center;
  font-family: FontAwesome;
  font-size: 14px;
  color: #fff;
}


.uk-subnav > * > :hover, .uk-subnav > * > :focus{
  color: #FDC830;  /* fallback for old browsers */
 
}

.uk-button::-moz-focus-inner {
  border: 0;
  padding: 0;
}
.uk-button {
-webkit-border-radius: 3px;
-moz-border-radius: 3px;
border-radius: 3px;
  -webkit-appearance: none;
  margin: 0;
  border: none;
  overflow: visible;
  font: inherit;
  color: #bebdbd;
  text-transform: none;
  display: inline-block;
  box-sizing: border-box;
  padding: 0 13px;
  vertical-align: middle;
  line-height: 30px;
  min-height: 30px;
  font-size: 1rem;
  text-decoration: none;
  text-align: center;
  background: #ED213A;  /* fallback for old browsers */
background: -webkit-linear-gradient(to right, #93291E, #ED213A);  /* Chrome 10-25, Safari 5.1-6 */
background: linear-gradient(to right, #93291E, #ED213A); /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */

}
.uk-button:not(:disabled) {
  cursor: pointer;
}
.uk-button:hover,
.uk-button:focus {
  background-color: #393838;
  color: #bebdbd;
  outline: none;
  text-decoration: none;
}
.uk-button:active,
.uk-button.uk-active {
  background-color: #323131;
  color: #bebdbd;
}
.uk-button-primary {
  background-color: #00a8e6;
  color: #ffffff;
}
.uk-button-primary:hover,
.uk-button-primary:focus {
  background-color: #0091c7;
  color: #ffffff;
}
.uk-button-primary:active,
.uk-button-primary.uk-active {
  background-color: #007dac;
  color: #ffffff;
}
.uk-button-success {
  background-color: #66b82f;
  color: #ffffff;
}
.uk-button-success:hover,
.uk-button-success:focus {
  background-color: #589f28;
  color: #ffffff;
}
.uk-button-success:active,
.uk-button-success.uk-active {
  background-color: #4c8922;
  color: #ffffff;
}
.uk-button-danger {
  background-color: #d65b49;
  color: #ffffff;
}
.uk-button-danger:hover,
.uk-button-danger:focus {
  background-color: #ba4f3f;
  color: #ffffff;
}
.uk-button-danger:active,
.uk-button-danger.uk-active {
  background-color: #a24436;
  color: #ffffff;
}
.uk-button:disabled {
  background-color: #131313;
  color: #999999;
}
.uk-button-link,
.uk-button-link:hover,
.uk-button-link:focus,
.uk-button-link:active,
.uk-button-link.uk-active,
.uk-button-link:disabled {
  border-color: transparent;
  background: none;
}
.uk-button-link {
  color: #249af2;
}
.uk-button-link:hover,
.uk-button-link:focus,
.uk-button-link:active,
.uk-button-link.uk-active {
  color: #fff;
  text-decoration: none;
}
.uk-button-link:disabled {
  color: #999999;
}
.uk-button-link:focus {
  outline: 1px dotted;
}
.uk-button-mini {
  min-height: 20px;
  padding: 0 6px;
  line-height: 20px;
  font-size: 12px;
}
.uk-button-small {
  min-height: 25px;
  padding: 0 10px;
  line-height: 25px;
  font-size: 13px;
}
.uk-button-large {
  min-height: 40px;
  padding: 0 15px;
  line-height: 40px;
  font-size: 17px;
}
.uk-button-group {
  display: inline-block;
  vertical-align: middle;
  position: relative;
  font-size: 0.001px;
  white-space: nowrap;
}
.uk-button-group > * {
  display: inline-block;
}
.uk-button-group .uk-button {
  vertical-align: top;
}
.uk-button-dropdown {
  display: inline-block;
  vertical-align: middle;
  position: relative;
}

.uk-button-social  {   height: 50px;
    line-height: 55px;}


/*
 * Modifier: `uk-button-facebook`
 */
.uk-button-facebook{
  background-color: #3B5997;
  color: #ffffff;
}
/* Hover */
.uk-button-facebook:hover,
.uk-button-facebook:focus {
  background-color: #334d84;
  color: #ffffff;
}
/* Active */
.uk-button-facebook:active,
.uk-button-facebook.uk-active {
  background-color: #5f739e;
  color: #ffffff;
}

/*
 * Modifier: `uk-button-twitter`
 */
.uk-button-twitter{
  background-color: #55ACEF;
  color: #ffffff;
}
/* Hover */
.uk-button-twitter:hover,
.uk-button-twitter:focus {
  background-color: #4994ce;
  color: #ffffff;
}
/* Active */
.uk-button-twitter:active,
.uk-button-twitter.uk-active {
  background-color: #74acd9;
  color: #ffffff;
}

.uk-navbar-brand img{
      margin-top: -10px;
    margin-left: -5px;
}

/*
 * Modifier: `uk-button-google+`
 */
.uk-button-google{
  background-color: #DF2F2F;
  color: #ffffff;
}
/* Hover */
.uk-button-google:hover,
.uk-button-google:focus {
  background-color: #c22929;
  color: #ffffff;
}
/* Active */
.uk-button-google:active,
.uk-button-google.uk-active {
  background-color: #d36565;
  color: #ffffff;
}


.uk-badge {
  display: inline-block;
  padding: 0 8px;
  background: #00a8e6;
  font-size: 11px;
  font-weight: normal;
  line-height: 15px;
  color: #ffffff;
  text-align: center;
  vertical-align: middle;
  text-transform: uppercase;
}
a.uk-badge:hover {
  color: #ffffff;
}
.uk-badge-notification {
  box-sizing: border-box;
  min-width: 19px;
  border-radius: 500px;
  font-size: 13px;
  line-height: 19px;
}
.uk-badge-success {
  background-color: #66b82f;
}
.uk-badge-warning {
  background-color: #da922c;
}
.uk-badge-danger {
  background-color: #d65b49;
}
.uk-alert {
  margin-bottom: 15px;
  padding: 10px;
  background: #2197ef;
  color: rgba(250, 250, 250, 0.79);
}
* + .uk-alert {
  margin-top: 15px;
}
.uk-alert > :last-child {
  margin-bottom: 0;
}
.uk-alert h1,
.uk-alert h2,
.uk-alert h3,
.uk-alert h4,
.uk-alert h5,
.uk-alert h6 {
  color: inherit;
}
.uk-alert > .uk-close:first-child {
  float: right;
}
.uk-alert > .uk-close:first-child + * {
  margin-top: 0;
}
.uk-alert-success {
  background: #66b92d;
  color: rgba(255, 255, 255, 0.81);
}
.uk-alert-warning {
  background: #da932b;
  color: rgba(255, 255, 255, 0.81);
}
.uk-alert-danger {
  background: #d65b49;
  color: rgba(255, 255, 255, 0.83);
}
.uk-alert-large {
  padding: 20px;
}
.uk-alert-large > .uk-close:first-child {
  margin: -10px -10px 0 0;
}

.uk-text-muted {
  color: #999999 !important;
}
.uk-text-primary {
  color: #00a8e6 !important;
}
.uk-text-success {
  color: #66b82f !important;
}
.uk-text-warning {
  color: #da922c !important;
}
.uk-text-danger {
  color: #d65b49 !important;
}
.uk-text-contrast {
  color: #ffffff !important;
}


.uk-progress-success .uk-progress-bar {
  background-color: #66b82f;
}
.uk-progress-warning .uk-progress-bar {
  background-color: #da922c;
}
.uk-progress-danger .uk-progress-bar {
  background-color: #d65b49;
}
.uk-progress-striped .uk-progress-bar {
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 30px 30px;
}



.uk-overlay-background-yelow {
    background: #FDC830;
background: -webkit-linear-gradient(to right, #F37335, #FDC830);
background: linear-gradient(to right, #F37335, #FDC830);
    opacity: 0.2
}


.uk-overlay-background-blue {
    background: rgba(37, 113, 170, 0.89);
    padding: 5em 0em;
}
.uk-overlay-background-blue {
    background: rgba(37, 113, 170, 0.89);
}

.uk-pagination > li > a, .uk-pagination > li > span {
    display: inline-block;
    min-width: 40px;
    padding: 3px 5px;
    line-height: 40px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    text-decoration: none;
    box-sizing: content-box;
    text-align: center;}


    .uk-pagination > li > a {
    background: #222121;
    color: #C7C7C7;
}

    .uk-pagination > li > a:hover {
    background: #393838;
    color: #fff;
}

    .uk-pagination > li > a:active , .uk-pagination > li > a:focus {
    background: #141313;
    color: #fff;
}


.uk-pagination > .uk-active > span {
    background: #ed213a !important;
    color: #ffffff;
    font-weight: bold;
}
.uk-pagination>.uk-disabled>span {
    background-color: transparent;
    color: #999;
}
.uk-nav-side > li > a:hover, .uk-nav-side > li > a:focus {
    background: rgba(0, 0, 0, 0.08);
    color: #fff;
    outline: none;
}

.uk-nav-side > li.uk-active > a {
    background: transparent;
    border-right: 3px solid #ed213a;
    color: #ffffff;
}

.uk-nav-side > li > a {
    font-size: 15px;
    color: #808080;
    line-height: 30px;
}
.uk-nav-side .uk-nav-divider {
    border-top: 1px dotted #3A3A3A;
}

.uk-nav-side ul a {
    color: #B5B5B5;
    font-size: 12px;}

    .uk-nav-side ul a:hover,.uk-nav-side ul a:active  {
    color: #fff;
   }
.uk-nav-side .uk-nav-header {
    color: #ECECEC;
}
.uk-navbar li>a {font-family: "Ubuntu", serif;} 



.uk-container {max-width: 2000px;
padding-left: 70px;
}



.uk-navbar-toggle {
    color: #949393;
}
.uk-navbar-toggle:hover, .uk-navbar-toggle:active  {
    color: #808080;
}


.uk-offcanvas-bar {
 
    background: #100f0f;}

    .uk-offcanvas-bar .uk-panel-title {
 
  font-size: 12px;}

html .uk-nav.uk-nav-offcanvas>li.uk-active>a {
    background: transparent;
    color: #fff;
    border-right: 4px solid #339ae7;
    font-weight: bold;
}


.uk-nav-offcanvas>.uk-open>a, html:not(.uk-touch) .uk-nav-offcanvas>li>a:focus, html:not(.uk-touch) .uk-nav-offcanvas>li>a:hover {
    background: #0e0d0d;
    color: #fff;
    outline: 0;}




.uk-overlay-background {
    background: rgba(0, 0, 0, 0.91);
}


/* Header Section
 ========================================================================== */






.tm-navbar {
    position: relative;
    background: #fff;
    box-shadow: 0 3px 8px -4px rgba(0,0,0,0.15);
    z-index: 1;
}

.tm-navbar-overlay:not(.uk-active) {
    position: absolute;
    z-index: 1;
    width: 100%;
}

.tm-navbar-transparent {
    border-bottom-color: transparent;
    background: transparent;
    box-shadow: none;
}

.tm-navbar-transparent .uk-navbar {
    background: none;
}

.tm-logo-contrast {
    display: none;
}

.tm-navbar-contrast .uk-navbar {
    color: #fff;
}

.tm-navbar-contrast .uk-navbar-nav>li>a {
    color: rgba(255,255,255,0.7);
}

.tm-navbar-contrast .uk-navbar-nav>li:hover>a,.tm-navbar-contrast .uk-navbar-nav>li>a:focus,.tm-navbar-contrast .uk-navbar-nav>li.uk-open>a {
    color: #fff;
}

.tm-navbar-contrast .uk-navbar-nav>li>a:active {
    color: #fff;
}

.tm-navbar-contrast .uk-navbar-nav>li.uk-active>a {
    color: #fff;
}

.tm-navbar-contrast .uk-navbar-content>a:not([class]) {
    color: rgba(255,255,255,0.7);
}

.tm-navbar-contrast .uk-navbar-content>a:not([class]):hover {
    color: #fff;
}

.tm-navbar-contrast .uk-navbar-brand {
    color: rgba(255,255,255,0.7);
}

.tm-navbar-contrast .uk-navbar-brand:hover,.tm-navbar-contrast .uk-navbar-brand:focus {
    color: #fff;
}

.tm-navbar-contrast .uk-navbar-toggle {
    color: rgba(255,255,255,0.7);
}

.tm-navbar-contrast .uk-navbar-toggle:hover,.tm-navbar-contrast .uk-navbar-toggle:focus {
    color: #fff;
}


.tm-navbar-contrast .tm-logo {
    display: none;
}

.tm-navbar-contrast .tm-logo-contrast {
    display: inline-block;
}


.uk-navbar-secondary {    background: #222121;
    
}
.uk-navbar-secondary .uk-navbar-nav>li.uk-active>a {
    background-color: transparent;
     color: #fff;
    font-weight: bold;
    border-bottom: 3px solid #ed213a;
    text-decoration: none;
}


.uk-navbar-secondary .uk-navbar-nav>li>a {
    height: 55px;
    padding: 0 15px;
    line-height: 55px;
    font-size: 15px;
    color: #808080;
}


.uk-navbar-secondary .uk-navbar-nav>li>a:hover , .uk-navbar-nav>li>a:active {
  background: transparent;
  color: #fff;
  text-decoration: none;
}
.uk-navbar-secondary .uk-navbar-nav>li>a:hover , .uk-navbar-nav>li>a:active {
  background: transparent;
  color: #fff;
  text-decoration: none;
}
.uk-navbar-flip .uk-navbar-nav>li.uk-open>a, .uk-navbar-nav>li:hover>a, .uk-navbar-nav>li>a:focus {
    background-color: transparent;
    color: #fff!important;
}

.uk-navbar-secondary .uk-nav-navbar>li>a:focus, .uk-nav-navbar>li>a:hover {
    background: #fdc830;
    }
.tm-bg-cover {width: 100%; height: 100%;}


#tm-topbar {
  background: transparent;
}

#tm-topbar  .uk-navbar-nav > li > a{
font-size: 13px;
color: #fff;
}

#tm-topbar  .uk-navbar-nav > li > a:hover, .uk-navbar-nav > li > a:active ,  .uk-navbar-nav > li > a:focus {
 background: transparent!important;
 cursor: pointer;   
 text-decoration: none;
 opacity: 0.8;

}

#tm-header {
  background: #222121c8; padding: 20px 0;}

#tm-header .uk-navbar-brand {
    font-family: "Ubuntu", serif;
    font-size: 25px;
    color: #fff;
    text-decoration: none;
    text-transform: uppercase;
    font-weight: bold;
}

#tm-header   .uk-navbar-nav > li > a{
color: #fff;
}
#tm-header   .uk-button-link{
font-size: 16px;
color: #fff;
text-transform: none;
}
#tm-header   .uk-button-link:hover,  .uk-button-link:active{
 
   opacity: 0.7;
   text-decoration: none;
outline: none;
}




/* Media Page 
 ========================================================================== */




#tm-media-section .uk-scrollable-box {
    box-sizing: border-box;
    height: 500px;
    padding: 10px;
    border: 0;

}

#tm-media-section .uk-tab > li.uk-active > a{
    border-bottom: 4px solid #249af2;
font-weight: bold;
color: #fff;
border-left: 0;
border-right: 0;
border-top:0;

  }

  #tm-media-section .uk-tab > li.uk-active > a:hover {
    border-bottom: 4px solid #249af2;
    font-weight: bold;
    color: #fff;
    background: inherit;
  }

#tm-media-section .uk-tab > li > a {
    height: 40px;
    margin-left: 0;
    line-height: 40px;
    font-size: 16px;
    letter-spacing: .5px;
    text-decoration: none;
    text-transform: uppercase;
    color: #808080;

}

#tm-media-section .tm-scroll-box {
    height: 500px;
    overflow: auto;
}

#tm-media-section .uk-tab > li > a:hover {
    border: none;
    background: #222121;
    color: #fff;
    border-bottom: 4px solid transparent;
}

#tm-media-section .media-cover {
    margin-top: -85px;
}

#tm-media-section .uk-tab-grid ul  {
    border:0;
}

/* Main Content
 ========================================================================== */
#tm-right-section .uk-overlay img {
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
}
/* Main section
 ========================================================================== */
#tm-right-section  .rating {color: #ED213A}

#tm-right-section .uk-overlay-icon:before {
    content: "\f03d";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 50px;
    height: 50px;
    margin-top: -25px;
    margin-left: -25px;
    font-size: 35px;
    line-height: 1;
    font-family: FontAwesome;
    text-align: center;
    color: rgba(255, 255, 255, 0.59);
}

#tm-right-section .uk-overlay-background {
    background: #ED213A; 
    background: -webkit-linear-gradient(to right, #93291E, #ED213A);  
    background: linear-gradient(to right, #93291E, #ED213A); 
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
}

#tm-right-section .uk-panel-title {padding-top: 10px; color: #fff; font-size: 15px; margin-bottom: 5px;}

#tm-right-section .uk-panel {padding: 10px;}

#tm-right-section .uk-panel p {margin-top: 5px;}

#tm-left-section .uk-form {padding: 10px;}

#tm-left-section h5.widget-header{    
    padding: 5px 15px;
    text-transform: uppercase;
    font-weight: bold;
    font-size: 12px;
    color: #ECECEC;
}

#tm-left-section .uk-nav-comments > li > a {
    font-size: 15px;
    color: #fff;
} 

#tm-left-section .uk-nav-comments > li>a>div {
   color: gray;
   padding-bottom: 10px;
}

/* Footer
 ========================================================================== */
#tm-footer .uk-subnav {margin: 0px; line-height: 40px;}

#tm-footer .copyright-text {    line-height: 60px;}

#tm-footer .uk-subnav>* {
   
    padding-left: 15px;

}

#tm-footer .uk-subnav>*>* {
    display: inline-block;
    color: #ECECEC;
}

#tm-footer .uk-subnav>*>*:hover {
   opacity: 0.8;
}

/* Sub-object
 ========================================================================== */
/*
 * Removes inner padding and border in Firefox 4+.
 */
.uk-search-field::-moz-focus-inner {
  border: 0;
  padding: 0;
}
/*
 * Remove inner padding and search cancel button in Chrome, Safari and Opera on OS X.
 */
.uk-search-field::-webkit-search-cancel-button,
.uk-search-field::-webkit-search-decoration {
  -webkit-appearance: none;
}
/*
 * Removes cancel button in IE10
 */
.uk-search-field::-ms-clear {
  display: none;
}
/*
 * Removes placeholder transparency in Firefox.
 */
.uk-search-field::-moz-placeholder {
  opacity: 1;
}
/*
 * 1. Define consistent box sizing.
 * 2. Address margins set differently in Firefox/IE and Chrome/Safari/Opera.
 * 3. Remove `border-radius` in iOS.
 * 4. Correct `font` properties and `color` not being inherited.
 * 5. Remove default style in iOS.
 * 6. Style
 */
.uk-search-field {
  /* 1 */
  box-sizing: border-box;
  /* 2 */
  margin: 0;
  /* 3 */
  border-radius: 0;
  /* 4 */
  font: inherit;
  color: #fff;
  /* 5 */
  -webkit-appearance: none;
  /* 6 */
  width: 120px;
  height: 30px;
  padding: 0 0 0 30px;
  border: 1px solid rgba(0, 0, 0, 0);
  background: rgba(0, 0, 0, 0);
  -webkit-transition: all 0.2s linear;
  transition: all 0.2s linear;
  vertical-align: middle;
  -webkit-border-radius: 4px;
-moz-border-radius: 4px;
border-radius: 4px;
}
/* Placeholder */
.uk-search-field:-ms-input-placeholder {
  color: #999999 !important;
}
.uk-search-field::-moz-placeholder {
  color: #999999;
}
.uk-search-field::-webkit-input-placeholder {
  color: #999999;
}
/* Focus */
.uk-search-field:focus {
  outline: 0;
}
/* Focus + Active */
.uk-search-field:focus,
.uk-search.uk-active .uk-search-field {
  width: 180px;
}
/* Dropdown modifier: `uk-dropdown-search`
 ========================================================================== */
.uk-dropdown-search {
  width: 300px;
  margin-top: 0;
  background: #f5f5f5;
  color: #444444;
}
.uk-open > .uk-dropdown-search {
  -webkit-animation: uk-slide-top-fixed 0.2s ease-in-out;
  animation: uk-slide-top-fixed 0.2s ease-in-out;
}
/*
 * Dependency `uk-navbar-flip`
 */
.uk-navbar-flip .uk-dropdown-search {
  margin-top: 5px;
  margin-right: -15px;
}
/* Nav modifier
 ========================================================================== */
/*
 * Item
 */
.uk-nav-search > li > a {
  color: #444444;
}
/*
 * Active.
 * 1. Remove default focus style.
 */
.uk-nav-search > li.uk-active > a {
  background: #00a8e6;
  color: #ffffff;
  /* 1 */
  outline: none;
}
/*
 * Sub-object: `uk-nav-header`
 */
.uk-nav-search .uk-nav-header {
  color: #999999;
}
/*
 * Sub-object: `uk-nav-divider`
 */
.uk-nav-search .uk-nav-divider {
  border-top: 1px solid #dddddd;
}
/*
 * Nested items
 */
.uk-nav-search ul a {
  color: #0077dd;
}
.uk-nav-search ul a:hover {
  color: #005599;
}
/* Search in offcanvas
 ========================================================================== */
.uk-offcanvas .uk-search {
  display: block;
  margin: 20px 15px;
}
.uk-offcanvas .uk-search:before {
  color: #777777;
}
.uk-offcanvas .uk-search-field {
  width: 100%;
  border-color: rgba(0, 0, 0, 0);
  background: #1a1a1a;
  color: #cccccc;
}
.uk-offcanvas .uk-search-field:-ms-input-placeholder {
  color: #777777 !important;
}
.uk-offcanvas .uk-search-field::-moz-placeholder {
  color: #777777;
}
.uk-offcanvas .uk-search-field::-webkit-input-placeholder {
  color: #777777;
}
 /* ************************** Style for the fixed link ************************** */
 .download-app {
  position: fixed;
  right: 10px;
  bottom: 0;
  transform: translateY(-50%);
  background-color: #ED213A;
  color: white;
  padding: 10px 20px;
  text-decoration: none !important;
  font-size: 16px;
  border-radius: 5px;
  font-weight: bold;
  z-index: 1;
  transition: background-color 0.3s ease-in-out, transform 0.3s ease-in-out;
}
/* ************************** Hover effect ************************** */
.download-app:hover {
  background-color: #C81D30; /* Slightly darker shade for hover */
  color: #000000;
  transform: translateY(-50%) scale(1.05); /* Slightly enlarges the button on hover */
}
/* ************************** Hover effect ************************** */
.scroll-tray {
  white-space: nowrap;
}
.scroll-tray div {
  display: inline-block;
}

.scroll-tray img {
  border-radius: 0.3rem;
  margin: 10px 5px;
  padding: 0;
  width:200px;
}

.scroll-holder {
  overflow-x: hidden;
}

.scroll-holder .scroll-tray:first-child {
  animation: scroll-tray-hero 180s linear infinite;
}

.scroll-holder .scroll-tray:last-child {
  animation: scroll-tray-hero 200s linear infinite;
}

@keyframes scroll-tray-hero {
  0% {
    transform: translateX(0);
  }

  99.99% {
    transform: translateX(-7098px);
  }

  100% {
    transform: translateX(0);
  }
}
.responsive-iframe {
  position: relative;
  width: 100%;
  padding-top: 56.25%; /* 16:9 Aspect Ratio (divide 9 by 16 = 0.5625) */
}

.responsive-iframe iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
}

@media (max-width: 768px) {
 
    #tm-media-section {
      width: 100%; /* Ensure full-width for the section */
      padding: 2px;
    }
    
    .uk-container {
      max-width: 100%; /* Allow container to stretch fully */
      padding: 0; /* Remove padding */
    }
    
    .uk-width-8-10 {
      width: 100%; /* Override 8/10 to full width */
    }

}

/* The main container for the language selector */
.language-selector {
  position: relative;
  display: inline-block;
  border: none;
  z-index: 100;
}

/* The button that shows the selected language */
.language-toggle {
  padding: 13px 15px ;
  background-color:transparent;
  cursor: pointer;
  color: white;
  border: 0;
}

/* The dropdown list of language options, hidden by default */
.language-dropdown {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  background-color: black;
  border-radius: 5px;
  margin-top: 5px;
  list-style-type: none;
  padding: 0;
  width: 100%;
}

/* Display the dropdown when the button is clicked */
.language-selector.open .language-dropdown {
  display: block;
}

/* Individual language option styles */
.language-option {
  padding: 8px 20px 10px 10px;
  cursor: pointer;
  font-size: 14px;
}

.language-option a {
  color: white;
  text-decoration: none;
  display: block;
}

.language-option:hover {
  background-color: #242323;
}

/* Highlight the active language */
.language-option a.active-language {
  font-weight: bold;
  color: rgb(116, 8, 8); /* Adjust color for active language */
}

/* Optional: Styling for the button when the dropdown is open */
.language-selector.open .language-toggle {
  background-color: #f0f0f0;
}

/* The main container for the language selector */
.menu-toggle {
  padding: 13px 15px ;
  background-color:transparent;
  cursor: pointer;
  color: white;
  border: 0;
}

.menu-dropdown {
  display: none;
  position: absolute;
  top: 100%;
  right: 20px;
  background-color: black;
  border-radius: 5px;
  margin-top: 2px;
  list-style-type: none;
  padding-left: 10;
  padding-right: 10;
  width: 200px;
}
.menu-link{
  padding: 5px;
  text-decoration: none; /* Removes underline */
  color: white; 
  padding-top: 5px;
  padding-bottom: 5px;
  width: 100%;
}
.menu-link:hover{
  text-decoration: none; /* Removes underline */
  color: rgb(156, 16, 16); 
}
.menu-selector {
  position: relative;
  display: inline-block;
  border: none;
  z-index: 100;
}
.details_movies{
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 20px;
  margin-top: 10px;
  margin-bottom: 10px;
}
.content_movie{
  width: 500px;
  height: 490px;
  border-radius: 15px;
  background-color: rgb(38, 36, 36);
  display: flex;
  flex-direction: column; 
  padding: 10px;
  gap: 5px;
  
}
.image_movies{
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  width: 350px;
  height: auto;

}
.image_movies img{
width: 350px;
height: 500px;
}
.Episode_number{
 position: absolute;
 width: 100px;
 border-radius: 6px;
 background-color: rgba(145, 4, 4, 0.892);
 color: white;
 border: 0;
 top: 0px;
 right: 0px;
 display: flex;
 justify-content: center;
 align-items: center;
 height: 30px;
}
.Episode_number b{
  font-size: 18px;
}

@media screen and (max-width: 500px) {
 .content_movie{
  height: auto;

 }
 .details_movie{
  height: auto;
  width: 100%;
  
 }
 .display_serie{
  gap: 20px !important;
  margin-left: 0px !important;
  margin-right: 0px !important;
  justify-content: center !important;
}
.img_serie , .img_episodes{
  width: 150px !important;
  height: 232px !important;
  border-radius: 10px;
}

}
.img_serie{
 width: 240px; height: 340px; 
 border-radius:10px;
}
.img_episodes{
  width: 200px; height: 280px; 
  border-radius:10px;
}
.display_serie{

  gap: 30px;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: center !important;

}

.content_movie .btn_details{
  color: white;
  font-size: 20px;
  border-radius: 5px;
  display: flex ;
  justify-content: start;
  align-items: center;
  gap: 4px;
  flex-direction: row;
  flex-wrap: wrap;
  background-color: rgba(74, 71, 71, 0.53);
  border: 0;
  border-radius: 4px;
  padding: 10px;
  padding-bottom: 20px;
}
.content_movie .btn_details:hover{
  background-color:  rgba(42, 40, 40, 0.53);
}

.details_movie{
  width: 450px;
  height: 490px;
  border-radius: 15px;
  background-color: rgb(38, 36, 36);
  display: flex;
  flex-direction: column; 
  
}
.download_now{
  color: white;
  font-size: 25px;
  display: flex;
  align-items: center;
  border: 0;
  border-radius: 12px;
  padding: 10px;
  background-color: rgb(2, 31, 2);
  cursor: pointer;

}
.download_now span{
  display: flex;
  justify-content: center;
  width: 100%;
}
.download_now:hover {
  background-color: green;
  color: white;
  text-decoration: none;
}

.download_now:hover svg path {
  stroke: white; /* Change SVG stroke color */
}
.watch_now{
  color: white;
  font-size: 25px;
  display: flex;
  align-items: center;
  margin-top: 10px;
  border: 0;
  border-radius: 12px;
  padding: 10px;
  background-color: rgb(39, 1, 1);
  cursor: pointer;

}
.watch_now span{
  display: flex;
  justify-content: center;
  width: 100%;
}
.watch_now:hover {
  background-color: rgb(152, 21, 6);
  color: white;
  text-decoration: none;
}


.watch_now svg .cls-1 {
  stroke: #cb0101; /* Default red color */
}

.watch_now:hover svg .cls-1 {
  stroke: white; /* Changes to white on hover */
}

.theme-btn-two {
  position: relative;
  display: flex;
  gap: 10px;

  font-size: 15px;
  color: white;
  line-height: 26px;
  font-family: "Ubuntu", serif;
  font-weight: 700;

  padding: 10px;
  border-radius: 5px;
  border: 1px solid #e5e5e5;
  border-radius: 4px;
  text-transform: uppercase;
  z-index: 1;
}


.theme-btn-two:before {
  position: absolute;
  content: '';
  width: 0%;
  height: 100%;
  left: 0px;
  top: 0px;
  border-radius: 4px;
  z-index: -1;
  transition: all 500ms ease;
}

.theme-btn-two:hover:before {
  width: 100%;
}

.theme-btn-two:after {
  position: absolute;
  content: '';
  background-color: red;
  height: 100%;
  width: 70px;
  right: 0px;
  top: 0px;
  clip-path: polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%, 30% 0%);
  opacity: 0.2;
  z-index: -1;
}
.theme-btn-two:hover{
  background-color: rgba(172, 9, 9, 0.284);
}
.theme-btn-two:hover .item_dtails_info{
  background-color: transparent;
}
.desc{
  padding: 10px;
  gap: 10px;
}
.span_details{
  display: flex;
  justify-content: start;
  flex-direction: row;
  padding-left: 20px;
  padding-right: 20px;
  height: 20px;
  line-height: 25px;
  width: 100%;
 
  
}

.item_dtails_info{
  background-color: red;
  border-radius: 12px;
  text-align: center;
  padding-left: 10px;
  padding-right: 10px;
  font-size: 16px;
  margin-left: 10px;
  margin-right: 10px;
}

.input_lien{
  height: 50px;
  border: white solid 2px;
  border-radius: 12px;
  color:  white;
  margin-left: 10px;
  width: 98%;
  
  background-color: transparent;
}
.Copy_Lien{
  width: 100%;
  border-radius: 12px;
  text-align: center;
  margin-top: 10px;
  background-color: rgb(4, 71, 4);
  height: 30px;
  border: 0;
  font-size: 20px;
  color: white;
}
.Copy_Lien:hover{
  background-color: rgb(2, 48, 2);
}
.btn_servers{
  width: 100%;
  margin-top: 10px;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-around;
}
.btn_server{
  color: white;
  font-size: 20px;
  height: 40px;
  display: flex;
  gap: 5px;
  justify-content: center;
  align-items: center;
  background-color: rgb(111, 5, 5);
  border: 0;
  border-radius: 4px;
  text-decoration: none;
  width: 24%;
}

.btn_server:hover{
  background-color: rgb(205, 4, 4);
  text-decoration: none;
  color:white
}



.scroll-tray-movies {
  white-space: nowrap;
}
.scroll-tray-movies div {
  display: inline-block;
}

.scroll-tray-movies img {
  border-radius: 0.3rem;
  margin: 10px 5px;
  padding: 0;
  width:200px;
}

.Genre_movies{
  text-decoration: none;
  color: white;
  font-size: 25px;
  width: 100%;
}
.Genre_movies:hover{
  color: white;
  text-decoration: none;
}

.products-slider{
  box-sizing: border-box;
}
.slider-heading{
  font-family:"Ubuntu", serif;
  display: flex;
  flex-wrap: wrap;
  max-width: 2200px;

}


.lSPager{
display:none;
}
.lSAction > a {

background-color:#00000033;
width:50px;
height:50px;
border-radius:50%;
opacity:0.8;
background-image:none;
display: flex;
justify-content: center;
align-items: center;
}
.lSAction > a::after{
position:absoult;
content:'';
margin:auto;
border: solid #ffffff;
border-width: 0 4px 4px 0;
display: inline-block;
padding: 5px;
}
.lSAction > .lSPrev::after {
transform: rotate(135deg);
-webkit-transform: rotate(135deg);
}

.lSAction > .lSNext::after {
transform: rotate(-45deg);
-webkit-transform: rotate(-45deg);
}


.title_movie{
  position: absolute ;
  bottom: 0%;
  width: 100%;
  text-align: center;
  padding: 180px 0px 20px 0px;
  margin: 0;

  /* border-radius: 10px; */
  display: flex;
  justify-content: center;
  color:white;
  align-items: center;
  background: linear-gradient(to bottom, transparent 0,rgba(0, 0, 0, 0) 0, #000 97%);
  font-size: 14px;
   font-family: "Ubuntu", serif;
  font-weight: 600;
  line-height: 23px;
}
/*making-responsive-css-------*/
@media(max-width:600px){
.lSAction > a{
  display:none;
}
.btn_server{
    width: 150px;
     margin-top: 5px;
}
.products-slider{
  padding: 20px;}
  .responsive_movie_image{
    width: 200px;
    height: 292px;
  }
  .title_movie{
    font-size: 11px;
    font-weight: 400;
    line-height: 20px;
  }
  .slider-heading{
    font-size: 16px;
    margin-left: 10px;
    && svg {
      width: 20px;
      height: 20px;
    }
  }
  .see_More{
    font-size: 16px;
    && svg {
      width: 20px;
      height: 20px;
    }
  }
}

@media(max-width:800px){
  .products-slider{
    padding: 20px;}
    .responsive_movie_image{
      width: 150px;
      height: 242;
    }
  }


.select_slider{
  padding-top: 10px;
  padding-bottom: 10px;
}
.item_slider:hover .movie_slider_hover {

  opacity: 1;

}
.item_slider{
  overflow: hidden;
  border-radius: 10px
}
.movie_slider_hover{
  opacity: 0;
  transition: 0.5s;
  width: 100%;
  height: 100%;
  position: absolute;
  background: linear-gradient(180deg,#000000c8 5%, transparent 95%);
  top: 0px;
}
.genre_slider{
  width: auto;
  padding-left: 5px;
  padding-right: 5px;
  padding-bottom: 3px;
  padding-top: 3px;
  border-radius: 12px;
  background-color: #ffcb17;;
}

.dt_movies{
  z-index: 12; background: transparent; 
  color: #000; width: auto;padding-left: 4px ;
  padding-right: 4px;
  font-family: "Ubuntu", serif;
  display: flex;
  flex-wrap: wrap ;
  flex-direction: row;
  gap:5px;
}
.discription_show{
  font-size: 15px;
  line-height: 20px;
  font-family: "Ubuntu", serif;
  font-weight: 400;
}
.see_More{
  border: 0;
  background-color: transparent;
  font-family:"Ubuntu", serif;
  font-size: 23px;
  color: white;
}