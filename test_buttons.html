<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الأزرار - فلكسي TV</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .button {
            background: #dc2626;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            margin: 10px;
            cursor: pointer;
            transition: background 0.3s;
        }
        .button:hover {
            background: #b91c1c;
        }
        .button.green {
            background: #16a34a;
        }
        .button.green:hover {
            background: #15803d;
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            background: #374151;
            border-radius: 8px;
            display: none;
        }
    </style>
</head>
<body>
    <h1>🧪 اختبار أزرار فلكسي TV</h1>
    
    <div>
        <button class="button" onclick="testSimpleAlert()">
            🚀 اختبار Alert بسيط
        </button>
        
        <button class="button green" onclick="testConsoleLog()">
            📝 اختبار Console Log
        </button>
        
        <button class="button" onclick="testPageReload()">
            🔄 اختبار إعادة التحميل
        </button>
        
        <button class="button green" onclick="testLocalStorage()">
            💾 اختبار Local Storage
        </button>
    </div>
    
    <div id="result" class="result">
        <h3>النتيجة:</h3>
        <p id="resultText"></p>
    </div>

    <script>
        // دالة اختبار Alert بسيط
        function testSimpleAlert() {
            alert('✅ الزر يعمل بشكل مثالي!');
            showResult('تم تشغيل Alert بنجاح');
        }

        // دالة اختبار Console Log
        function testConsoleLog() {
            console.log('🔄 تم النقر على زر Console Log');
            console.log('⏰ الوقت:', new Date().toLocaleString());
            showResult('تم كتابة رسالة في Console - افتح Developer Tools للمشاهدة');
        }

        // دالة اختبار إعادة التحميل
        function testPageReload() {
            if (confirm('هل تريد إعادة تحميل الصفحة؟')) {
                window.location.reload();
            } else {
                showResult('تم إلغاء إعادة التحميل');
            }
        }

        // دالة اختبار Local Storage
        function testLocalStorage() {
            const testData = 'test_' + Date.now();
            localStorage.setItem('flexiTV_test', testData);
            const retrieved = localStorage.getItem('flexiTV_test');
            
            if (retrieved === testData) {
                showResult('✅ Local Storage يعمل بشكل مثالي');
                localStorage.removeItem('flexiTV_test');
            } else {
                showResult('❌ مشكلة في Local Storage');
            }
        }

        // دالة عرض النتيجة
        function showResult(message) {
            const resultDiv = document.getElementById('result');
            const resultText = document.getElementById('resultText');
            
            resultText.textContent = message;
            resultDiv.style.display = 'block';
            
            // إخفاء النتيجة بعد 5 ثوان
            setTimeout(() => {
                resultDiv.style.display = 'none';
            }, 5000);
        }

        // رسالة ترحيب
        window.onload = function() {
            console.log('🎯 صفحة اختبار الأزرار محملة بنجاح');
            console.log('📱 User Agent:', navigator.userAgent);
            console.log('🌐 URL:', window.location.href);
        };
    </script>
</body>
</html>
