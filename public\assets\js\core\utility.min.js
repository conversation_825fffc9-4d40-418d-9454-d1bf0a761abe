/*! UIkit 2.26.3 | http://www.getuikit.com | (c) 2014 YOOtheme | MIT License */
!function(t){"use strict";var i=[];t.component("stackMargin",{defaults:{cls:"uk-margin-small-top",rowfirst:!1,observe:!1},boot:function(){t.ready(function(i){t.$("[data-uk-margin]",i).each(function(){var i=t.$(this);i.data("stackMargin")||t.stackMargin(i,t.Utils.options(i.attr("data-uk-margin")))})})},init:function(){var n=this;t.$win.on("resize orientationchange",function(){var i=function(){n.process()};return t.$(function(){i(),t.$win.on("load",i)}),t.Utils.debounce(i,20)}()),this.on("display.uk.check",function(){this.element.is(":visible")&&this.process()}.bind(this)),this.options.observe&&t.domObserve(this.element,function(){n.element.is(":visible")&&n.process()}),i.push(this)},process:function(){var i=this.element.children();if(t.Utils.stackMargin(i,this.options),!this.options.rowfirst||!i.length)return this;var n={},e=!1;return i.removeClass(this.options.rowfirst).each(function(i,s){s=t.$(this),"none"!=this.style.display&&(i=s.offset().left,((n[i]=n[i]||[])&&n[i]).push(this),e=e===!1?i:Math.min(e,i))}),t.$(n[e]).addClass(this.options.rowfirst),this}}),function(){var i=[],n=function(t){if(t.is(":visible")){var i=t.parent().width(),n=t.data("width"),e=i/n,s=Math.floor(e*t.data("height"));t.css({height:n>i?s:t.data("height")})}};t.component("responsiveElement",{defaults:{},boot:function(){t.ready(function(i){t.$("iframe.uk-responsive-width, [data-uk-responsive]",i).each(function(){var i,n=t.$(this);n.data("responsiveElement")||(i=t.responsiveElement(n,{}))})})},init:function(){var t=this.element;t.attr("width")&&t.attr("height")&&(t.data({width:t.attr("width"),height:t.attr("height")}).on("display.uk.check",function(){n(t)}),n(t),i.push(t))}}),t.$win.on("resize load",t.Utils.debounce(function(){i.forEach(function(t){n(t)})},15))}(),t.Utils.stackMargin=function(i,n){n=t.$.extend({cls:"uk-margin-small-top"},n),i=t.$(i).removeClass(n.cls);var e=!1;i.each(function(i,n,s,a){a=t.$(this),"none"!=a.css("display")&&(i=a.offset(),n=a.outerHeight(),s=i.top+n,a.data({ukMarginPos:s,ukMarginTop:i.top}),(e===!1||i.top<e.top)&&(e={top:i.top,left:i.left,pos:s}))}).each(function(i){i=t.$(this),"none"!=i.css("display")&&i.data("ukMarginTop")>e.top&&i.data("ukMarginPos")>e.pos&&i.addClass(n.cls)})},t.Utils.matchHeights=function(i,n){i=t.$(i).css("min-height",""),n=t.$.extend({row:!0},n);var e=function(i){if(!(i.length<2)){var n=0;i.each(function(){n=Math.max(n,t.$(this).outerHeight())}).each(function(){var i=t.$(this),e=n-("border-box"==i.css("box-sizing")?0:i.outerHeight()-i.height());i.css("min-height",e+"px")})}};n.row?(i.first().width(),setTimeout(function(){var n=!1,s=[];i.each(function(){var i=t.$(this),a=i.offset().top;a!=n&&s.length&&(e(t.$(s)),s=[],a=i.offset().top),s.push(i),n=a}),s.length&&e(t.$(s))},0)):e(i)},function(i){t.Utils.inlineSvg=function(n,e){t.$(n||'img[src$=".svg"]',e||document).each(function(){var n=t.$(this),e=n.attr("src");if(!i[e]){var s=t.$.Deferred();t.$.get(e,{nc:Math.random()},function(i){s.resolve(t.$(i).find("svg"))}),i[e]=s.promise()}i[e].then(function(i){var e=t.$(i).clone();n.attr("id")&&e.attr("id",n.attr("id")),n.attr("class")&&e.attr("class",n.attr("class")),n.attr("style")&&e.attr("style",n.attr("style")),n.attr("width")&&(e.attr("width",n.attr("width")),n.attr("height")||e.removeAttr("height")),n.attr("height")&&(e.attr("height",n.attr("height")),n.attr("width")||e.removeAttr("width")),n.replaceWith(e)})})},t.ready(function(i){t.Utils.inlineSvg("[data-uk-svg]",i)})}({})}(UIkit);