// ===== ملف JavaScript محسن لصفحة البث المباشر =====

// متغيرات عامة
let allChannels = [];
let lastChannelUrl = null;
let showingFavorites = false;
let isSideBySide = true;

// دالة ملء الشاشة
function toggleFullscreen() {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen().catch(err => {
      showNotification('error', 'لا يمكن تفعيل ملء الشاشة');
    });
  } else {
    document.exitFullscreen();
  }
}

// دالة مشاركة القناة
function shareChannel() {
  if (lastChannelUrl) {
    if (navigator.share) {
      navigator.share({
        title: 'فلكسي TV - قناة مباشرة',
        text: 'شاهد هذه القناة المباشرة',
        url: window.location.href
      });
    } else {
      // نسخ الرابط للحافظة
      navigator.clipboard.writeText(window.location.href).then(() => {
        showNotification('success', 'تم نسخ الرابط إلى الحافظة');
      });
    }
  } else {
    showNotification('error', 'لا توجد قناة مشغلة حالياً');
  }
}

// دالة كتم الصوت
function toggleMute() {
  const video = document.getElementById('videoElement');
  const muteIcon = document.getElementById('muteIcon');
  
  if (video.muted) {
    video.muted = false;
    muteIcon.textContent = '🔊';
  } else {
    video.muted = true;
    muteIcon.textContent = '🔇';
  }
}

// دالة عرض الإشعارات المحسنة
function showNotification(type, message, duration = 3000) {
  const notifications = {
    success: document.getElementById('successNotice'),
    error: document.getElementById('errorNotice'),
    loading: document.getElementById('loadingNotice'),
    favorite: document.getElementById('favNotification')
  };
  
  const notification = notifications[type];
  if (!notification) return;
  
  // تحديث الرسالة
  if (type === 'success') {
    document.getElementById('successMessage').textContent = message;
  } else if (type === 'error') {
    document.getElementById('errorMessage').textContent = message;
  }
  
  // إظهار الإشعار
  notification.classList.remove('hidden');
  notification.classList.add('slide-up');
  
  // إخفاء الإشعار بعد المدة المحددة
  setTimeout(() => {
    notification.classList.add('hidden');
    notification.classList.remove('slide-up');
  }, duration);
}

// تحسين البحث السريع
function initializeSearch() {
  const searchInput = document.getElementById('searchInput');
  const clearBtn = document.getElementById('clearSearch');
  const searchResults = document.getElementById('searchResults');
  const resultsList = document.getElementById('searchResultsList');
  
  searchInput.addEventListener('input', function(e) {
    const term = e.target.value.toLowerCase().trim();
    
    // إظهار/إخفاء زر المسح
    if (term) {
      clearBtn.classList.remove('hidden');
    } else {
      clearBtn.classList.add('hidden');
      searchResults.classList.add('hidden');
      displayChannels('all');
      return;
    }
    
    // البحث في القنوات
    const filtered = allChannels.filter(ch => 
      ch.title.toLowerCase().includes(term) || 
      ch.group.toLowerCase().includes(term)
    );
    
    // عرض النتائج السريعة
    if (filtered.length > 0 && term.length > 1) {
      resultsList.innerHTML = '';
      filtered.slice(0, 5).forEach(channel => {
        const item = document.createElement('div');
        item.className = 'flex items-center gap-3 p-2 hover:bg-gray-700 rounded cursor-pointer';
        item.innerHTML = `
          <img src="${channel.logo}" class="w-8 h-8 rounded object-cover" />
          <div class="flex-1">
            <div class="text-sm font-medium">${channel.title}</div>
            <div class="text-xs text-gray-400">${channel.group}</div>
          </div>
        `;
        item.addEventListener('click', () => {
          checkAndPlayChannel(channel.url);
          searchResults.classList.add('hidden');
        });
        resultsList.appendChild(item);
      });
      searchResults.classList.remove('hidden');
    } else {
      searchResults.classList.add('hidden');
    }
    
    // تصفية القنوات المعروضة
    displayFilteredChannels(filtered);
  });
  
  // زر مسح البحث
  clearBtn.addEventListener('click', function() {
    searchInput.value = '';
    this.classList.add('hidden');
    searchResults.classList.add('hidden');
    displayChannels('all');
  });
}

// تحسين عداد المفضلة
function updateFavoriteCount() {
  const favCount = document.getElementById('favCount');
  const favorites = getFavorites();
  
  if (favorites.length > 0) {
    favCount.textContent = favorites.length;
    favCount.classList.remove('hidden');
  } else {
    favCount.classList.add('hidden');
  }
}

// تحسين دالة إضافة/إزالة المفضلة
function toggleFavorite(event, url) {
  event.stopPropagation();
  let favorites = getFavorites();
  let message;
  
  if (favorites.includes(url)) {
    favorites = favorites.filter(f => f !== url);
    message = 'تمت الإزالة من المفضلة';
    showNotification('error', message);
  } else {
    favorites.push(url);
    message = 'تم الإضافة إلى المفضلة';
    showNotification('favorite', message);
  }
  
  localStorage.setItem('favorites', JSON.stringify(favorites));
  updateFavoriteCount();
  displayChannels();
}

// تحسين عرض القنوات مع الأنيميشن
function displayChannels(selected = 'all') {
  const container = document.getElementById('channelGroups');
  container.innerHTML = '';
  const groups = [...new Set(allChannels.map(c => c.group))];
  
  groups.forEach((group, groupIndex) => {
    if (selected !== 'all' && selected !== group) return;
    
    const section = document.createElement('div');
    section.className = 'fade-in';
    section.style.animationDelay = `${groupIndex * 0.1}s`;
    section.innerHTML = `<h2 class="text-xl font-bold mb-4 text-gradient bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">${group}</h2>`;
    
    const grid = document.createElement('div');
    grid.className = 'grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4';
    
    allChannels.filter(c => c.group === group).forEach((channel, index) => {
      const card = document.createElement('div');
      card.className = 'channel-card fade-in';
      card.style.animationDelay = `${(groupIndex * 0.1) + (index * 0.05)}s`;
      
      const isFavorite = getFavorites().includes(channel.url);
      card.innerHTML = `
        <div class="relative">
          <img src="${channel.logo}" class="w-full object-contain rounded-lg mx-auto transition-transform duration-300" style="max-height: 60px;" />
          <div class="absolute top-2 right-2">
            <button onclick="toggleFavorite(event, '${channel.url}')" class="text-yellow-400 hover:text-yellow-300 transition-colors duration-200">
              ${isFavorite ? '⭐' : '☆'}
            </button>
          </div>
        </div>
        <div class="text-sm mt-3 text-center font-medium truncate">${channel.title}</div>
        <div class="text-xs text-gray-400 text-center mt-1">${channel.group}</div>
      `;
      
      card.addEventListener('click', e => {
        if (e.target.tagName !== 'BUTTON') {
          checkAndPlayChannel(channel.url);
          // إضافة تأثير النقر
          card.style.transform = 'scale(0.95)';
          setTimeout(() => {
            card.style.transform = '';
          }, 150);
        }
      });
      
      grid.appendChild(card);
    });
    
    section.appendChild(grid);
    container.appendChild(section);
  });
  
  updateFavoriteCount();
}

// تحسين تحديث شريط التصنيفات
function updateCategoryBar() {
  const categoryMenu = document.getElementById("categoriesBar");
  const categoryBtn = document.getElementById('categoryDropdownBtn');
  
  categoryMenu.innerHTML = '';
  const groups = [...new Set(allChannels.map(c => c.group))];
  
  const allBtn = document.createElement('button');
  allBtn.textContent = '📺 كل القنوات';
  allBtn.className = 'block w-full text-right px-4 py-3 text-sm hover:bg-gray-700 transition-colors duration-200 rounded-lg mb-1';
  allBtn.onclick = () => {
    categoryBtn.innerHTML = `
      <span class="text-lg">📂</span>
      <span class="font-semibold">كل القنوات</span>
      <svg class="w-4 h-4 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
      </svg>
    `;
    displayChannels('all');
    categoryMenu.classList.add('hidden');
  };
  categoryMenu.appendChild(allBtn);
  
  groups.forEach(group => {
    const btn = document.createElement('button');
    btn.textContent = group;
    btn.className = 'block w-full text-right px-4 py-3 text-sm hover:bg-gray-700 transition-colors duration-200 rounded-lg mb-1';
    btn.onclick = () => {
      categoryBtn.innerHTML = `
        <span class="text-lg">📂</span>
        <span class="font-semibold">${group}</span>
        <svg class="w-4 h-4 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
        </svg>
      `;
      displayChannels(group);
      categoryMenu.classList.add('hidden');
    };
    categoryMenu.appendChild(btn);
  });
}

// تهيئة المستمعات
function initializeEventListeners() {
  const categoryBtn = document.getElementById('categoryDropdownBtn');
  const categoryMenu = document.getElementById("categoriesBar");
  
  // تحسين دالة تبديل القائمة
  categoryBtn.addEventListener('click', () => {
    const arrow = document.getElementById('categoryArrow');
    categoryMenu.classList.toggle('hidden');
    
    if (categoryMenu.classList.contains('hidden')) {
      arrow.style.transform = 'rotate(0deg)';
    } else {
      arrow.style.transform = 'rotate(180deg)';
    }
  });
  
  // إضافة مستمع للنقر خارج القائمة
  document.addEventListener('click', (e) => {
    if (!categoryBtn.contains(e.target) && !categoryMenu.contains(e.target)) {
      categoryMenu.classList.add('hidden');
      document.getElementById('categoryArrow').style.transform = 'rotate(0deg)';
    }
    
    if (!document.getElementById('searchInput').contains(e.target) && !document.getElementById('searchResults').contains(e.target)) {
      document.getElementById('searchResults').classList.add('hidden');
    }
  });
}

// تهيئة التطبيق
function initializeApp() {
  initializeSearch();
  initializeEventListeners();
  updateFavoriteCount();
}

// تشغيل التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', initializeApp);
