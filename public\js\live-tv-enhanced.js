// ===== ملف JavaScript محسن لصفحة البث المباشر =====

// متغيرات عامة
let allChannels = [];
let lastChannelUrl = null;
let showingFavorites = false;
let isSideBySide = true;

// دالة ملء الشاشة
function toggleFullscreen() {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen().catch(err => {
      showNotification('error', 'لا يمكن تفعيل ملء الشاشة');
    });
  } else {
    document.exitFullscreen();
  }
}

// دالة مشاركة القناة
function shareChannel() {
  if (lastChannelUrl) {
    if (navigator.share) {
      navigator.share({
        title: 'فلكسي TV - قناة مباشرة',
        text: 'شاهد هذه القناة المباشرة',
        url: window.location.href
      });
    } else {
      // نسخ الرابط للحافظة
      navigator.clipboard.writeText(window.location.href).then(() => {
        showNotification('success', 'تم نسخ الرابط إلى الحافظة');
      });
    }
  } else {
    showNotification('error', 'لا توجد قناة مشغلة حالياً');
  }
}

// دالة كتم الصوت
function toggleMute() {
  const video = document.getElementById('videoElement');
  const muteIcon = document.getElementById('muteIcon');

  if (video.muted) {
    video.muted = false;
    muteIcon.textContent = '🔊';
  } else {
    video.muted = true;
    muteIcon.textContent = '🔇';
  }
}

// دالة عرض الإشعارات المحسنة
function showNotification(type, message, duration = 3000) {
  const notifications = {
    success: document.getElementById('successNotice'),
    error: document.getElementById('errorNotice'),
    loading: document.getElementById('loadingNotice'),
    favorite: document.getElementById('favNotification')
  };

  const notification = notifications[type];
  if (!notification) return;

  // تحديث الرسالة
  if (type === 'success') {
    document.getElementById('successMessage').textContent = message;
  } else if (type === 'error') {
    document.getElementById('errorMessage').textContent = message;
  }

  // إظهار الإشعار
  notification.classList.remove('hidden');
  notification.classList.add('slide-up');

  // إخفاء الإشعار بعد المدة المحددة
  setTimeout(() => {
    notification.classList.add('hidden');
    notification.classList.remove('slide-up');
  }, duration);
}

// تحسين البحث السريع
function initializeSearch() {
  const searchInput = document.getElementById('searchInput');
  const clearBtn = document.getElementById('clearSearch');
  const searchResults = document.getElementById('searchResults');
  const resultsList = document.getElementById('searchResultsList');

  searchInput.addEventListener('input', function(e) {
    const term = e.target.value.toLowerCase().trim();

    // إظهار/إخفاء زر المسح
    if (term) {
      clearBtn.classList.remove('hidden');
    } else {
      clearBtn.classList.add('hidden');
      searchResults.classList.add('hidden');
      displayChannels('all');
      return;
    }

    // البحث في القنوات
    const filtered = allChannels.filter(ch =>
      ch.title.toLowerCase().includes(term) ||
      ch.group.toLowerCase().includes(term)
    );

    // عرض النتائج السريعة
    if (filtered.length > 0 && term.length > 1) {
      resultsList.innerHTML = '';
      filtered.slice(0, 5).forEach(channel => {
        const item = document.createElement('div');
        item.className = 'flex items-center gap-3 p-2 hover:bg-gray-700 rounded cursor-pointer';
        item.innerHTML = `
          <img src="${channel.logo}" class="w-8 h-8 rounded object-cover" />
          <div class="flex-1">
            <div class="text-sm font-medium">${channel.title}</div>
            <div class="text-xs text-gray-400">${channel.group}</div>
          </div>
        `;
        item.addEventListener('click', () => {
          checkAndPlayChannel(channel.url);
          searchResults.classList.add('hidden');
        });
        resultsList.appendChild(item);
      });
      searchResults.classList.remove('hidden');
    } else {
      searchResults.classList.add('hidden');
    }

    // تصفية القنوات المعروضة
    displayFilteredChannels(filtered);
  });

  // زر مسح البحث
  clearBtn.addEventListener('click', function() {
    searchInput.value = '';
    this.classList.add('hidden');
    searchResults.classList.add('hidden');
    displayChannels('all');
  });
}

// تحسين عداد المفضلة
function updateFavoriteCount() {
  const favCount = document.getElementById('favCount');
  const favorites = getFavorites();

  if (favorites.length > 0) {
    favCount.textContent = favorites.length;
    favCount.classList.remove('hidden');
  } else {
    favCount.classList.add('hidden');
  }
}

// تحسين دالة إضافة/إزالة المفضلة
function toggleFavorite(event, url) {
  event.stopPropagation();
  let favorites = getFavorites();
  let message;

  if (favorites.includes(url)) {
    favorites = favorites.filter(f => f !== url);
    message = 'تمت الإزالة من المفضلة';
    showNotification('error', message);
  } else {
    favorites.push(url);
    message = 'تم الإضافة إلى المفضلة';
    showNotification('favorite', message);
  }

  localStorage.setItem('favorites', JSON.stringify(favorites));
  updateFavoriteCount();
  displayChannels();
}

// تحسين عرض القنوات مع الأنيميشن والتجاوب
function displayChannels(selected = 'all') {
  const container = document.getElementById('channelGroups');
  container.innerHTML = '';
  const groups = [...new Set(allChannels.map(c => c.group))];

  // تحديد عدد الأعمدة حسب حجم الشاشة
  const isMobile = window.innerWidth < 768;
  const isTablet = window.innerWidth >= 768 && window.innerWidth < 1024;

  groups.forEach((group, groupIndex) => {
    if (selected !== 'all' && selected !== group) return;

    const section = document.createElement('div');
    section.className = 'fade-in mb-6';
    section.style.animationDelay = `${groupIndex * 0.1}s`;

    // عنوان المجموعة متجاوب
    const title = document.createElement('h2');
    title.className = isMobile
      ? 'text-lg font-bold mb-3 text-gradient bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent'
      : 'text-xl font-bold mb-4 text-gradient bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent';
    title.textContent = group;
    section.appendChild(title);

    const grid = document.createElement('div');
    // شبكة متجاوبة محسنة
    if (isMobile) {
      grid.className = 'grid grid-cols-2 gap-3 mobile-grid';
    } else if (isTablet) {
      grid.className = 'grid grid-cols-3 gap-4';
    } else {
      grid.className = 'grid grid-cols-4 lg:grid-cols-5 gap-4';
    }

    allChannels.filter(c => c.group === group).forEach((channel, index) => {
      const card = document.createElement('div');
      card.className = 'channel-card fade-in';
      card.style.animationDelay = `${(groupIndex * 0.1) + (index * 0.05)}s`;

      const isFavorite = getFavorites().includes(channel.url);

      // محتوى البطاقة متجاوب
      card.innerHTML = `
        <div class="relative">
          <img src="${channel.logo}"
               class="w-full object-contain rounded-lg mx-auto transition-transform duration-300"
               style="max-height: ${isMobile ? '50px' : '60px'};"
               loading="lazy" />
          <div class="absolute top-1 right-1 md:top-2 md:right-2">
            <button onclick="toggleFavorite(event, '${channel.url}')"
                    class="text-yellow-400 hover:text-yellow-300 transition-colors duration-200 text-sm md:text-base p-1">
              ${isFavorite ? '⭐' : '☆'}
            </button>
          </div>
        </div>
        <div class="text-xs md:text-sm mt-2 md:mt-3 text-center font-medium truncate mobile-text-sm">${channel.title}</div>
        <div class="text-xs text-gray-400 text-center mt-1 mobile-text-xs">${channel.group}</div>
      `;

      // تحسين التفاعل للجوال
      card.addEventListener('click', e => {
        if (e.target.tagName !== 'BUTTON') {
          checkAndPlayChannel(channel.url);

          // تأثير النقر محسن للجوال
          card.style.transform = 'scale(0.95)';
          card.style.opacity = '0.8';

          setTimeout(() => {
            card.style.transform = '';
            card.style.opacity = '';
          }, 150);
        }
      });

      // تحسين اللمس للجوال
      if (isMobile) {
        card.addEventListener('touchstart', function() {
          this.style.transform = 'scale(0.98)';
        });

        card.addEventListener('touchend', function() {
          setTimeout(() => {
            this.style.transform = '';
          }, 100);
        });
      }

      grid.appendChild(card);
    });

    section.appendChild(grid);
    container.appendChild(section);
  });

  updateFavoriteCount();
  updateChannelCount();
}

// تحسين تحديث شريط التصنيفات
function updateCategoryBar() {
  const categoryMenu = document.getElementById("categoriesBar");
  const categoryBtn = document.getElementById('categoryDropdownBtn');

  categoryMenu.innerHTML = '';
  const groups = [...new Set(allChannels.map(c => c.group))];

  const allBtn = document.createElement('button');
  allBtn.textContent = '📺 كل القنوات';
  allBtn.className = 'block w-full text-right px-4 py-3 text-sm hover:bg-gray-700 transition-colors duration-200 rounded-lg mb-1';
  allBtn.onclick = () => {
    categoryBtn.innerHTML = `
      <span class="text-lg">📂</span>
      <span class="font-semibold">كل القنوات</span>
      <svg class="w-4 h-4 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
      </svg>
    `;
    displayChannels('all');
    categoryMenu.classList.add('hidden');
  };
  categoryMenu.appendChild(allBtn);

  groups.forEach(group => {
    const btn = document.createElement('button');
    btn.textContent = group;
    btn.className = 'block w-full text-right px-4 py-3 text-sm hover:bg-gray-700 transition-colors duration-200 rounded-lg mb-1';
    btn.onclick = () => {
      categoryBtn.innerHTML = `
        <span class="text-lg">📂</span>
        <span class="font-semibold">${group}</span>
        <svg class="w-4 h-4 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
        </svg>
      `;
      displayChannels(group);
      categoryMenu.classList.add('hidden');
    };
    categoryMenu.appendChild(btn);
  });
}

// تهيئة المستمعات
function initializeEventListeners() {
  const categoryBtn = document.getElementById('categoryDropdownBtn');
  const categoryMenu = document.getElementById("categoriesBar");

  // تحسين دالة تبديل القائمة
  categoryBtn.addEventListener('click', () => {
    const arrow = document.getElementById('categoryArrow');
    categoryMenu.classList.toggle('hidden');

    if (categoryMenu.classList.contains('hidden')) {
      arrow.style.transform = 'rotate(0deg)';
    } else {
      arrow.style.transform = 'rotate(180deg)';
    }
  });

  // إضافة مستمع للنقر خارج القائمة
  document.addEventListener('click', (e) => {
    if (!categoryBtn.contains(e.target) && !categoryMenu.contains(e.target)) {
      categoryMenu.classList.add('hidden');
      document.getElementById('categoryArrow').style.transform = 'rotate(0deg)';
    }

    if (!document.getElementById('searchInput').contains(e.target) && !document.getElementById('searchResults').contains(e.target)) {
      document.getElementById('searchResults').classList.add('hidden');
    }
  });
}

// تهيئة التطبيق
function initializeApp() {
  initializeSearch();
  initializeEventListeners();
  updateFavoriteCount();
}

// ===== دوال نافذة M3U للجوال =====

// إظهار نافذة M3U للجوال
function showMobileM3UDialog() {
  const dialog = document.getElementById('mobileM3UDialog');
  const content = document.getElementById('mobileM3UContent');

  dialog.classList.remove('hidden');

  // تأخير قصير لضمان عرض العنصر قبل الأنيميشن
  setTimeout(() => {
    content.style.transform = 'translateY(0)';
  }, 10);

  // منع التمرير في الخلفية
  document.body.style.overflow = 'hidden';
}

// إخفاء نافذة M3U للجوال
function hideMobileM3UDialog() {
  const dialog = document.getElementById('mobileM3UDialog');
  const content = document.getElementById('mobileM3UContent');

  content.style.transform = 'translateY(100%)';

  setTimeout(() => {
    dialog.classList.add('hidden');
    document.body.style.overflow = '';
  }, 300);
}

// تحميل M3U من الرابط في الجوال
function loadMobileM3UUrl() {
  const urlInput = document.getElementById('mobileM3UUrl');
  const url = urlInput.value.trim();

  if (!url) {
    showNotification('error', 'يرجى إدخال رابط صالح');
    return;
  }

  // التحقق من صحة الرابط
  try {
    new URL(url);
  } catch (e) {
    showNotification('error', 'رابط غير صالح');
    return;
  }

  showNotification('loading', 'جارٍ تحميل القائمة...');
  hideMobileM3UDialog();

  fetch(url)
    .then(res => {
      if (!res.ok) throw new Error('فشل في تحميل الملف');
      return res.text();
    })
    .then(content => {
      localStorage.setItem('savedM3UUrl', url);
      localStorage.removeItem('savedM3UContent');
      parseM3U(content);
      showNotification('success', 'تم تحميل القائمة بنجاح');
      urlInput.value = '';
    })
    .catch(error => {
      console.error('خطأ في تحميل M3U:', error);
      showNotification('error', 'فشل في تحميل القائمة');
    });
}

// معالجة رفع ملف M3U في الجوال
function initializeMobileM3UFile() {
  const fileInput = document.getElementById('mobileM3UFile');

  if (fileInput) {
    fileInput.addEventListener('change', function(e) {
      const file = e.target.files[0];
      if (!file) return;

      // التحقق من نوع الملف
      const validTypes = ['.m3u', '.txt'];
      const fileExtension = '.' + file.name.split('.').pop().toLowerCase();

      if (!validTypes.includes(fileExtension)) {
        showNotification('error', 'نوع الملف غير مدعوم. يرجى اختيار ملف M3U أو TXT');
        fileInput.value = '';
        return;
      }

      // التحقق من حجم الملف (حد أقصى 10MB)
      if (file.size > 10 * 1024 * 1024) {
        showNotification('error', 'حجم الملف كبير جداً. الحد الأقصى 10MB');
        fileInput.value = '';
        return;
      }

      showNotification('loading', 'جارٍ قراءة الملف...');
      hideMobileM3UDialog();

      const reader = new FileReader();
      reader.onload = function(e) {
        const content = e.target.result;
        localStorage.setItem('savedM3UContent', content);
        localStorage.removeItem('savedM3UUrl');
        parseM3U(content);
        showNotification('success', 'تم تحميل الملف بنجاح');
        fileInput.value = '';
      };

      reader.onerror = function() {
        showNotification('error', 'فشل في قراءة الملف');
        fileInput.value = '';
      };

      reader.readAsText(file);
    });
  }
}

// تحديث عداد القنوات
function updateChannelCount() {
  const channelCountElement = document.getElementById('channelCount');
  if (channelCountElement && allChannels) {
    channelCountElement.textContent = `• ${allChannels.length} قناة`;
  }
}

// تحسين دالة parseM3U لتشمل تحديث العداد ودعم الجوال
function parseM3U(content) {
  const lines = content.split('\n');
  const channels = [];

  // تحسين معالجة الملف للجوال
  const isMobile = window.innerWidth < 768;

  for (let i = 0; i < lines.length; i++) {
    if (lines[i].startsWith('#EXTINF')) {
      const title = lines[i].split(',')[1]?.trim() || 'بدون اسم';
      const logo = lines[i].match(/tvg-logo="(.*?)"/)?.[1] || 'https://via.placeholder.com/100x100';
      const group = lines[i].match(/group-title="(.*?)"/)?.[1] || 'غير مصنف';
      const url = lines[i + 1]?.trim();

      if (url?.startsWith('http')) {
        channels.push({ title, logo, group, url });
      }
    }
  }

  allChannels = channels;

  // تحديث الواجهة
  updateCategoryBar();
  displayChannels();
  updateChannelCount();
  updateFavoriteCount();

  // إشعار خاص للجوال
  if (isMobile) {
    showNotification('success', `تم تحميل ${channels.length} قناة بنجاح`);

    // حفظ القائمة في التخزين المحلي للجوال
    localStorage.setItem('mobileChannelsCache', JSON.stringify(channels));
    localStorage.setItem('mobileChannelsCacheTime', Date.now().toString());
  }
}

// تحميل القنوات المحفوظة للجوال
function loadCachedChannelsForMobile() {
  const isMobile = window.innerWidth < 768;

  if (isMobile) {
    const cachedChannels = localStorage.getItem('mobileChannelsCache');
    const cacheTime = localStorage.getItem('mobileChannelsCacheTime');

    // التحقق من صحة الكاش (24 ساعة)
    if (cachedChannels && cacheTime) {
      const timeDiff = Date.now() - parseInt(cacheTime);
      const hoursDiff = timeDiff / (1000 * 60 * 60);

      if (hoursDiff < 24) {
        try {
          const channels = JSON.parse(cachedChannels);
          if (channels.length > 0) {
            allChannels = channels;
            updateCategoryBar();
            displayChannels();
            updateChannelCount();
            updateFavoriteCount();

            showNotification('success', `تم تحميل ${channels.length} قناة من الذاكرة`);
            return true;
          }
        } catch (e) {
          console.error('خطأ في تحميل الكاش:', e);
        }
      }
    }
  }

  return false;
}

// مسح الكاش للجوال
function clearMobileCacheIfNeeded() {
  const isMobile = window.innerWidth < 768;

  if (isMobile) {
    const cacheTime = localStorage.getItem('mobileChannelsCacheTime');

    if (cacheTime) {
      const timeDiff = Date.now() - parseInt(cacheTime);
      const daysDiff = timeDiff / (1000 * 60 * 60 * 24);

      // مسح الكاش إذا كان أقدم من 7 أيام
      if (daysDiff > 7) {
        localStorage.removeItem('mobileChannelsCache');
        localStorage.removeItem('mobileChannelsCacheTime');
        showNotification('success', 'تم تنظيف ذاكرة التخزين');
      }
    }
  }
}

// دالة لإعادة تحميل القنوات للجوال
function reloadChannelsForMobile() {
  const savedUrl = localStorage.getItem('savedM3UUrl');
  const savedContent = localStorage.getItem('savedM3UContent');

  if (savedUrl) {
    showNotification('loading', 'جارٍ إعادة تحميل القنوات...');

    fetch(savedUrl)
      .then(res => res.text())
      .then(content => {
        parseM3U(content);
        showNotification('success', 'تم إعادة تحميل القنوات بنجاح');
      })
      .catch(error => {
        console.error('خطأ في إعادة التحميل:', error);
        showNotification('error', 'فشل في إعادة تحميل القنوات');
      });
  } else if (savedContent) {
    parseM3U(savedContent);
    showNotification('success', 'تم إعادة تحميل القنوات من الملف المحفوظ');
  } else {
    showNotification('error', 'لا توجد قائمة محفوظة لإعادة التحميل');
  }
}

// تحسين تجربة اللمس للجوال
function initializeMobileTouch() {
  // منع التكبير المزدوج على الأزرار
  const buttons = document.querySelectorAll('button');
  buttons.forEach(button => {
    button.addEventListener('touchend', function(e) {
      e.preventDefault();
      this.click();
    });
  });

  // تحسين التمرير للقوائم
  const scrollableElements = document.querySelectorAll('.overflow-y-auto');
  scrollableElements.forEach(element => {
    element.style.webkitOverflowScrolling = 'touch';
  });
}

// تحسين الأداء للجوال
function optimizeForMobile() {
  const isMobile = window.innerWidth < 768;

  if (isMobile) {
    // تقليل جودة الصور على الجوال
    const images = document.querySelectorAll('img');
    images.forEach(img => {
      img.loading = 'lazy';
      img.style.imageRendering = 'auto';
    });

    // تحسين الأنيميشن للجوال
    const style = document.createElement('style');
    style.textContent = `
      * {
        animation-duration: 0.2s !important;
        transition-duration: 0.2s !important;
      }
    `;
    document.head.appendChild(style);

    // تحسين المشغل للجوال
    optimizePlayerForMobile();

    // تحسين التمرير للجوال
    optimizeScrollingForMobile();
  }
}

// تحسين المشغل للجوال
function optimizePlayerForMobile() {
  const playerContainer = document.getElementById('playerContainer');

  if (playerContainer) {
    // جعل المشغل يظهر في الجوال عند تشغيل قناة
    const originalCheckAndPlayChannel = window.checkAndPlayChannel;

    window.checkAndPlayChannel = function(url) {
      // إظهار المشغل في الجوال
      playerContainer.classList.remove('hidden');
      playerContainer.classList.remove('md:block');

      // تشغيل القناة
      if (originalCheckAndPlayChannel) {
        originalCheckAndPlayChannel(url);
      }

      // التمرير إلى المشغل في الجوال
      if (window.innerWidth < 768) {
        setTimeout(() => {
          playerContainer.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }, 500);
      }
    };
  }
}

// تحسين التمرير للجوال
function optimizeScrollingForMobile() {
  // تحسين التمرير السلس
  document.documentElement.style.scrollBehavior = 'smooth';

  // منع التمرير الأفقي
  document.body.style.overflowX = 'hidden';

  // تحسين التمرير في القوائم
  const scrollableElements = document.querySelectorAll('.overflow-y-auto');
  scrollableElements.forEach(element => {
    element.style.webkitOverflowScrolling = 'touch';
    element.style.overscrollBehavior = 'contain';
  });
}

// تحسين تخطيط الصفحة للجوال
function optimizeLayoutForMobile() {
  const isMobile = window.innerWidth < 768;
  const mainContainer = document.querySelector('.flex.gap-6');

  if (mainContainer && isMobile) {
    // تغيير التخطيط للجوال
    mainContainer.classList.remove('flex');
    mainContainer.classList.add('block');
    mainContainer.classList.add('space-y-4');
  } else if (mainContainer && !isMobile) {
    // استعادة التخطيط للشاشات الكبيرة
    mainContainer.classList.add('flex');
    mainContainer.classList.remove('block');
    mainContainer.classList.remove('space-y-4');
  }
}

// مراقبة تغيير حجم الشاشة
function initializeResponsiveHandlers() {
  let resizeTimeout;

  window.addEventListener('resize', function() {
    clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(() => {
      optimizeLayoutForMobile();

      // إعادة عرض القنوات مع التخطيط الجديد
      if (typeof displayChannels === 'function') {
        displayChannels();
      }
    }, 250);
  });

  // تشغيل التحسين عند التحميل
  optimizeLayoutForMobile();
}

// تحسين نافذة M3U للجوال
function enhanceMobileM3UDialog() {
  const dialog = document.getElementById('mobileM3UDialog');

  if (dialog) {
    // إضافة إيماءات السحب للإغلاق
    let startY = 0;
    let currentY = 0;
    let isDragging = false;

    const content = document.getElementById('mobileM3UContent');

    content.addEventListener('touchstart', function(e) {
      startY = e.touches[0].clientY;
      isDragging = true;
    });

    content.addEventListener('touchmove', function(e) {
      if (!isDragging) return;

      currentY = e.touches[0].clientY;
      const deltaY = currentY - startY;

      if (deltaY > 0) {
        content.style.transform = `translateY(${deltaY}px)`;
      }
    });

    content.addEventListener('touchend', function() {
      if (!isDragging) return;

      const deltaY = currentY - startY;

      if (deltaY > 100) {
        // إغلاق النافذة إذا تم السحب لأسفل أكثر من 100px
        hideMobileM3UDialog();
      } else {
        // العودة للموضع الأصلي
        content.style.transform = 'translateY(0)';
      }

      isDragging = false;
    });
  }
}

// تحديث دالة التهيئة
function initializeApp() {
  initializeSearch();
  initializeEventListeners();
  initializeMobileM3UFile();
  initializeMobileTouch();
  initializeResponsiveHandlers();
  enhanceMobileM3UDialog();
  optimizeForMobile();
  updateFavoriteCount();
  updateChannelCount();

  // تحسين خاص للجوال
  if (window.innerWidth < 768) {
    // إضافة meta tag للتحكم في العرض
    const viewport = document.querySelector('meta[name="viewport"]');
    if (viewport) {
      viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
    }

    // منع التكبير المزدوج
    document.addEventListener('dblclick', function(e) {
      e.preventDefault();
    });

    // تحسين الأداء
    document.body.style.webkitTapHighlightColor = 'transparent';
    document.body.style.webkitTouchCallout = 'none';
    document.body.style.webkitUserSelect = 'none';

    // تحميل القنوات المحفوظة للجوال
    if (!loadCachedChannelsForMobile()) {
      // إذا لم يتم العثور على قنوات محفوظة، اعرض رسالة
      setTimeout(() => {
        showNotification('success', 'مرحباً! اضغط على "إضافة M3U" لتحميل قائمة القنوات');
      }, 1000);
    }

    // تنظيف الكاش القديم
    clearMobileCacheIfNeeded();
  }
}

// تشغيل التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', initializeApp);
