<!DOCTYPE html>
<html lang="ar">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />

    <meta name="csrf-token" content="{{ csrf_token() }}">

  <title>فلكسي TV - البث المباشر المحسن</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdn.plyr.io/3.7.8/plyr.css" />
  <link rel="stylesheet" href="{{ asset('css/live-tv-enhanced.css') }}" />
  <style>
    /* ===== متغيرات CSS للألوان والمقاسات ===== */
    :root {
      --primary-color: #dc2626;
      --secondary-color: #f59e0b;
      --accent-color: #3b82f6;
      --success-color: #10b981;
      --warning-color: #f59e0b;
      --danger-color: #ef4444;
      --dark-bg: #111827;
      --card-bg: #1f2937;
      --border-radius: 12px;
      --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      --shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    /* ===== تحسينات عامة ===== */
    html, body {
      overflow-x: hidden;
      scroll-behavior: smooth;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    /* ===== مشغل الفيديو المحسن ===== */
    #videoPlayer {
      width: 100%;
      background: linear-gradient(135deg, #000 0%, #1a1a1a 100%);
      aspect-ratio: 16/9;
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--shadow);
    }

    /* ===== أنيميشن محسن للنبض ===== */
    .pulse {
      animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
      transform-origin: center;
    }
    @keyframes pulse {
      0%, 100% { transform: scale(1); opacity: 1; }
      50% { transform: scale(1.05); opacity: 0.8; }
    }

    /* ===== سبينر تحميل محسن ===== */
    .spinner {
      border: 3px solid rgba(255, 255, 255, 0.2);
      border-top: 3px solid var(--primary-color);
      border-radius: 50%;
      width: 48px;
      height: 48px;
      animation: spin 1s linear infinite;
      box-shadow: 0 0 20px rgba(220, 38, 38, 0.3);
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* ===== إخفاء شريط التمرير مع تحسينات ===== */
    .hide-scrollbar::-webkit-scrollbar { display: none; }
    .hide-scrollbar {
      -ms-overflow-style: none;
      scrollbar-width: none;
      overscroll-behavior: contain;
    }

    /* ===== تحسينات التكبير ===== */
    #zoomWrapper {
      overflow: hidden;
      touch-action: none;
      position: relative;
      border-radius: var(--border-radius);
      background: #000;
    }
    #zoomWrapper video {
      transform-origin: center center;
      transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      border-radius: var(--border-radius);
    }

    /* ===== أنيميشن ping سريع ===== */
    @keyframes ping-fast {
      0% { transform: scale(1); opacity: 1; }
      50% { transform: scale(1.2); opacity: 0.6; }
      100% { transform: scale(1); opacity: 1; }
    }
    .animate-ping-fast { animation: ping-fast 0.8s ease-in-out infinite; }

    /* ===== تحسينات الأزرار ===== */
    .btn-modern {
      background: linear-gradient(135deg, var(--primary-color) 0%, #b91c1c 100%);
      border: none;
      border-radius: var(--border-radius);
      padding: 12px 24px;
      color: white;
      font-weight: 600;
      transition: var(--transition);
      box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3);
      position: relative;
      overflow: hidden;
    }

    .btn-modern:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(220, 38, 38, 0.4);
    }

    .btn-modern:active {
      transform: translateY(0);
    }

    /* ===== تحسينات البطاقات ===== */
    .channel-card {
      background: var(--card-bg);
      border-radius: var(--border-radius);
      padding: 16px;
      transition: var(--transition);
      border: 1px solid rgba(255, 255, 255, 0.1);
      position: relative;
      overflow: hidden;
    }

    .channel-card:hover {
      transform: translateY(-4px) scale(1.02);
      box-shadow: var(--shadow);
      border-color: var(--primary-color);
    }

    .channel-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
      transition: left 0.5s;
    }

    .channel-card:hover::before {
      left: 100%;
    }

    /* ===== تحسينات القوائم المنسدلة ===== */
    .dropdown-modern {
      background: var(--card-bg);
      border-radius: var(--border-radius);
      border: 1px solid rgba(255, 255, 255, 0.1);
      box-shadow: var(--shadow);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
    }

    /* ===== تحسينات الإشعارات ===== */
    .notification {
      border-radius: var(--border-radius);
      padding: 16px 20px;
      box-shadow: var(--shadow);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    /* ===== تحسينات متجاوبة للجوال ===== */
    @media (max-width: 768px) {
      .btn-modern {
        padding: 8px 12px;
        font-size: 13px;
        min-height: 44px; /* حد أدنى للمس */
      }

      .channel-card {
        padding: 8px;
        min-height: 120px;
      }

      #zoomWrapper {
        border-radius: 8px;
      }

      /* تحسين شريط التنقل للجوال */
      .mobile-nav {
        flex-direction: column;
        gap: 8px;
      }

      .mobile-nav .btn-modern {
        width: 100%;
        justify-content: center;
      }

      /* تحسين البحث للجوال */
      #searchInput {
        font-size: 16px; /* منع التكبير في iOS */
        padding: 12px 16px;
      }

      /* تحسين المشغل للجوال */
      #playerContainer {
        width: 100% !important;
        margin-bottom: 16px;
      }

      /* تحسين القوائم المنسدلة للجوال */
      .dropdown-modern {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        max-height: 50vh;
        border-radius: 16px 16px 0 0;
        transform: translateY(100%);
        transition: transform 0.3s ease-in-out;
      }

      .dropdown-modern.show {
        transform: translateY(0);
      }

      /* تحسين الإشعارات للجوال */
      #notificationContainer {
        bottom: 20px;
        right: 16px;
        left: 16px;
      }

      .notification {
        width: 100%;
        margin-bottom: 8px;
      }
    }

    @media (max-width: 480px) {
      .btn-modern {
        padding: 6px 10px;
        font-size: 12px;
      }

      .channel-card {
        padding: 6px;
        min-height: 100px;
      }

      /* تحسين الشبكة للشاشات الصغيرة */
      .mobile-grid {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 8px;
      }

      /* تحسين النصوص للشاشات الصغيرة */
      .mobile-text-sm {
        font-size: 11px;
      }

      .mobile-text-xs {
        font-size: 10px;
      }
    }

    /* ===== تأثيرات الحركة المتقدمة ===== */
    .fade-in {
      animation: fadeIn 0.6s ease-out;
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }

    .slide-up {
      animation: slideUp 0.4s ease-out;
    }

    @keyframes slideUp {
      from { transform: translateY(100%); opacity: 0; }
      to { transform: translateY(0); opacity: 1; }
    }
  </style>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/flowplayer/7.2.7/skin/skin.css">
</head>
<body class="bg-gray-900 text-white p-4 min-h-screen text-sm sm:text-base">



<!-- ===== شريط التنقل المحسن ===== -->
<div class="flex flex-wrap gap-3 mb-8 fade-in">
    <!-- زر القائمة الرئيسية -->
    <button id="categoryDropdownBtn" class="btn-modern bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 px-6 py-3 rounded-xl shadow-lg transition-all duration-300 flex items-center gap-2">
        <span class="text-lg">📂</span>
        <span class="font-semibold">القائمة</span>
        <svg class="w-4 h-4 transition-transform duration-300" id="categoryArrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
        </svg>
    </button>

    <!-- زر المفضلة -->
    <button onclick="displayFavorites()" class="btn-modern bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 px-6 py-3 rounded-xl shadow-lg transition-all duration-300 flex items-center gap-2">
        <span class="text-lg animate-ping-fast">⭐</span>
        <span class="font-semibold">المفضلة</span>
        <span id="favCount" class="bg-white text-yellow-600 text-xs px-2 py-1 rounded-full font-bold hidden">0</span>
    </button>

    <!-- زر الإعدادات -->
    <button onclick="checkPassword()" class="btn-modern bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 px-3 md:px-6 py-2 md:py-3 rounded-lg md:rounded-xl shadow-lg transition-all duration-300 flex items-center gap-1 md:gap-2 flex-1 md:flex-none">
        <span class="text-base md:text-lg">🔐</span>
        <span class="font-semibold text-sm md:text-base">إعدادات</span>
    </button>

    <!-- زر ملء الشاشة -->
    <button onclick="toggleFullscreen()" class="btn-modern bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 px-3 md:px-6 py-2 md:py-3 rounded-lg md:rounded-xl shadow-lg transition-all duration-300 flex items-center gap-1 md:gap-2 flex-1 md:flex-none">
        <span class="text-base md:text-lg">🖥️</span>
        <span class="font-semibold text-sm md:text-base">ملء الشاشة</span>
    </button>

    <!-- زر إضافة M3U للجوال -->
    <button onclick="showMobileM3UDialog()" class="btn-modern bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 px-3 py-2 rounded-lg shadow-lg transition-all duration-300 flex items-center gap-1 flex-1 md:hidden">
        <span class="text-base">📡</span>
        <span class="font-semibold text-sm">إضافة M3U</span>
    </button>

    <!-- زر إعادة تحميل للجوال -->
    <button onclick="reloadChannelsForMobile()" class="btn-modern bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 px-3 py-2 rounded-lg shadow-lg transition-all duration-300 flex items-center gap-1 flex-1 md:hidden">
        <span class="text-base">🔄</span>
        <span class="font-semibold text-sm">إعادة تحميل</span>
    </button>

    <!-- مؤشر الحالة -->
    <div class="flex items-center gap-2 bg-gray-800 px-3 md:px-4 py-2 md:py-3 rounded-lg md:rounded-xl border border-gray-700 w-full md:w-auto justify-center md:justify-start">
        <div id="statusIndicator" class="w-2 h-2 md:w-3 md:h-3 bg-green-500 rounded-full animate-pulse"></div>
        <span id="statusText" class="text-xs md:text-sm font-medium text-gray-300">متصل</span>
        <span id="channelCount" class="text-xs text-gray-400 hidden md:inline">• 0 قناة</span>
    </div>
</div>

<!-- ===== نافذة إضافة M3U للجوال ===== -->
<div id="mobileM3UDialog" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
    <div class="absolute bottom-0 left-0 right-0 bg-gray-800 rounded-t-2xl p-6 transform translate-y-full transition-transform duration-300" id="mobileM3UContent">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-bold text-white">إضافة قائمة M3U</h3>
            <button onclick="hideMobileM3UDialog()" class="text-gray-400 hover:text-white">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <div class="space-y-4">
            <!-- إدخال رابط M3U -->
            <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">رابط M3U</label>
                <input type="url" id="mobileM3UUrl" placeholder="https://example.com/playlist.m3u"
                       class="w-full p-3 bg-gray-700 rounded-lg border border-gray-600 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all duration-300 text-white placeholder-gray-400" />
            </div>

            <!-- أو رفع ملف -->
            <div class="text-center">
                <span class="text-gray-400 text-sm">أو</span>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">رفع ملف M3U</label>
                <input type="file" id="mobileM3UFile" accept=".m3u,.txt"
                       class="w-full p-3 bg-gray-700 rounded-lg border border-gray-600 focus:border-blue-500 transition-all duration-300 text-white file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-blue-600 file:text-white hover:file:bg-blue-700" />
            </div>

            <!-- أزرار العمل -->
            <div class="flex gap-3 pt-4">
                <button onclick="loadMobileM3UUrl()" class="flex-1 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white py-3 px-4 rounded-lg font-semibold transition-all duration-300">
                    📥 تحميل من الرابط
                </button>
                <button onclick="hideMobileM3UDialog()" class="flex-1 bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white py-3 px-4 rounded-lg font-semibold transition-all duration-300">
                    إلغاء
                </button>
            </div>
        </div>
    </div>
</div>

  <!-- الشعار
  <div class="flex justify-center items-center mb-6">
    <img src="https://www.arabseed.stream/images/67fcb12ad4171ic_launcher.png" alt="Flicksy Logo" class="w-12 h-12 mr-2" />
    <h1 class="text-2xl font-bold text-center">📺</h1>
  </div> -->




  <!-- نموذج الاستيراد -->
  <div id="uploadForm" class="mb-6 space-y-4 hidden">
    <input type="file" id="m3uFile" accept=".m3u,.txt" class="bg-gray-800 p-2 rounded w-full" />
    <input type="text" id="m3uUrl" placeholder="🌐 أدخل رابط M3U مباشر" class="bg-gray-800 p-2 rounded w-full" />
    <button type="button" id="loadUrl" class="bg-green-600 py-2 px-4 rounded w-full">📥 استيراد من رابط</button>

    <div class="bg-gray-800 p-3 rounded space-y-2">
      <h2 class="font-bold text-sm text-yellow-400">🌐 استيراد من Xtream Codes</h2>
      <input type="text" id="xtreamHost" placeholder="🛰️ رابط السيرفر (http://host:port)" class="w-full p-1 rounded bg-gray-700 text-sm" />
      <input type="text" id="xtreamUser" placeholder="👤 اسم المستخدم" class="w-full p-1 rounded bg-gray-700 text-sm" />
      <input type="password" id="xtreamPass" placeholder="🔑 كلمة المرور" class="w-full p-1 rounded bg-gray-700 text-sm" />
      <button type="button" onclick="loadXtream()" class="bg-blue-700 py-2 px-4 rounded w-full">📡 استيراد من Xtream</button>


  </div>
  </div>

  <script async="async" data-cfasync="false" src="//pl20482735.profitableratecpm.com/8c1ac1e8c1dbde26b523284c8e19c617/invoke.js"></script>
<div id="container-8c1ac1e8c1dbde26b523284c8e19c617"></div>





  <!-- زر تحميل القنوات المحفوظة -->
<div class="relative group mb-4">
  <button type="button" onclick="loadChannelsFromDefault()" class="bg-red-700 py-2 px-4 rounded w-full">
      تحديث 🚀🚀🚀 البث
  </button>
</div>


<!-- زر القائمة -->
<div class="relative mb-4">
  <button onclick="toggleM3UDropdown()" class="bg-purple-700 px-4 py-2 rounded w-full">
    🌐 🚀 سوف يعمل قريبا
  </button>

  <!-- القائمة المنسدلة -->
  <div id="m3uDropdown" class="hidden absolute mt-2 w-full bg-gray-800 rounded shadow-lg z-30">
      <button onclick="loadFromUrl('https://example.com/all.m3u')" class="block w-full text-right px-4 py-2 hover:bg-gray-700">🌐 جميع القنوات</button>

    <button onclick="loadFromUrl('https://example.com/movies.m3u')" class="block w-full text-right px-4 py-2 hover:bg-gray-700">🎬 أفلام</button>

    <button onclick="loadFromUrl('https://example.com/sports.m3u')" class="block w-full text-right px-4 py-2 hover:bg-gray-700">⚽ رياضة</button>

    <button onclick="loadFromUrl('https://example.com/kids.m3u')" class="block w-full text-right px-4 py-2 hover:bg-gray-700">🧒 أطفال</button>

    <button onclick="loadFromUrl('https://example.com/news.m3u')" class="block w-full text-right px-4 py-2 hover:bg-gray-700">📰 أخبار</button>

  </div>
</div>



  <!-- ===== شريط البحث المحسن ===== -->
  <div class="relative mb-6 fade-in">
    <div class="relative">
      <input type="text" id="searchInput"
             placeholder="🔍 ابحث عن قناة أو برنامج..."
             class="w-full p-4 pr-12 bg-gradient-to-r from-gray-800 to-gray-700 rounded-xl border border-gray-600 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all duration-300 text-white placeholder-gray-400" />

      <!-- أيقونة البحث -->
      <div class="absolute right-4 top-1/2 transform -translate-y-1/2">
        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
        </svg>
      </div>

      <!-- زر مسح البحث -->
      <button id="clearSearch" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors duration-200 hidden">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <!-- نتائج البحث السريع -->
    <div id="searchResults" class="absolute top-full left-0 right-0 mt-2 bg-gray-800 rounded-xl shadow-2xl border border-gray-600 hidden z-30 max-h-60 overflow-y-auto">
      <div class="p-3 text-sm text-gray-400 border-b border-gray-700">نتائج البحث</div>
      <div id="searchResultsList" class="p-2"></div>
    </div>
  </div>

  <!-- قائمة التصنيفات المحسنة -->
  <div id="categoriesBar" class="dropdown-modern absolute hidden mt-2 w-64 max-h-80 overflow-y-auto z-20"></div>

  <!-- المشغل -->
 <!-- ✅ مشغل + قنوات بجانب بعض (المشغل في اليمين) -->
<div id="sideBySideContainer" class="flex flex-col md:flex-row-reverse gap-4 mb-6">




<!-- ✅ حركة اليد لتوضيح التكبير -->
<div id="zoomGesture" class="fixed inset-0 z-50 flex flex-col items-center justify-center bg-black bg-opacity-60 hidden">
  <img src="https://upload.wikimedia.org/wikipedia/commons/6/6b/Pinch-zoom-gesture.gif"
       alt="zoom gesture" class="w-32 h-32 rounded-xl shadow-lg" />
  <p class="mt-4 text-white text-center text-sm animate-bounce">📲 استخدم إصبعين للتكبير</p>
</div>


  <!-- ✅ المشغل المتجاوب -->
  <div id="playerContainer" class="relative w-full md:w-3/5 overflow-hidden hide-scrollbar hidden">

    <div id="loadingSpinner" class="hidden absolute inset-0 flex items-center justify-center bg-black bg-opacity-60 z-10">
      <div class="spinner"></div>
    </div>

   <!-- Plyr داخل Zoom Wrapper -->
<div id="plyrContainer" class="hide-scrollbar overflow-hidden flex" style="max-height: 100%; height: 100%;">
  <div id="zoomWrapper" class="touch-none w-full h-full" style="touch-action: none;">
  <video id="videoElement" controls playsinline style="width: 100%; height: 100%; object-fit: contain;"></video>
  <!-- زر التكبير فقط -->
  <button id="zoomButton"
    onclick="cycleZoom()"
    class="hidden absolute bottom-4 left-4 bg-white/80 text-black border border-gray-400 px-4 py-2 rounded-full shadow-md text-xs font-semibold backdrop-blur-sm z-50 transition-all duration-300">
    ✂️ Zoom ×1
  </button>
</div>
</div>


    <!-- Flowplayer -->
    <div id="flowContainer" class="w-full hidden hide-scrollbar overflow-hidden">
      <div id="flowInstance" class="flowplayer fixed-controls bg-black rounded" data-ratio="0.5625" data-embed="true">
        <video>
          <source id="flowSource" type="application/x-mpegurl" src="">
        </video>
      </div>
    </div>

    <!-- Video.js Player -->
    <div id="videojsContainer" class="w-full hidden hide-scrollbar overflow-hidden">
      <video id="videojsPlayer" class="video-js vjs-default-skin w-full h-auto" controls preload="auto" data-setup="{}">
        <p class="vjs-no-js">
          لتشغيل هذا الفيديو، يرجى تفعيل JavaScript، والنظر في الترقية إلى
          <a href="https://videojs.com/html5-video-support/" target="_blank">متصفح يدعم HTML5 video</a>.
        </p>
      </video>
    </div>

    <!-- DPlayer -->
    <div id="dplayerContainer" class="w-full hidden hide-scrollbar overflow-hidden">
      <div id="dplayerElement" class="w-full h-auto bg-black rounded"></div>
    </div>

    <!-- Shaka Player -->
    <div id="shakaContainer" class="w-full hidden hide-scrollbar overflow-hidden">
      <video id="shakaPlayer" class="w-full h-auto bg-black rounded" controls></video>
    </div>

    <!-- مشغل HTML5 محسن -->
    <div id="html5Container" class="w-full hidden hide-scrollbar overflow-hidden">
      <video id="html5Player" class="w-full h-auto bg-black rounded" controls crossorigin="anonymous" playsinline>
        <source id="html5Source" src="" type="">
        متصفحك لا يدعم تشغيل الفيديو.
      </video>
    </div>

    <!-- ===== أزرار التحكم المتجاوبة ===== -->
    <div class="mt-3 md:mt-4 flex flex-wrap gap-2 md:gap-3 justify-center">
      <!-- الصف الأول: أزرار أساسية -->
      <div class="flex gap-2 w-full md:w-auto">
        <!-- زر الإغلاق -->
        <button onclick="closePlayer()" class="btn-modern bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 px-3 md:px-4 py-2 rounded-lg flex items-center gap-1 md:gap-2 pulse flex-1 md:flex-none">
          <span class="text-sm md:text-base">❌</span>
          <span class="text-xs md:text-sm font-medium">إغلاق</span>
        </button>

        <!-- زر القناة السابقة -->
        <button onclick="playLastChannel()" class="btn-modern bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 px-3 md:px-4 py-2 rounded-lg flex items-center gap-1 md:gap-2 flex-1 md:flex-none">
          <span class="text-sm md:text-base">🔁</span>
          <span class="text-xs md:text-sm font-medium">السابقة</span>
        </button>

        <!-- زر تبديل العرض -->
        <button onclick="togglePlayerLayout()" id="toggleLayoutBtn" class="btn-modern bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 px-3 md:px-4 py-2 rounded-lg flex items-center gap-1 md:gap-2 flex-1 md:flex-none">
          <span class="text-sm md:text-base">🧭</span>
          <span class="text-xs md:text-sm font-medium">عرض</span>
        </button>
      </div>

      <!-- الصف الثاني: أزرار إضافية -->
      <div class="flex gap-2 w-full md:w-auto">
        <!-- زر مشاركة -->
        <button onclick="shareChannel()" class="btn-modern bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 px-3 md:px-4 py-2 rounded-lg flex items-center gap-1 md:gap-2 flex-1 md:flex-none">
          <span class="text-sm md:text-base">📤</span>
          <span class="text-xs md:text-sm font-medium">مشاركة</span>
        </button>

        <!-- زر الصوت -->
        <button onclick="toggleMute()" id="muteBtn" class="btn-modern bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 px-3 md:px-4 py-2 rounded-lg flex items-center gap-1 md:gap-2 flex-1 md:flex-none">
          <span id="muteIcon" class="text-sm md:text-base">🔊</span>
          <span class="text-xs md:text-sm font-medium">صوت</span>
        </button>

        <!-- زر ملء الشاشة للجوال -->
        <button onclick="toggleFullscreen()" class="btn-modern bg-gradient-to-r from-indigo-600 to-indigo-700 hover:from-indigo-700 hover:to-indigo-800 px-3 py-2 rounded-lg flex items-center gap-1 flex-1 md:hidden">
          <span class="text-sm">🖥️</span>
          <span class="text-xs font-medium">ملء الشاشة</span>
        </button>

        <button onclick="switchPlayer()" class="btn-modern bg-gradient-to-r from-orange-600 to-orange-700 hover:from-orange-700 hover:to-orange-800 px-3 py-2 rounded-lg flex items-center gap-1 flex-1">
          <span class="text-sm">🔄</span>
          <span class="text-xs font-medium">تبديل المشغل</span>
        </button>
      </div>
    </div>
  </div>

  <!-- ✅ القنوات المتجاوبة -->
  <div id="channelGroups" class="w-full md:w-2/5 space-y-4 md:space-y-6 overflow-y-auto max-h-[70vh] md:max-h-[80vh] pr-1 hide-scrollbar"></div>
</div>

<!-- ✅ القنوات المنفصلة (عندما لا يكون هناك مشغل) -->
<div id="standaloneChannels" class="w-full space-y-4 md:space-y-6 overflow-y-auto max-h-[80vh] pr-1 hide-scrollbar block">

  <!-- رسالة الحالة -->
  <div id="statusMessage" class="bg-blue-600 text-white p-4 rounded-lg text-center mb-4">
    🎯 مرحباً بك في فلكسي TV - اختر طريقة تحميل القنوات
  </div>

  <!-- أزرار تحميل مباشرة -->
  <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
    <button onclick="loadTestChannelsNow()" class="bg-green-600 hover:bg-green-700 px-6 py-4 rounded-lg font-semibold transition-colors duration-200 text-center">
      🧪 قنوات تجريبية<br>
      <span class="text-sm opacity-80">(تعمل فوراً)</span>
    </button>

    <button onclick="loadRealChannelsNow()" class="bg-blue-600 hover:bg-blue-700 px-6 py-4 rounded-lg font-semibold transition-colors duration-200 text-center">
      📺 القنوات الحقيقية<br>
      <span class="text-sm opacity-80">(من الإنترنت)</span>
    </button>

    <button onclick="openTestPage()" class="bg-purple-600 hover:bg-purple-700 px-6 py-4 rounded-lg font-semibold transition-colors duration-200 text-center">
      🔧 صفحة الاختبار<br>
      <span class="text-sm opacity-80">(للتشخيص)</span>
    </button>
  </div>

  <!-- منطقة عرض القنوات -->
  <div id="channelsDisplay" class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
    <!-- القنوات ستظهر هنا -->
  </div>

</div>



  <!-- ===== الإشعارات المحسنة ===== -->
  <div id="notificationContainer" class="fixed bottom-6 right-6 z-50 space-y-3">
    <!-- إشعار المفضلة -->
    <div id="favNotification" class="notification bg-gradient-to-r from-yellow-500 to-yellow-600 text-white px-6 py-4 rounded-xl shadow-2xl hidden transform transition-all duration-300 slide-up">
      <div class="flex items-center gap-3">
        <span class="text-xl">⭐</span>
        <div>
          <div class="font-semibold">تم الإضافة إلى المفضلة</div>
          <div class="text-sm opacity-90">يمكنك الوصول إليها من قائمة المفضلة</div>
        </div>
      </div>
    </div>

    <!-- إشعار التحميل -->
    <div id="loadingNotice" class="notification bg-gradient-to-r from-blue-500 to-blue-600 text-white px-6 py-4 rounded-xl shadow-2xl hidden transform transition-all duration-300 slide-up">
      <div class="flex items-center gap-3">
        <div class="spinner w-5 h-5"></div>
        <div>
          <div class="font-semibold">جارٍ التحميل...</div>
          <div class="text-sm opacity-90">يرجى الانتظار</div>
        </div>
      </div>
    </div>

    <!-- إشعار النجاح -->
    <div id="successNotice" class="notification bg-gradient-to-r from-green-500 to-green-600 text-white px-6 py-4 rounded-xl shadow-2xl hidden transform transition-all duration-300 slide-up">
      <div class="flex items-center gap-3">
        <span class="text-xl">✅</span>
        <div>
          <div class="font-semibold">تم بنجاح!</div>
          <div class="text-sm opacity-90" id="successMessage">العملية تمت بنجاح</div>
        </div>
      </div>
    </div>

    <!-- إشعار الخطأ -->
    <div id="errorNotice" class="notification bg-gradient-to-r from-red-500 to-red-600 text-white px-6 py-4 rounded-xl shadow-2xl hidden transform transition-all duration-300 slide-up">
      <div class="flex items-center gap-3">
        <span class="text-xl">❌</span>
        <div>
          <div class="font-semibold">حدث خطأ!</div>
          <div class="text-sm opacity-90" id="errorMessage">يرجى المحاولة مرة أخرى</div>
        </div>
      </div>
    </div>
  </div>

  <!-- صوت عند التشغيل -->
  <audio id="startSound" src="https://cdn.pixabay.com/download/audio/2022/03/15/audio_f93c5643bf.mp3?filename=tweet.mp3" preload="auto"></audio>

  <!-- سكربتات -->
  <script src="https://cdn.plyr.io/3.7.8/plyr.polyfilled.js"></script>
  <script src="{{ asset('js/live-tv-enhanced.js') }}"></script>
  <script type='text/javascript' src='//pl20482749.profitableratecpm.com/9f/8d/83/9f8d8377cd0b12067194770cb79f68cf.js'></script>



  <!-- مشغلات متعددة لدعم جميع الصيغ -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/flowplayer/7.2.7/flowplayer.min.js"></script>
  <script src="https://releases.flowplayer.org/hlsjs/flowplayer.hlsjs.min.js"></script>

  <!-- Video.js لدعم المزيد من الصيغ -->
  <link href="https://vjs.zencdn.net/8.6.1/video-js.css" rel="stylesheet">
  <script src="https://vjs.zencdn.net/8.6.1/video.min.js"></script>

  <!-- DPlayer لدعم DASH و FLV -->
  <script src="https://cdn.jsdelivr.net/npm/dplayer@1.27.1/dist/DPlayer.min.js"></script>

  <!-- Shaka Player لدعم DASH -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/shaka-player/4.7.5/shaka-player.compiled.min.js"></script>




  <script type='text/javascript' src='//pl20479207.profitableratecpm.com/b9/d4/b9/b9d4b953bed55faf387da628be9148d2.js'></script>
  <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
  <script>
    // ✅ تهيئة المتغيرات العامة فوراً
    let allChannels = [];
    let lastChannelUrl = null;
    let showingFavorites = false;

    console.log('🎯 تم تهيئة المتغيرات العامة');

    // ✅ دالة تشخيص فورية
    function quickDiagnose() {
      console.log('🔍 تشخيص سريع:');
      console.log('- allChannels:', allChannels ? allChannels.length : 'غير محدد');
      console.log('- standaloneChannels:', !!document.getElementById('standaloneChannels'));
      console.log('- channelsDisplay:', !!document.getElementById('channelsDisplay'));
      console.log('- statusMessage:', !!document.getElementById('statusMessage'));

      return {
        channels: allChannels ? allChannels.length : 0,
        elements: {
          standalone: !!document.getElementById('standaloneChannels'),
          display: !!document.getElementById('channelsDisplay'),
          status: !!document.getElementById('statusMessage')
        }
      };
    }

    // جعل الدالة متاحة عالمياً فوراً
    window.quickDiagnose = quickDiagnose;

    // ✅ تحميل فوري للقنوات التجريبية
    setTimeout(() => {
      console.log('🚀 بدء التحميل التلقائي للقنوات التجريبية');

      if (!allChannels || allChannels.length === 0) {
        const testChannels = [
          {
            title: 'Big Buck Bunny',
            logo: 'https://via.placeholder.com/100x100/FF6B6B/FFFFFF?text=TEST1',
            group: 'قنوات تجريبية',
            url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4'
          },
          {
            title: 'Elephant Dream',
            logo: 'https://via.placeholder.com/100x100/4ECDC4/FFFFFF?text=TEST2',
            group: 'قنوات تجريبية',
            url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4'
          },
          {
            title: 'Sample Video',
            logo: 'https://via.placeholder.com/100x100/45B7D1/FFFFFF?text=TEST3',
            group: 'قنوات تجريبية',
            url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4'
          }
        ];

        allChannels = testChannels;
        console.log('✅ تم تحميل', testChannels.length, 'قنوات تجريبية تلقائياً');

        // محاولة عرض القنوات
        setTimeout(() => {
          const container = document.getElementById('channelsDisplay');
          if (container && typeof displayChannelsSimple === 'function') {
            displayChannelsSimple();
          }
        }, 500);
      }
    }, 2000);

    const player = new Plyr('#videoElement');

    // ✅ دوال اختيار المشغل المناسب
    function detectBestPlayer(url) {
      const urlLower = url.toLowerCase();
      const ext = url.split('?')[0].split('.').pop().toLowerCase();

      console.log('🔍 فحص نوع الملف:', ext, 'للرابط:', url.substring(0, 50) + '...');

      // HLS (m3u8)
      if (ext === 'm3u8' || urlLower.includes('.m3u8') || urlLower.includes('hls')) {
        return 'hls';
      }

      // DASH (mpd)
      if (ext === 'mpd' || urlLower.includes('.mpd') || urlLower.includes('dash')) {
        return 'dash';
      }

      // FLV
      if (ext === 'flv' || urlLower.includes('.flv')) {
        return 'flv';
      }

      // صيغ الفيديو العادية
      if (['mp4', 'webm', 'ogg', 'avi', 'mkv', 'mov', 'wmv', 'ts'].includes(ext)) {
        return 'html5';
      }

      // صيغ الصوت
      if (['mp3', 'aac', 'wav', 'flac', 'm4a'].includes(ext)) {
        return 'html5';
      }

      // RTMP/RTSP
      if (urlLower.startsWith('rtmp') || urlLower.startsWith('rtsp')) {
        return 'videojs'; // Video.js يدعم RTMP بشكل أفضل
      }

      // YouTube
      if (urlLower.includes('youtube.com') || urlLower.includes('youtu.be')) {
        return 'videojs';
      }

      // روابط أخرى - نجرب Video.js أولاً
      return 'videojs';
    }

    function hideAllPlayers() {
      const players = [
        'plyrContainer',
        'flowContainer',
        'videojsContainer',
        'dplayerContainer',
        'shakaContainer',
        'html5Container'
      ];

      players.forEach(id => {
        const element = document.getElementById(id);
        if (element) element.classList.add('hidden');
      });
    }

    // 🔧 إجبار fullscreen أن يشمل #zoomWrapper بالكامل
player.on('enterfullscreen', () => {
  const zoomBtn = document.getElementById('zoomButton');
  zoomBtn.classList.remove('hidden');
});

player.on('exitfullscreen', () => {
  const zoomBtn = document.getElementById('zoomButton');
  zoomBtn.classList.add('hidden');
  lastScale = 1;
  currentX = 0;
  currentY = 0;
  applyTransform();
  zoomBtn.innerText = '✂️ Zoom ×1';
  currentZoomIndex = 0;
});
    const video = document.getElementById('videoElement');

    const categoryBtn = document.getElementById('categoryDropdownBtn');
    const categoryMenu = document.getElementById("categoriesBar");
    const spinner = document.getElementById("loadingSpinner");
    let allChannels = [], lastChannelUrl = null, showingFavorites = false;

    function checkPassword() {
      const input = prompt("🔐 أدخل كلمة المرور:");
      if (input === "") {
        document.getElementById('uploadForm').classList.remove('hidden');
        document.getElementById('adminAccess').classList.add('hidden');
      } else {
        alert("❌ كلمة المرور غير صحيحة");
      }
    }

    function parseM3U(content) {
      const lines = content.split('\n');
      const channels = [];
      for (let i = 0; i < lines.length; i++) {
        if (lines[i].startsWith('#EXTINF')) {
          const title = lines[i].split(',')[1]?.trim() || 'بدون اسم';
          const logo = lines[i].match(/tvg-logo="(.*?)"/)?.[1] || 'https://via.placeholder.com/100x100';
          const group = lines[i].match(/group-title="(.*?)"/)?.[1] || 'غير مصنف';
          const url = lines[i + 1]?.trim();
          if (url?.startsWith('http')) channels.push({ title, logo, group, url });
        }
      }
      allChannels = channels;
      updateCategoryBar();
      displayChannels();
    }

    // تم نقل هذه الدالة إلى الأسفل مع التحسينات

    // تم نقل هذه الدالة إلى الأسفل مع التحسينات

    function checkAndPlayChannel(url) {
      spinner.classList.remove("hidden");
      fetch(url, { method: 'HEAD' })
        .then(res => res.ok ? playChannel(url) : alert('❌ الرابط غير صالح'))
        .catch(() => alert('❌ تعذر التحقق من الرابط'))
        .finally(() => spinner.classList.add("hidden"));
    }

  function playChannel(url) {
  console.log('🎬 تشغيل القناة:', url);
  lastChannelUrl = url;

  // إظهار المشغل والقنوات
  document.getElementById('standaloneChannels').classList.add('hidden');
  document.getElementById('sideBySideContainer').classList.remove('hidden');
  document.getElementById('playerContainer').classList.remove('hidden');
  document.getElementById('channelGroups').classList.remove('hidden');

  const spinner = document.getElementById("loadingSpinner");
  if (spinner) spinner.classList.remove("hidden");

  // اختيار المشغل المناسب حسب نوع الملف
  const playerType = detectBestPlayer(url);
  console.log('🎯 نوع المشغل المختار:', playerType);

  // إخفاء جميع المشغلات
  hideAllPlayers();

  // تشغيل بالمشغل المناسب
  switch(playerType) {
    case 'hls':
      playWithHLS(url);
      break;
    case 'dash':
      playWithShaka(url);
      break;
    case 'flv':
      playWithDPlayer(url);
      break;
    case 'videojs':
      playWithVideoJS(url);
      break;
    case 'flowplayer':
      playWithFlowplayer(url);
      break;
    default:
      playWithHTML5(url);
  }

  // تشغيل صوت البداية
  const startSound = document.getElementById('startSound');
  if (startSound) startSound.play().catch(() => {});

  // التمرير إلى المشغل
  document.getElementById('playerContainer').scrollIntoView({ behavior: 'smooth' });

  showNotification('🎬 جاري تحميل القناة...', 'info', 2000);
}



    // ✅ دوال التشغيل للمشغلات المختلفة

    function playWithHLS(url) {
      console.log('🎵 تشغيل HLS:', url);
      document.getElementById('plyrContainer').classList.remove('hidden');

      const video = document.getElementById("videoElement");

      // تنظيف المشغل
      video.pause();
      video.removeAttribute("src");
      video.load();
      if (window.hls) {
        window.hls.destroy();
        window.hls = null;
      }

      if (Hls.isSupported()) {
        const hls = new Hls({
          maxBufferLength: 10,
          maxRetry: 3,
          retryDelay: 2000,
          enableWorker: true
        });

        hls.loadSource(url);
        hls.attachMedia(video);
        window.hls = hls;

        hls.on(Hls.Events.MANIFEST_PARSED, () => {
          video.play().then(() => {
            const spinner = document.getElementById("loadingSpinner");
            if (spinner) spinner.classList.add("hidden");
            showNotification('✅ تم تشغيل HLS بنجاح', 'success', 2000);
          }).catch(err => {
            console.warn('⚠️ فشل التشغيل التلقائي:', err);
            const spinner = document.getElementById("loadingSpinner");
            if (spinner) spinner.classList.add("hidden");
            showNotification('⚠️ اضغط زر التشغيل لبدء المشاهدة', 'warning');
          });
        });

        hls.on(Hls.Events.ERROR, (_, data) => {
          if (data.fatal) {
            console.warn("📛 خطأ HLS، محاولة مشغل بديل...");
            playWithVideoJS(url);
          }
        });
      } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
        // Safari يدعم HLS محلياً
        video.src = url;
        video.load();
        video.play().then(() => {
          const spinner = document.getElementById("loadingSpinner");
          if (spinner) spinner.classList.add("hidden");
          showNotification('✅ تم تشغيل HLS (Safari)', 'success', 2000);
        });
      } else {
        console.warn('⚠️ HLS غير مدعوم، تجربة مشغل بديل');
        playWithVideoJS(url);
      }
    }

    function playWithShaka(url) {
      console.log('🎬 تشغيل DASH مع Shaka:', url);
      document.getElementById('shakaContainer').classList.remove('hidden');

      const video = document.getElementById('shakaPlayer');

      if (typeof shaka !== 'undefined') {
        const player = new shaka.Player(video);

        player.load(url).then(() => {
          video.play().then(() => {
            const spinner = document.getElementById("loadingSpinner");
            if (spinner) spinner.classList.add("hidden");
            showNotification('✅ تم تشغيل DASH بنجاح', 'success', 2000);
          });
        }).catch(error => {
          console.error('❌ خطأ Shaka Player:', error);
          playWithVideoJS(url);
        });
      } else {
        console.warn('⚠️ Shaka Player غير متوفر، تجربة مشغل بديل');
        playWithVideoJS(url);
      }
    }

    function playWithDPlayer(url) {
      console.log('📺 تشغيل FLV مع DPlayer:', url);
      document.getElementById('dplayerContainer').classList.remove('hidden');

      if (typeof DPlayer !== 'undefined') {
        const dp = new DPlayer({
          container: document.getElementById('dplayerElement'),
          video: {
            url: url,
            type: 'flv'
          },
          autoplay: true,
          theme: '#b7daff'
        });

        dp.on('loadeddata', () => {
          const spinner = document.getElementById("loadingSpinner");
          if (spinner) spinner.classList.add("hidden");
          showNotification('✅ تم تشغيل FLV بنجاح', 'success', 2000);
        });

        dp.on('error', () => {
          console.error('❌ خطأ DPlayer');
          playWithVideoJS(url);
        });
      } else {
        console.warn('⚠️ DPlayer غير متوفر، تجربة مشغل بديل');
        playWithVideoJS(url);
      }
    }

    function playWithVideoJS(url) {
      console.log('🎥 تشغيل مع Video.js:', url);
      document.getElementById('videojsContainer').classList.remove('hidden');

      if (typeof videojs !== 'undefined') {
        const player = videojs('videojsPlayer', {
          fluid: true,
          responsive: true,
          playbackRates: [0.5, 1, 1.25, 1.5, 2],
          controls: true,
          preload: 'auto'
        });

        player.src({
          src: url,
          type: detectMimeType(url)
        });

        player.ready(() => {
          player.play().then(() => {
            const spinner = document.getElementById("loadingSpinner");
            if (spinner) spinner.classList.add("hidden");
            showNotification('✅ تم تشغيل الفيديو بنجاح', 'success', 2000);
          }).catch(() => {
            playWithFlowplayer(url);
          });
        });

        player.on('error', () => {
          console.error('❌ خطأ Video.js');
          playWithFlowplayer(url);
        });
      } else {
        console.warn('⚠️ Video.js غير متوفر، تجربة مشغل بديل');
        playWithFlowplayer(url);
      }
    }

    function playWithFlowplayer(url) {
      console.log('🌊 تشغيل مع Flowplayer:', url);
      document.getElementById("flowContainer").classList.remove("hidden");

      document.getElementById("flowSource").src = url;

      if (typeof flowplayer !== 'undefined') {
        flowplayer(); // إعادة تهيئة Flowplayer
        const spinner = document.getElementById("loadingSpinner");
        if (spinner) spinner.classList.add("hidden");
        showNotification('✅ تم تشغيل Flowplayer', 'success', 2000);
      } else {
        console.warn('⚠️ Flowplayer غير متوفر، تجربة HTML5');
        playWithHTML5(url);
      }
    }

    function playWithHTML5(url) {
      console.log('🎞️ تشغيل مع HTML5:', url);
      document.getElementById('html5Container').classList.remove('hidden');

      const video = document.getElementById('html5Player');
      const source = document.getElementById('html5Source');

      source.src = url;
      source.type = detectMimeType(url);

      video.load();
      video.play().then(() => {
        const spinner = document.getElementById("loadingSpinner");
        if (spinner) spinner.classList.add("hidden");
        showNotification('✅ تم تشغيل HTML5', 'success', 2000);
      }).catch(error => {
        console.error('❌ فشل تشغيل HTML5:', error);
        const spinner = document.getElementById("loadingSpinner");
        if (spinner) spinner.classList.add("hidden");
        showNotification('❌ فشل تشغيل الفيديو - جرب رابط آخر', 'error', 4000);
      });
    }

    function detectMimeType(url) {
      const ext = url.split('?')[0].split('.').pop().toLowerCase();

      const mimeTypes = {
        'mp4': 'video/mp4',
        'webm': 'video/webm',
        'ogg': 'video/ogg',
        'avi': 'video/avi',
        'mkv': 'video/x-matroska',
        'mov': 'video/quicktime',
        'wmv': 'video/x-ms-wmv',
        'flv': 'video/x-flv',
        'ts': 'video/mp2t',
        'm3u8': 'application/x-mpegURL',
        'mpd': 'application/dash+xml',
        'mp3': 'audio/mpeg',
        'aac': 'audio/aac',
        'wav': 'audio/wav',
        'flac': 'audio/flac',
        'm4a': 'audio/mp4'
      };

      return mimeTypes[ext] || 'video/mp4';
    }

    // ✅ دالة تبديل المشغل يدوياً
    function switchPlayer() {
      if (!lastChannelUrl) {
        showNotification('❌ لا توجد قناة نشطة', 'warning', 2000);
        return;
      }

      const players = ['hls', 'videojs', 'html5', 'flowplayer', 'dash', 'flv'];
      const currentPlayer = getCurrentPlayer();
      const currentIndex = players.indexOf(currentPlayer);
      const nextIndex = (currentIndex + 1) % players.length;
      const nextPlayer = players[nextIndex];

      console.log('🔄 تبديل من', currentPlayer, 'إلى', nextPlayer);
      showNotification(`🔄 تبديل إلى مشغل ${getPlayerName(nextPlayer)}`, 'info', 2000);

      // إيقاف المشغل الحالي
      stopCurrentPlayer();

      // تشغيل بالمشغل الجديد
      hideAllPlayers();

      switch(nextPlayer) {
        case 'hls':
          playWithHLS(lastChannelUrl);
          break;
        case 'dash':
          playWithShaka(lastChannelUrl);
          break;
        case 'flv':
          playWithDPlayer(lastChannelUrl);
          break;
        case 'videojs':
          playWithVideoJS(lastChannelUrl);
          break;
        case 'flowplayer':
          playWithFlowplayer(lastChannelUrl);
          break;
        default:
          playWithHTML5(lastChannelUrl);
      }
    }

    function getCurrentPlayer() {
      const players = [
        { id: 'plyrContainer', type: 'hls' },
        { id: 'videojsContainer', type: 'videojs' },
        { id: 'html5Container', type: 'html5' },
        { id: 'flowContainer', type: 'flowplayer' },
        { id: 'shakaContainer', type: 'dash' },
        { id: 'dplayerContainer', type: 'flv' }
      ];

      for (const player of players) {
        const element = document.getElementById(player.id);
        if (element && !element.classList.contains('hidden')) {
          return player.type;
        }
      }

      return 'hls'; // افتراضي
    }

    function getPlayerName(type) {
      const names = {
        'hls': 'HLS Player',
        'videojs': 'Video.js',
        'html5': 'HTML5',
        'flowplayer': 'Flowplayer',
        'dash': 'Shaka Player',
        'flv': 'DPlayer'
      };

      return names[type] || type;
    }

    function stopCurrentPlayer() {
      // إيقاف HLS
      if (window.hls) {
        window.hls.destroy();
        window.hls = null;
      }

      // إيقاف Video.js
      if (typeof videojs !== 'undefined') {
        const vjsPlayer = videojs.getPlayer('videojsPlayer');
        if (vjsPlayer) {
          vjsPlayer.pause();
          vjsPlayer.dispose();
        }
      }

      // إيقاف HTML5
      const html5Player = document.getElementById('html5Player');
      if (html5Player) {
        html5Player.pause();
        html5Player.removeAttribute('src');
        html5Player.load();
      }

      // إيقاف Plyr
      const plyrPlayer = document.getElementById('videoElement');
      if (plyrPlayer) {
        plyrPlayer.pause();
        plyrPlayer.removeAttribute('src');
        plyrPlayer.load();
      }
    }

    // ✅ دالة اختبار جميع المشغلات
    function testAllPlayers() {
      const testUrl = 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4';

      console.log('🧪 بدء اختبار جميع المشغلات');

      const results = [];

      // اختبار توفر المكتبات
      results.push(`HLS.js: ${typeof Hls !== 'undefined' ? '✅' : '❌'}`);
      results.push(`Video.js: ${typeof videojs !== 'undefined' ? '✅' : '❌'}`);
      results.push(`DPlayer: ${typeof DPlayer !== 'undefined' ? '✅' : '❌'}`);
      results.push(`Shaka Player: ${typeof shaka !== 'undefined' ? '✅' : '❌'}`);
      results.push(`Flowplayer: ${typeof flowplayer !== 'undefined' ? '✅' : '❌'}`);

      // اختبار العناصر
      const elements = [
        'plyrContainer',
        'videojsContainer',
        'dplayerContainer',
        'shakaContainer',
        'flowContainer',
        'html5Container'
      ];

      elements.forEach(id => {
        const element = document.getElementById(id);
        results.push(`${id}: ${element ? '✅' : '❌'}`);
      });

      alert('🧪 نتائج اختبار المشغلات:\n\n' + results.join('\n') + '\n\n🎬 سيتم تشغيل فيديو تجريبي...');

      // تشغيل فيديو تجريبي
      playChannel(testUrl);
    }

    // ✅ دالة تشخيص سريعة للقنوات
    function diagnoseChannels() {
      console.log('🔍 === تشخيص شامل للقنوات ===');
      console.log('عدد القنوات:', allChannels ? allChannels.length : 'غير محدد');
      console.log('نوع allChannels:', typeof allChannels);
      console.log('محتوى allChannels:', allChannels);

      console.log('العناصر الموجودة:');
      console.log('- standaloneChannels:', !!document.getElementById('standaloneChannels'));
      console.log('- channelGroups:', !!document.getElementById('channelGroups'));
      console.log('- playerContainer:', !!document.getElementById('playerContainer'));

      console.log('البيانات المحفوظة:');
      console.log('- savedM3UContent:', !!localStorage.getItem('savedM3UContent'));
      console.log('- savedM3UUrl:', localStorage.getItem('savedM3UUrl'));

      console.log('الدوال المتاحة:');
      console.log('- parseM3U:', typeof parseM3U);
      console.log('- displayChannels:', typeof displayChannels);
      console.log('- loadDefaultChannels:', typeof loadDefaultChannels);

      // اختبار سريع
      if (!allChannels || allChannels.length === 0) {
        console.warn('⚠️ لا توجد قنوات! جرب:');
        console.log('createTestChannels(); displayChannels();');
      }

      return {
        channelsCount: allChannels ? allChannels.length : 0,
        hasElements: !!document.getElementById('standaloneChannels'),
        hasSavedData: !!localStorage.getItem('savedM3UContent')
      };
    }

    // ✅ دوال مبسطة ومضمونة العمل

    function updateStatus(message, color = 'blue') {
      const statusEl = document.getElementById('statusMessage');
      if (statusEl) {
        statusEl.textContent = message;
        statusEl.className = `bg-${color}-600 text-white p-4 rounded-lg text-center mb-4`;
      }
      console.log('📢', message);
    }

    function loadTestChannelsNow() {
      updateStatus('🧪 تحميل القنوات التجريبية...', 'green');

      const testChannels = [
        {
          title: 'Big Buck Bunny',
          logo: 'https://via.placeholder.com/100x100/FF6B6B/FFFFFF?text=TEST1',
          group: 'قنوات تجريبية',
          url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4'
        },
        {
          title: 'Elephant Dream',
          logo: 'https://via.placeholder.com/100x100/4ECDC4/FFFFFF?text=TEST2',
          group: 'قنوات تجريبية',
          url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4'
        },
        {
          title: 'Sample Video',
          logo: 'https://via.placeholder.com/100x100/45B7D1/FFFFFF?text=TEST3',
          group: 'قنوات تجريبية',
          url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4'
        }
      ];

      allChannels = testChannels;
      displayChannelsSimple();
      updateStatus('✅ تم تحميل ' + testChannels.length + ' قنوات تجريبية', 'green');
    }

    function loadRealChannelsNow() {
      updateStatus('📺 تحميل القنوات الحقيقية...', 'blue');

      const url = "https://www.arabseed.stream/storage/m3u/playlist_68203bbd9e6114.72123431.m3u";

      fetch(url)
        .then(response => {
          if (!response.ok) throw new Error('فشل في تحميل الملف');
          return response.text();
        })
        .then(content => {
          console.log('📄 تم تحميل الملف، الحجم:', content.length);
          parseM3USimple(content);
          updateStatus('✅ تم تحميل ' + allChannels.length + ' قناة حقيقية', 'green');
        })
        .catch(error => {
          console.error('❌ خطأ:', error);
          updateStatus('❌ فشل تحميل القنوات: ' + error.message, 'red');
          loadTestChannelsNow(); // تحميل تجريبية كبديل
        });
    }

    function parseM3USimple(content) {
      const lines = content.split('\n');
      const parsedChannels = [];

      for (let i = 0; i < lines.length; i++) {
        if (lines[i].startsWith('#EXTINF')) {
          const title = lines[i].split(',')[1]?.trim() || 'بدون اسم';
          const logo = lines[i].match(/tvg-logo="(.*?)"/)?.[1] || 'https://via.placeholder.com/100x100/6B7280/FFFFFF?text=📺';
          const group = lines[i].match(/group-title="(.*?)"/)?.[1] || 'غير مصنف';
          const url = lines[i + 1]?.trim();

          if (url && url.startsWith('http')) {
            parsedChannels.push({ title, logo, group, url });
          }
        }
      }

      allChannels = parsedChannels;
      displayChannelsSimple();
      console.log('📺 تم تحليل', parsedChannels.length, 'قناة');
    }

    function displayChannelsSimple() {
      const container = document.getElementById('channelsDisplay');
      if (!container) {
        console.error('❌ لم يتم العثور على حاوية القنوات');
        return;
      }

      container.innerHTML = '';

      if (!allChannels || allChannels.length === 0) {
        container.innerHTML = '<div class="col-span-full text-center text-gray-400 py-8">لا توجد قنوات</div>';
        return;
      }

      allChannels.forEach((channel, index) => {
        const card = document.createElement('div');
        card.className = 'bg-gray-800 p-4 rounded-lg text-center cursor-pointer hover:bg-gray-700 transition-colors duration-200';
        card.innerHTML = `
          <img src="${channel.logo}" class="w-16 h-16 object-cover rounded mx-auto mb-2"
               onerror="this.src='https://via.placeholder.com/100x100/6B7280/FFFFFF?text=📺'" />
          <div class="text-sm font-medium truncate">${channel.title}</div>
          <div class="text-xs text-gray-400 mt-1">قناة ${index + 1}</div>
        `;

        card.addEventListener('click', () => {
          console.log('🎬 تشغيل القناة:', channel.title);
          playChannelSimple(channel);
        });

        container.appendChild(card);
      });

      console.log('✅ تم عرض', allChannels.length, 'قناة');
    }

    function playChannelSimple(channel) {
      updateStatus('🎬 تشغيل: ' + channel.title, 'purple');

      // إخفاء القنوات المنفصلة وإظهار المشغل
      document.getElementById('standaloneChannels').classList.add('hidden');
      document.getElementById('sideBySideContainer').classList.remove('hidden');
      document.getElementById('playerContainer').classList.remove('hidden');
      document.getElementById('channelGroups').classList.remove('hidden');

      // نسخ القنوات للعرض الجانبي
      displayChannels();

      // تشغيل الفيديو
      playChannel(channel.url);
    }

    function openTestPage() {
      window.open('test_simple.html', '_blank');
    }

    // جعل الدوال متاحة عالمياً
    window.diagnoseChannels = diagnoseChannels;
    window.createTestChannels = createTestChannels;
    window.loadDefaultChannels = loadDefaultChannels;
    window.forceReloadChannels = forceReloadChannels;
    window.loadTestChannelsNow = loadTestChannelsNow;
    window.loadRealChannelsNow = loadRealChannelsNow;
    window.openTestPage = openTestPage;

    // ✅ دالة إصلاح طارئة
    window.emergencyFix = function() {
      console.log('🚨 بدء الإصلاح الطارئ...');

      // تهيئة المتغيرات
      if (typeof allChannels === 'undefined') {
        window.allChannels = [];
      }

      // إنشاء قنوات تجريبية
      const testChannels = [
        {
          title: 'Big Buck Bunny',
          logo: 'https://via.placeholder.com/100x100/FF6B6B/FFFFFF?text=TEST1',
          group: 'قنوات تجريبية',
          url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4'
        },
        {
          title: 'Elephant Dream',
          logo: 'https://via.placeholder.com/100x100/4ECDC4/FFFFFF?text=TEST2',
          group: 'قنوات تجريبية',
          url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4'
        }
      ];

      allChannels = testChannels;

      // عرض القنوات مباشرة
      const container = document.getElementById('channelsDisplay');
      if (container) {
        container.innerHTML = '';

        testChannels.forEach((channel, index) => {
          const card = document.createElement('div');
          card.className = 'bg-gray-800 p-4 rounded-lg text-center cursor-pointer hover:bg-gray-700 transition-colors duration-200';
          card.innerHTML = `
            <img src="${channel.logo}" class="w-16 h-16 object-cover rounded mx-auto mb-2" />
            <div class="text-sm font-medium">${channel.title}</div>
            <div class="text-xs text-gray-400 mt-1">قناة ${index + 1}</div>
          `;

          card.addEventListener('click', () => {
            alert('🎬 تشغيل: ' + channel.title);
            console.log('🎬 تم النقر على:', channel.title);
          });

          container.appendChild(card);
        });

        console.log('✅ تم عرض', testChannels.length, 'قنوات بالإصلاح الطارئ');
      }

      // تحديث رسالة الحالة
      const status = document.getElementById('statusMessage');
      if (status) {
        status.textContent = '✅ تم الإصلاح الطارئ - ' + testChannels.length + ' قنوات متاحة';
        status.className = 'bg-green-600 text-white p-4 rounded-lg text-center mb-4';
      }

      return '✅ تم الإصلاح الطارئ بنجاح!';
    };




    function closePlayer() {
  if (window.hls) window.hls.destroy();
  video.pause();

  // ✅ في الجوال فقط، نخفي المشغل
  if (window.innerWidth < 768) {
    document.getElementById('playerContainer').classList.add('hidden');
  }

  // ✅ نعيد عرض القنوات
  document.getElementById('channelGroups').classList.remove('hidden');
}


    function playLastChannel() {
      if (lastChannelUrl) checkAndPlayChannel(lastChannelUrl);
    }

    function getFavorites() {
      return JSON.parse(localStorage.getItem('favorites') || '[]');
    }

    // تم نقل هذه الدالة إلى الأسفل مع التحسينات

    function showFavoriteNotification(msg = '✅ تم الإضافة إلى المفضلة') {
      const notif = document.getElementById('favNotification');
      notif.textContent = msg;
      notif.classList.remove('hidden');
      setTimeout(() => notif.classList.add('hidden'), 2000);
    }

    function displayFavorites() {
      const btn = event.target;
      if (showingFavorites) {
        const savedContent = localStorage.getItem('savedM3UContent');
        const savedUrl = localStorage.getItem('savedM3UUrl');
        if (savedContent) parseM3U(savedContent);
        else if (savedUrl) fetch(savedUrl).then(r => r.text()).then(parseM3U);
        btn.textContent = '⭐ المفضلة';
        showingFavorites = false;
      } else {
        const favs = getFavorites();
        const filtered = allChannels.filter(c => favs.includes(c.url));
        allChannels = filtered;
        displayChannels('all');
        btn.textContent = '↩️ رجوع للتصنيفات';
        showingFavorites = true;
      }
    }

    function loadSavedChannels() {
      const notice = document.getElementById('loadingNotice');
      notice.classList.remove('hidden');
      const savedContent = localStorage.getItem('savedM3UContent');
      const savedUrl = localStorage.getItem('savedM3UUrl');
      const onComplete = () => setTimeout(() => notice.classList.add('hidden'), 1000);
      if (savedContent) {
        parseM3U(savedContent);
        onComplete();
      if (savedContent) {
  parseM3U(savedContent);
} else if (savedUrl) {
  fetch(savedUrl).then(res => res.text()).then(parseM3U);
} else {
  const defaultUrl = "https://www.arabseed.stream/storage/m3u/playlist_68203bbd9e6114.72123431.m3u";
  fetch(defaultUrl)
    .then(res => res.text())
    .then(content => {
      localStorage.setItem('savedM3UUrl', defaultUrl);
      parseM3U(content);
    });
}

      } else {
        alert("❌ لا توجد قنوات محفوظة");
        onComplete();
      }
    }

    document.getElementById('m3uFile').addEventListener('change', e => {
      const file = e.target.files[0];
      if (!file) return;
      const reader = new FileReader();
      reader.onload = e => {
        const content = e.target.result;
        localStorage.setItem('savedM3UContent', content);
        localStorage.removeItem('savedM3UUrl');
        parseM3U(content);
        const notif = document.getElementById('loadingNotice');
        notif.textContent = "✅ تم حفظ القنوات بنجاح";
        notif.classList.remove("hidden");
        setTimeout(() => {
          notif.classList.add("hidden");
          notif.textContent = "⏳ جارٍ تحميل القنوات...";
        }, 2000);
      };
      reader.readAsText(file);
    });

    document.getElementById('loadUrl').addEventListener('click', () => {
      const url = document.getElementById('m3uUrl').value.trim();
      if (!url) return alert('❌ أدخل رابط صالح');
      fetch(url)
        .then(res => res.text())
        .then(content => {
          localStorage.setItem('savedM3UUrl', url);
          localStorage.removeItem('savedM3UContent');
          parseM3U(content);
        })
        .catch(() => alert('❌ تعذر تحميل الرابط'));
    });

    // تم نقل مستمع البحث إلى الأسفل مع التحسينات




// ✅ المفضله web
function displayFilteredChannels(filteredList) {
  const container = document.getElementById('channelGroups');
  container.innerHTML = '';
  const grouped = {};

  filteredList.forEach(ch => {
    if (!grouped[ch.group]) grouped[ch.group] = [];
    grouped[ch.group].push(ch);
  });

  Object.keys(grouped).forEach(group => {
    const section = document.createElement('div');
    section.innerHTML = `<h2 class="text-xl font-bold mb-2">${group}</h2>`;
    const grid = document.createElement('div');
    grid.className = 'grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4';

    grouped[group].forEach(channel => {
      const card = document.createElement('div');
      card.className = 'bg-gray-800 p-2 rounded hover:scale-105 transition';
      const isFavorite = getFavorites().includes(channel.url);
      card.innerHTML = `
        <img src="${channel.logo}" class="w-full object-contain rounded mx-auto" style="max-height: 60px;" />
        <div class="text-sm mt-2 text-center truncate">${channel.title}</div>
        <button onclick="toggleFavorite(event, '${channel.url}')" class="mt-1 text-yellow-400 text-xs hover:underline">
          ${isFavorite ? '⭐ إزالة من المفضلة' : '☆ أضف إلى المفضلة'}
        </button>`;
      card.addEventListener('click', e => {
        if (e.target.tagName !== 'BUTTON') checkAndPlayChannel(channel.url);
      });
      grid.appendChild(card);
    });

    section.appendChild(grid);
    container.appendChild(section);
  });
}


  // ✅   <!-- زر قائمة م -->

function toggleM3UDropdown() {
  document.getElementById('m3uDropdown').classList.toggle('hidden');
}



    // ✅ تحميل تلقائي عند فتح الصفحة مع حقن رابط افتراضي
    // ✅ تحميل مبسط ومضمون عند تحميل الصفحة
    window.addEventListener('DOMContentLoaded', function() {
      console.log('🎯 DOM محمل - بدء التهيئة');

      // التأكد من وجود العناصر
      setTimeout(() => {
        const statusEl = document.getElementById('statusMessage');
        const channelsEl = document.getElementById('channelsDisplay');

        console.log('العناصر:', {
          status: !!statusEl,
          channels: !!channelsEl,
          standalone: !!document.getElementById('standaloneChannels')
        });

        if (statusEl) {
          statusEl.textContent = '🎯 مرحباً بك في فلكسي TV - اختر طريقة تحميل القنوات';
          statusEl.className = 'bg-blue-600 text-white p-4 rounded-lg text-center mb-4';
        }

        // تحميل تلقائي للقنوات التجريبية
        console.log('🧪 تحميل القنوات التجريبية تلقائياً...');
        loadTestChannelsNow();

      }, 1000);
    });

    // نسخة احتياطية مع window.onload
    window.onload = function() {
      console.log('🔄 window.onload - نسخة احتياطية');

      setTimeout(() => {
        if (!allChannels || allChannels.length === 0) {
          console.log('🧪 تحميل احتياطي للقنوات التجريبية');
          loadTestChannelsNow();
        }
      }, 2000);
    };

    // ✅ دالة تحميل القنوات الافتراضية
    function loadDefaultChannels() {
      console.log('📺 تحميل القنوات الافتراضية');
      showNotification('جاري تحميل القنوات...', 'info', 2000);

      const defaultUrl = "https://www.arabseed.stream/storage/m3u/playlist_68203bbd9e6114.72123431.m3u";

      fetch(defaultUrl)
        .then(res => {
          if (!res.ok) throw new Error('فشل في تحميل القنوات');
          return res.text();
        })
        .then(content => {
          console.log('✅ تم تحميل القنوات الافتراضية بنجاح');
          localStorage.setItem('savedM3UUrl', defaultUrl);
          parseM3U(content);
          showNotification('تم تحميل القنوات بنجاح', 'success', 2000);
        })
        .catch(error => {
          console.error('❌ خطأ في تحميل القنوات الافتراضية:', error);
          showNotification('فشل تحميل القنوات - تحقق من الاتصال', 'error', 4000);
          createTestChannels();
        });
    }

    // ✅ إنشاء قنوات تجريبية في حالة فشل التحميل
    function createTestChannels() {
      console.log('🧪 إنشاء قنوات تجريبية');
      const testChannels = [
        {
          title: 'قناة تجريبية 1',
          logo: 'https://via.placeholder.com/100x100/FF6B6B/FFFFFF?text=TEST1',
          group: 'قنوات تجريبية',
          url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4'
        },
        {
          title: 'قناة تجريبية 2',
          logo: 'https://via.placeholder.com/100x100/4ECDC4/FFFFFF?text=TEST2',
          group: 'قنوات تجريبية',
          url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4'
        },
        {
          title: 'قناة تجريبية 3',
          logo: 'https://via.placeholder.com/100x100/45B7D1/FFFFFF?text=TEST3',
          group: 'قنوات تجريبية',
          url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4'
        }
      ];

      allChannels = testChannels;
      showNotification('تم تحميل قنوات تجريبية', 'warning', 3000);
    }

    // ✅ دالة إعادة التحميل القسرية
    function forceReloadChannels() {
      console.log('⚡ إعادة تحميل قسرية للقنوات');

      // مسح جميع البيانات المحفوظة
      localStorage.removeItem('savedM3UContent');
      localStorage.removeItem('savedM3UUrl');
      localStorage.removeItem('favorites');

      // إعادة تعيين المتغيرات
      allChannels = [];
      lastChannelUrl = null;

      // إظهار رسالة تحميل
      const standaloneContainer = document.getElementById('standaloneChannels');
      if (standaloneContainer) {
        standaloneContainer.innerHTML = `
          <div class="text-center py-8">
            <div class="text-4xl mb-4 animate-pulse">🔄</div>
            <p class="text-gray-400">إعادة تحميل قسرية...</p>
            <div class="mt-4">
              <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-red-500"></div>
            </div>
          </div>
        `;
      }

      // إعادة تحميل الصفحة بعد ثانيتين
      setTimeout(() => {
        window.location.reload(true);
      }, 2000);
    }




    // تم نقل مستمع النقر إلى الأسفل مع التحسينات







// ✅ زر تحديث القنوات
function loadChannelsFromDefault() {
  const notice = document.getElementById('loadingNotice');
  notice.classList.remove('hidden');

  const defaultUrl = "https://www.arabseed.stream/storage/m3u/playlist_68203bbd9e6114.72123431.m3u";

  fetch(defaultUrl)
    .then(res => res.text())
    .then(content => {
      localStorage.setItem('savedM3UUrl', defaultUrl);  // اختياري إذا أردت الحفظ
      localStorage.removeItem('savedM3UContent');       // إلغاء أي ملف محفوظ
      parseM3U(content);
    })
    .catch(() => alert("❌ تعذر تحميل القنوات من الرابط"))
    .finally(() => setTimeout(() => notice.classList.add('hidden'), 1000));
}





// ✅ خاص app
function uploadApkToServer() {
  const input = document.getElementById('apkUploader');
  const file = input.files[0];
  const status = document.getElementById('apkUploadStatus');
  const progressBar = document.getElementById('apkProgressBar');
  const progressContainer = document.getElementById('apkProgressContainer');

  if (!file) {
    status.innerHTML = `<span class="text-red-400">❌ الرجاء اختيار ملف APK</span>`;
    return;
  }

  // ✅ حساب الحجم بالميغابايت
  const fileSizeMB = (file.size / (1024 * 1024)).toFixed(2);
  status.innerHTML = `⏳ جاري رفع ملف (${fileSizeMB} MB)...`;

  const formData = new FormData();
  formData.append('apk_file', file);

  const xhr = new XMLHttpRequest();
  xhr.open('POST', "/upload-apk", true);
  xhr.setRequestHeader('X-CSRF-TOKEN', document.querySelector('meta[name="csrf-token"]').content);

  // عرض شريط التقدم
  progressContainer.classList.remove('hidden');
  progressBar.style.width = '0%';

  xhr.upload.addEventListener('progress', (e) => {
    if (e.lengthComputable) {
      const percent = Math.round((e.loaded / e.total) * 100);
      progressBar.style.width = percent + '%';
    }
  });

  xhr.onload = () => {
    if (xhr.status === 200) {
      const data = JSON.parse(xhr.responseText);
      if (data.success) {
        progressBar.style.width = '100%';
        status.innerHTML = `<span class="text-green-400">✅ تم الرفع بنجاح</span><br>
          <a href="${data.url}" target="_blank" class="inline-block mt-2 bg-red-600 py-2 px-4 rounded">📥 تحميل التطبيق</a>`;
      } else {
        status.innerHTML = `<span class="text-red-400">❌ ${data.error || 'حدث خطأ أثناء الرفع'}</span>`;
      }
    } else {
      status.innerHTML = `<span class="text-red-400">❌ فشل الاتصال بالسيرفر</span>`;
    }
  };

  xhr.onerror = () => {
    status.innerHTML = `<span class="text-red-400">❌ حدث خطأ في الاتصال</span>`;
  };

  xhr.send(formData);
}





  function uploadM3UToServer() {
  const fileInput = document.getElementById('m3uFile');
  const file = fileInput.files[0];

  if (!file) {
    alert("❌ الرجاء اختيار ملف أولاً");
    return;
  }

  const formData = new FormData();
  formData.append('m3u_file', file);

  fetch('/Dashboard/Settings/upload-m3u', {
    method: 'POST',
    headers: {
      'X-CSRF-TOKEN': '{{ csrf_token() }}'
    },
    body: formData
  })
    .then(res => res.ok ? res.text() : Promise.reject(res))
    .then(() => {
      alert('✅ تم رفع الملف إلى السيرفر بنجاح!');
    })
    .catch(() => alert('❌ فشل رفع الملف'));
}




 // ✅  خاص  ازرار القائمة م
function loadFromUrl(url) {
  const notice = document.getElementById('loadingNotice');
  notice.classList.remove('hidden');

  fetch(url)
    .then(res => res.text())
    .then(content => {
      localStorage.setItem('savedM3UUrl', url);
      localStorage.removeItem('savedM3UContent');
      parseM3U(content);
    })
    .catch(() => alert("❌ تعذر تحميل القنوات من الرابط"))
    .finally(() => setTimeout(() => notice.classList.add('hidden'), 1000));
}











let isSideBySide = true;

function togglePlayerLayout() {
  const channelGroups = document.getElementById("channelGroups");
  const btn = document.getElementById("toggleLayoutBtn");

  isSideBySide = !isSideBySide;

  if (isSideBySide) {
    channelGroups.classList.remove('hidden');
    btn.textContent = "🧭 تبديل العرض";
  } else {
    channelGroups.classList.add('hidden');
    btn.textContent = "🖥️ عرض منفصل";
  }
}













function applyTransform() {
  videoEl.style.transform = `scale(${lastScale}) translate(${currentX}px, ${currentY}px)`;
}



function distance(touches) {
  const dx = touches[0].clientX - touches[1].clientX;
  const dy = touches[0].clientY - touches[1].clientY;
  return Math.sqrt(dx * dx + dy * dy);
}




//<script>
// 🌟 متغيرات التكبير
let currentZoomIndex = 0;
const zoomLevels = [1, 1.3, 1.6, 2];
let initialDistance = null;
let lastScale = 1;
let currentX = 0;
let currentY = 0;
let lastX = 0;
let lastY = 0;

const wrapper = document.getElementById('zoomWrapper');
const videoEl = document.getElementById('videoElement');
const zoomBtn = document.getElementById('zoomButton');

// ✅ دالة تطبيق التحويل
function applyTransform() {
  videoEl.style.transform = `scale(${lastScale}) translate(${currentX}px, ${currentY}px)`;
}

// ✅ دالة حساب المسافة بين إصبعين
function distance(touches) {
  const dx = touches[0].clientX - touches[1].clientX;
  const dy = touches[0].clientY - touches[1].clientY;
  return Math.sqrt(dx * dx + dy * dy);
}

// ✅ التكبير اليدوي عبر الزر
function cycleZoom() {
  currentZoomIndex = (currentZoomIndex + 1) % zoomLevels.length;
  lastScale = zoomLevels[currentZoomIndex];
  currentX = 0;
  currentY = 0;
  applyTransform();
  zoomBtn.innerText = `✂️ Zoom ×${lastScale}`;
}

// ✅ إظهار زر التكبير فقط في وضع ملء الشاشة
document.addEventListener('fullscreenchange', () => {
  if (document.fullscreenElement) {
    zoomBtn.classList.remove('hidden');
  } else {
    zoomBtn.classList.add('hidden');
    lastScale = 1;
    currentX = 0;
    currentY = 0;
    applyTransform();
    zoomBtn.innerText = '✂️ Zoom ×1';
    currentZoomIndex = 0;
  }
});

// ✅ أحداث اللمس للتكبير
wrapper.addEventListener('touchstart', e => {
  if (e.touches.length === 2) {
    initialDistance = distance(e.touches);
  } else if (e.touches.length === 1 && lastScale > 1) {
    lastX = e.touches[0].clientX - currentX;
    lastY = e.touches[0].clientY - currentY;
  }
}, { passive: false });

wrapper.addEventListener('touchmove', e => {
  if (e.touches.length === 2 && initialDistance) {
    e.preventDefault();
    const newDistance = distance(e.touches);
    let scale = newDistance / initialDistance;

    const videoWidth = videoEl.offsetWidth;
    if (videoWidth === 0) return; // ✅ إصلاح التجمّد

    const screenWidth = window.innerWidth;
    const maxScale = (screenWidth / videoWidth) * 1.2;
    lastScale = Math.min(Math.max(scale, 1), maxScale);

    applyTransform();
  } else if (e.touches.length === 1 && lastScale > 1) {
    e.preventDefault();
    currentX = 0;
    currentY = 0;
    applyTransform();
  }
}, { passive: false });

// ✅ حركة اليد عند بدء التشغيل
function showZoomGesture() {
  if (!document.fullscreenElement) return;
  const gesture = document.getElementById('zoomGesture');
  if (!gesture) return;
  gesture.classList.remove('hidden');
  setTimeout(() => gesture.classList.add('hidden'), 3000);
}




//upload-xtream
function loadXtream() {
  const host = document.getElementById('xtreamHost').value.trim();
  const user = document.getElementById('xtreamUser').value.trim();
  const pass = document.getElementById('xtreamPass').value.trim();

  if (!host || !user || !pass) {
    alert("❌ الرجاء تعبئة جميع الحقول");
    return;
  }

  const port = host.split(':')[2] || '8080'; // محاولة استخراج البورت
  const cleanHost = host.split(':')[0] + '://' + host.split('/')[2]; // تنظيف الرابط

  const formData = new FormData();
  formData.append('host', cleanHost);
  formData.append('port', port);
  formData.append('username', user);
  formData.append('password', pass);

  const csrf = document.querySelector('meta[name="csrf-token"]').content;

  fetch('/upload-xtream', {
    method: 'POST',
    headers: {
      'X-CSRF-TOKEN': csrf
    },
    body: formData
  })
    .then(res => res.json())
    .then(data => {
      if (data.success) {
        alert(`✅ تم تحميل الملف:\n${data.download_url}`);
        // يمكنك الآن تشغيل تحليل الملف أو إعادة تحميل القنوات
      } else {
        alert(`❌ خطأ: ${data.error || "غير معروف"}`);
      }
    })
    .catch(err => {
      alert("❌ فشل الاتصال بالخادم");
      console.error(err);
    });
}



// ===== دوال جديدة لتحسين التفاعل =====

// دالة ملء الشاشة
function toggleFullscreen() {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen().catch(err => {
      showNotification('error', 'لا يمكن تفعيل ملء الشاشة');
    });
  } else {
    document.exitFullscreen();
  }
}

// دالة مشاركة القناة
function shareChannel() {
  if (lastChannelUrl) {
    if (navigator.share) {
      navigator.share({
        title: 'فلكسي TV - قناة مباشرة',
        text: 'شاهد هذه القناة المباشرة',
        url: window.location.href
      });
    } else {
      // نسخ الرابط للحافظة
      navigator.clipboard.writeText(window.location.href).then(() => {
        showNotification('success', 'تم نسخ الرابط إلى الحافظة');
      });
    }
  } else {
    showNotification('error', 'لا توجد قناة مشغلة حالياً');
  }
}

// دالة كتم الصوت
function toggleMute() {
  const video = document.getElementById('videoElement');
  const muteIcon = document.getElementById('muteIcon');

  if (video.muted) {
    video.muted = false;
    muteIcon.textContent = '🔊';
  } else {
    video.muted = true;
    muteIcon.textContent = '🔇';
  }
}

// دالة عرض الإشعارات المحسنة
function showNotification(message, type = 'info', duration = 3000) {
  console.log('📢 إشعار:', message, 'نوع:', type);

  const notifications = {
    success: document.getElementById('successNotice'),
    error: document.getElementById('errorNotice'),
    info: document.getElementById('loadingNotice'),
    warning: document.getElementById('errorNotice'),
    loading: document.getElementById('loadingNotice'),
    favorite: document.getElementById('favNotification')
  };

  const notification = notifications[type];
  if (!notification) {
    console.warn('⚠️ نوع الإشعار غير معروف:', type);
    return;
  }

  // تحديث الرسالة
  if (type === 'success') {
    const msgElement = document.getElementById('successMessage');
    if (msgElement) msgElement.textContent = message;
  } else if (type === 'error' || type === 'warning') {
    const msgElement = document.getElementById('errorMessage');
    if (msgElement) msgElement.textContent = message;
  }

  // إظهار الإشعار
  notification.classList.remove('hidden');
  notification.classList.add('slide-up');

  // إخفاء الإشعار بعد المدة المحددة
  setTimeout(() => {
    notification.classList.add('hidden');
    notification.classList.remove('slide-up');
  }, duration);
}

// تحسين البحث السريع
document.getElementById('searchInput').addEventListener('input', function(e) {
  const term = e.target.value.toLowerCase().trim();
  const clearBtn = document.getElementById('clearSearch');
  const searchResults = document.getElementById('searchResults');
  const resultsList = document.getElementById('searchResultsList');

  // إظهار/إخفاء زر المسح
  if (term) {
    clearBtn.classList.remove('hidden');
  } else {
    clearBtn.classList.add('hidden');
    searchResults.classList.add('hidden');
    displayChannels('all');
    return;
  }

  // البحث في القنوات
  const filtered = allChannels.filter(ch =>
    ch.title.toLowerCase().includes(term) ||
    ch.group.toLowerCase().includes(term)
  );

  // عرض النتائج السريعة
  if (filtered.length > 0 && term.length > 1) {
    resultsList.innerHTML = '';
    filtered.slice(0, 5).forEach(channel => {
      const item = document.createElement('div');
      item.className = 'flex items-center gap-3 p-2 hover:bg-gray-700 rounded cursor-pointer';
      item.innerHTML = `
        <img src="${channel.logo}" class="w-8 h-8 rounded object-cover" />
        <div class="flex-1">
          <div class="text-sm font-medium">${channel.title}</div>
          <div class="text-xs text-gray-400">${channel.group}</div>
        </div>
      `;
      item.addEventListener('click', () => {
        checkAndPlayChannel(channel.url);
        searchResults.classList.add('hidden');
      });
      resultsList.appendChild(item);
    });
    searchResults.classList.remove('hidden');
  } else {
    searchResults.classList.add('hidden');
  }

  // تصفية القنوات المعروضة
  displayFilteredChannels(filtered);
});

// زر مسح البحث
document.getElementById('clearSearch').addEventListener('click', function() {
  document.getElementById('searchInput').value = '';
  this.classList.add('hidden');
  document.getElementById('searchResults').classList.add('hidden');
  displayChannels('all');
});

// تحسين عداد المفضلة
function updateFavoriteCount() {
  const favCount = document.getElementById('favCount');
  const favorites = getFavorites();

  if (favorites.length > 0) {
    favCount.textContent = favorites.length;
    favCount.classList.remove('hidden');
  } else {
    favCount.classList.add('hidden');
  }
}

// تحسين دالة إضافة/إزالة المفضلة
function toggleFavorite(event, url) {
  event.stopPropagation();
  let favorites = getFavorites();
  let message;

  if (favorites.includes(url)) {
    favorites = favorites.filter(f => f !== url);
    message = 'تمت الإزالة من المفضلة';
    showNotification('error', message);
  } else {
    favorites.push(url);
    message = 'تم الإضافة إلى المفضلة';
    showNotification('favorite', message);
  }

  localStorage.setItem('favorites', JSON.stringify(favorites));
  updateFavoriteCount();
  displayChannels();
}

// ✅ تحسين عرض القنوات مع دعم العرض المنفصل
function displayChannels(selected = 'all') {
  console.log('📺 عرض القنوات:', selected, 'عدد القنوات:', allChannels.length);

  // تحديد الحاوية المناسبة
  const sideContainer = document.getElementById('channelGroups');
  const standaloneContainer = document.getElementById('standaloneChannels');

  // تحديد أي حاوية نستخدم
  const isStandalone = standaloneContainer && !standaloneContainer.classList.contains('hidden');
  const container = isStandalone ? standaloneContainer : sideContainer;

  if (!container) {
    console.error('❌ لم يتم العثور على حاوية القنوات');
    return;
  }

  container.innerHTML = '';

  if (!allChannels || allChannels.length === 0) {
    container.innerHTML = `
      <div class="text-center py-8">
        <div class="text-4xl mb-4">📺</div>
        <p class="text-gray-400">لا توجد قنوات متاحة</p>
        <button onclick="loadDefaultChannels()" class="mt-4 bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded">
          🔄 إعادة تحميل القنوات
        </button>
      </div>
    `;
    return;
  }

  const groups = [...new Set(allChannels.map(c => c.group))];

  groups.forEach((group, groupIndex) => {
    if (selected !== 'all' && selected !== group) return;

    const section = document.createElement('div');
    section.className = 'fade-in mb-6';
    section.style.animationDelay = `${groupIndex * 0.1}s`;
    section.innerHTML = `<h2 class="text-xl font-bold mb-4 text-gradient bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">${group}</h2>`;

    const grid = document.createElement('div');
    grid.className = 'grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4';

    allChannels.filter(c => c.group === group).forEach((channel, index) => {
      const card = document.createElement('div');
      card.className = 'channel-card fade-in';
      card.style.animationDelay = `${(groupIndex * 0.1) + (index * 0.05)}s`;

      const isFavorite = getFavorites().includes(channel.url);
      card.innerHTML = `
        <div class="relative">
          <img src="${channel.logo}" class="w-full object-contain rounded-lg mx-auto transition-transform duration-300" style="max-height: 60px;"
               onerror="this.src='https://via.placeholder.com/100x100/4A5568/FFFFFF?text=📺'" />
          <div class="absolute top-2 right-2">
            <button onclick="toggleFavorite(event, '${channel.url}')" class="text-yellow-400 hover:text-yellow-300 transition-colors duration-200">
              ${isFavorite ? '⭐' : '☆'}
            </button>
          </div>
        </div>
        <div class="text-sm mt-3 text-center font-medium truncate">${channel.title}</div>
        <div class="text-xs text-gray-400 text-center mt-1">${channel.group}</div>
      `;

      card.addEventListener('click', e => {
        if (e.target.tagName !== 'BUTTON') {
          console.log('🎬 تشغيل القناة:', channel.title);
          checkAndPlayChannel(channel.url);
          // إضافة تأثير النقر
          card.style.transform = 'scale(0.95)';
          setTimeout(() => {
            card.style.transform = '';
          }, 150);
        }
      });

      grid.appendChild(card);
    });

    section.appendChild(grid);
    container.appendChild(section);
  });

  // تحديث شريط التصنيفات
  updateCategoryBar();
  updateFavoriteCount();

  console.log('✅ تم عرض القنوات بنجاح');
}

// تحسين تحديث شريط التصنيفات
function updateCategoryBar() {
  categoryMenu.innerHTML = '';
  const groups = [...new Set(allChannels.map(c => c.group))];

  const allBtn = document.createElement('button');
  allBtn.textContent = '📺 كل القنوات';
  allBtn.className = 'block w-full text-right px-4 py-3 text-sm hover:bg-gray-700 transition-colors duration-200 rounded-lg mb-1';
  allBtn.onclick = () => {
    categoryBtn.innerHTML = `
      <span class="text-lg">📂</span>
      <span class="font-semibold">كل القنوات</span>
      <svg class="w-4 h-4 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
      </svg>
    `;
    displayChannels('all');
    categoryMenu.classList.add('hidden');
  };
  categoryMenu.appendChild(allBtn);

  groups.forEach(group => {
    const btn = document.createElement('button');
    btn.textContent = group;
    btn.className = 'block w-full text-right px-4 py-3 text-sm hover:bg-gray-700 transition-colors duration-200 rounded-lg mb-1';
    btn.onclick = () => {
      categoryBtn.innerHTML = `
        <span class="text-lg">📂</span>
        <span class="font-semibold">${group}</span>
        <svg class="w-4 h-4 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
        </svg>
      `;
      displayChannels(group);
      categoryMenu.classList.add('hidden');
    };
    categoryMenu.appendChild(btn);
  });
}

// تحسين دالة تبديل القائمة
categoryBtn.addEventListener('click', () => {
  const arrow = document.getElementById('categoryArrow');
  categoryMenu.classList.toggle('hidden');

  if (categoryMenu.classList.contains('hidden')) {
    arrow.style.transform = 'rotate(0deg)';
  } else {
    arrow.style.transform = 'rotate(180deg)';
  }
});

// إضافة مستمع للنقر خارج القائمة
document.addEventListener('click', (e) => {
  if (!categoryBtn.contains(e.target) && !categoryMenu.contains(e.target)) {
    categoryMenu.classList.add('hidden');
    document.getElementById('categoryArrow').style.transform = 'rotate(0deg)';
  }

  if (!document.getElementById('searchInput').contains(e.target) && !document.getElementById('searchResults').contains(e.target)) {
    document.getElementById('searchResults').classList.add('hidden');
  }
});

// تحديث العداد عند تحميل الصفحة
window.addEventListener('load', () => {
  updateFavoriteCount();
});

    </script>
  </body>
</html>
