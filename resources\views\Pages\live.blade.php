<!DOCTYPE html>
<html lang="ar">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />

    <meta name="csrf-token" content="{{ csrf_token() }}">

  <title>فلكسي TV - البث المباشر المحسن</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdn.plyr.io/3.7.8/plyr.css" />
  <link rel="stylesheet" href="{{ asset('css/live-tv-enhanced.css') }}" />
  <style>
    /* ===== متغيرات CSS للألوان والمقاسات ===== */
    :root {
      --primary-color: #dc2626;
      --secondary-color: #f59e0b;
      --accent-color: #3b82f6;
      --success-color: #10b981;
      --warning-color: #f59e0b;
      --danger-color: #ef4444;
      --dark-bg: #111827;
      --card-bg: #1f2937;
      --border-radius: 12px;
      --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      --shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    /* ===== تحسينات عامة ===== */
    html, body {
      overflow-x: hidden;
      scroll-behavior: smooth;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    /* ===== مشغل الفيديو المحسن ===== */
    #videoPlayer {
      width: 100%;
      background: linear-gradient(135deg, #000 0%, #1a1a1a 100%);
      aspect-ratio: 16/9;
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--shadow);
    }

    /* ===== أنيميشن محسن للنبض ===== */
    .pulse {
      animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
      transform-origin: center;
    }
    @keyframes pulse {
      0%, 100% { transform: scale(1); opacity: 1; }
      50% { transform: scale(1.05); opacity: 0.8; }
    }

    /* ===== سبينر تحميل محسن ===== */
    .spinner {
      border: 3px solid rgba(255, 255, 255, 0.2);
      border-top: 3px solid var(--primary-color);
      border-radius: 50%;
      width: 48px;
      height: 48px;
      animation: spin 1s linear infinite;
      box-shadow: 0 0 20px rgba(220, 38, 38, 0.3);
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* ===== إخفاء شريط التمرير مع تحسينات ===== */
    .hide-scrollbar::-webkit-scrollbar { display: none; }
    .hide-scrollbar {
      -ms-overflow-style: none;
      scrollbar-width: none;
      overscroll-behavior: contain;
    }

    /* ===== تحسينات التكبير ===== */
    #zoomWrapper {
      overflow: hidden;
      touch-action: none;
      position: relative;
      border-radius: var(--border-radius);
      background: #000;
    }
    #zoomWrapper video {
      transform-origin: center center;
      transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      border-radius: var(--border-radius);
    }

    /* ===== أنيميشن ping سريع ===== */
    @keyframes ping-fast {
      0% { transform: scale(1); opacity: 1; }
      50% { transform: scale(1.2); opacity: 0.6; }
      100% { transform: scale(1); opacity: 1; }
    }
    .animate-ping-fast { animation: ping-fast 0.8s ease-in-out infinite; }

    /* ===== تحسينات الأزرار ===== */
    .btn-modern {
      background: linear-gradient(135deg, var(--primary-color) 0%, #b91c1c 100%);
      border: none;
      border-radius: var(--border-radius);
      padding: 12px 24px;
      color: white;
      font-weight: 600;
      transition: var(--transition);
      box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3);
      position: relative;
      overflow: hidden;
    }

    .btn-modern:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(220, 38, 38, 0.4);
    }

    .btn-modern:active {
      transform: translateY(0);
    }

    /* ===== تحسينات البطاقات ===== */
    .channel-card {
      background: var(--card-bg);
      border-radius: var(--border-radius);
      padding: 16px;
      transition: var(--transition);
      border: 1px solid rgba(255, 255, 255, 0.1);
      position: relative;
      overflow: hidden;
    }

    .channel-card:hover {
      transform: translateY(-4px) scale(1.02);
      box-shadow: var(--shadow);
      border-color: var(--primary-color);
    }

    .channel-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
      transition: left 0.5s;
    }

    .channel-card:hover::before {
      left: 100%;
    }

    /* ===== تحسينات القوائم المنسدلة ===== */
    .dropdown-modern {
      background: var(--card-bg);
      border-radius: var(--border-radius);
      border: 1px solid rgba(255, 255, 255, 0.1);
      box-shadow: var(--shadow);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
    }

    /* ===== تحسينات الإشعارات ===== */
    .notification {
      border-radius: var(--border-radius);
      padding: 16px 20px;
      box-shadow: var(--shadow);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    /* ===== تحسينات متجاوبة للجوال ===== */
    @media (max-width: 768px) {
      .btn-modern {
        padding: 8px 12px;
        font-size: 13px;
        min-height: 44px; /* حد أدنى للمس */
      }

      .channel-card {
        padding: 8px;
        min-height: 120px;
      }

      #zoomWrapper {
        border-radius: 8px;
      }

      /* تحسين شريط التنقل للجوال */
      .mobile-nav {
        flex-direction: column;
        gap: 8px;
      }

      .mobile-nav .btn-modern {
        width: 100%;
        justify-content: center;
      }

      /* تحسين البحث للجوال */
      #searchInput {
        font-size: 16px; /* منع التكبير في iOS */
        padding: 12px 16px;
      }

      /* تحسين المشغل للجوال */
      #playerContainer {
        width: 100% !important;
        margin-bottom: 16px;
      }

      /* تحسين القوائم المنسدلة للجوال */
      .dropdown-modern {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        max-height: 50vh;
        border-radius: 16px 16px 0 0;
        transform: translateY(100%);
        transition: transform 0.3s ease-in-out;
      }

      .dropdown-modern.show {
        transform: translateY(0);
      }

      /* تحسين الإشعارات للجوال */
      #notificationContainer {
        bottom: 20px;
        right: 16px;
        left: 16px;
      }

      .notification {
        width: 100%;
        margin-bottom: 8px;
      }
    }

    @media (max-width: 480px) {
      .btn-modern {
        padding: 6px 10px;
        font-size: 12px;
      }

      .channel-card {
        padding: 6px;
        min-height: 100px;
      }

      /* تحسين الشبكة للشاشات الصغيرة */
      .mobile-grid {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 8px;
      }

      /* تحسين النصوص للشاشات الصغيرة */
      .mobile-text-sm {
        font-size: 11px;
      }

      .mobile-text-xs {
        font-size: 10px;
      }
    }

    /* ===== تأثيرات الحركة المتقدمة ===== */
    .fade-in {
      animation: fadeIn 0.6s ease-out;
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }

    .slide-up {
      animation: slideUp 0.4s ease-out;
    }

    @keyframes slideUp {
      from { transform: translateY(100%); opacity: 0; }
      to { transform: translateY(0); opacity: 1; }
    }
  </style>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/flowplayer/7.2.7/skin/skin.css">
</head>
<body class="bg-gray-900 text-white p-4 min-h-screen text-sm sm:text-base">



<!-- ===== شريط التنقل المحسن ===== -->
<div class="flex flex-wrap gap-3 mb-8 fade-in">
    <!-- زر القائمة الرئيسية -->
    <button id="categoryDropdownBtn" class="btn-modern bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 px-6 py-3 rounded-xl shadow-lg transition-all duration-300 flex items-center gap-2">
        <span class="text-lg">📂</span>
        <span class="font-semibold">القائمة</span>
        <svg class="w-4 h-4 transition-transform duration-300" id="categoryArrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
        </svg>
    </button>

    <!-- زر المفضلة -->
    <button onclick="displayFavorites()" class="btn-modern bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 px-6 py-3 rounded-xl shadow-lg transition-all duration-300 flex items-center gap-2">
        <span class="text-lg animate-ping-fast">⭐</span>
        <span class="font-semibold">المفضلة</span>
        <span id="favCount" class="bg-white text-yellow-600 text-xs px-2 py-1 rounded-full font-bold hidden">0</span>
    </button>

    <!-- زر الإعدادات -->
    <button onclick="checkPassword()" class="btn-modern bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 px-3 md:px-6 py-2 md:py-3 rounded-lg md:rounded-xl shadow-lg transition-all duration-300 flex items-center gap-1 md:gap-2 flex-1 md:flex-none">
        <span class="text-base md:text-lg">🔐</span>
        <span class="font-semibold text-sm md:text-base">إعدادات</span>
    </button>

    <!-- زر ملء الشاشة -->
    <button onclick="toggleFullscreen()" class="btn-modern bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 px-3 md:px-6 py-2 md:py-3 rounded-lg md:rounded-xl shadow-lg transition-all duration-300 flex items-center gap-1 md:gap-2 flex-1 md:flex-none">
        <span class="text-base md:text-lg">🖥️</span>
        <span class="font-semibold text-sm md:text-base">ملء الشاشة</span>
    </button>

    <!-- زر إضافة M3U للجوال -->
    <button onclick="showMobileM3UDialog()" class="btn-modern bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 px-3 py-2 rounded-lg shadow-lg transition-all duration-300 flex items-center gap-1 flex-1 md:hidden">
        <span class="text-base">📡</span>
        <span class="font-semibold text-sm">إضافة M3U</span>
    </button>

    <!-- زر إعادة تحميل للجوال -->
    <button onclick="reloadChannelsForMobile()" class="btn-modern bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 px-3 py-2 rounded-lg shadow-lg transition-all duration-300 flex items-center gap-1 flex-1 md:hidden">
        <span class="text-base">🔄</span>
        <span class="font-semibold text-sm">إعادة تحميل</span>
    </button>

    <!-- مؤشر الحالة -->
    <div class="flex items-center gap-2 bg-gray-800 px-3 md:px-4 py-2 md:py-3 rounded-lg md:rounded-xl border border-gray-700 w-full md:w-auto justify-center md:justify-start">
        <div id="statusIndicator" class="w-2 h-2 md:w-3 md:h-3 bg-green-500 rounded-full animate-pulse"></div>
        <span id="statusText" class="text-xs md:text-sm font-medium text-gray-300">متصل</span>
        <span id="channelCount" class="text-xs text-gray-400 hidden md:inline">• 0 قناة</span>
    </div>
</div>

<!-- ===== نافذة إضافة M3U للجوال ===== -->
<div id="mobileM3UDialog" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
    <div class="absolute bottom-0 left-0 right-0 bg-gray-800 rounded-t-2xl p-6 transform translate-y-full transition-transform duration-300" id="mobileM3UContent">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-bold text-white">إضافة قائمة M3U</h3>
            <button onclick="hideMobileM3UDialog()" class="text-gray-400 hover:text-white">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <div class="space-y-4">
            <!-- إدخال رابط M3U -->
            <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">رابط M3U</label>
                <input type="url" id="mobileM3UUrl" placeholder="https://example.com/playlist.m3u"
                       class="w-full p-3 bg-gray-700 rounded-lg border border-gray-600 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all duration-300 text-white placeholder-gray-400" />
            </div>

            <!-- أو رفع ملف -->
            <div class="text-center">
                <span class="text-gray-400 text-sm">أو</span>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">رفع ملف M3U</label>
                <input type="file" id="mobileM3UFile" accept=".m3u,.txt"
                       class="w-full p-3 bg-gray-700 rounded-lg border border-gray-600 focus:border-blue-500 transition-all duration-300 text-white file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-blue-600 file:text-white hover:file:bg-blue-700" />
            </div>

            <!-- أزرار العمل -->
            <div class="flex gap-3 pt-4">
                <button onclick="loadMobileM3UUrl()" class="flex-1 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white py-3 px-4 rounded-lg font-semibold transition-all duration-300">
                    📥 تحميل من الرابط
                </button>
                <button onclick="hideMobileM3UDialog()" class="flex-1 bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white py-3 px-4 rounded-lg font-semibold transition-all duration-300">
                    إلغاء
                </button>
            </div>
        </div>
    </div>
</div>

  <!-- الشعار
  <div class="flex justify-center items-center mb-6">
    <img src="https://www.arabseed.stream/images/67fcb12ad4171ic_launcher.png" alt="Flicksy Logo" class="w-12 h-12 mr-2" />
    <h1 class="text-2xl font-bold text-center">📺</h1>
  </div> -->




  <!-- نموذج الاستيراد -->
  <div id="uploadForm" class="mb-6 space-y-4 hidden">
    <input type="file" id="m3uFile" accept=".m3u,.txt" class="bg-gray-800 p-2 rounded w-full" />
    <input type="text" id="m3uUrl" placeholder="🌐 أدخل رابط M3U مباشر" class="bg-gray-800 p-2 rounded w-full" />
    <button type="button" id="loadUrl" class="bg-green-600 py-2 px-4 rounded w-full">📥 استيراد من رابط</button>

    <div class="bg-gray-800 p-3 rounded space-y-2">
      <h2 class="font-bold text-sm text-yellow-400">🌐 استيراد من Xtream Codes</h2>
      <input type="text" id="xtreamHost" placeholder="🛰️ رابط السيرفر (http://host:port)" class="w-full p-1 rounded bg-gray-700 text-sm" />
      <input type="text" id="xtreamUser" placeholder="👤 اسم المستخدم" class="w-full p-1 rounded bg-gray-700 text-sm" />
      <input type="password" id="xtreamPass" placeholder="🔑 كلمة المرور" class="w-full p-1 rounded bg-gray-700 text-sm" />
      <button type="button" onclick="loadXtream()" class="bg-blue-700 py-2 px-4 rounded w-full">📡 استيراد من Xtream</button>


  </div>
  </div>

  <script async="async" data-cfasync="false" src="//pl20482735.profitableratecpm.com/8c1ac1e8c1dbde26b523284c8e19c617/invoke.js"></script>
<div id="container-8c1ac1e8c1dbde26b523284c8e19c617"></div>





  <!-- زر تحميل القنوات المحفوظة -->
<div class="relative group mb-4">
  <button type="button" onclick="loadChannelsFromDefault()" class="bg-red-700 py-2 px-4 rounded w-full">
      تحديث 🚀🚀🚀 البث
  </button>
</div>


<!-- زر القائمة -->
<div class="relative mb-4">
  <button onclick="toggleM3UDropdown()" class="bg-purple-700 px-4 py-2 rounded w-full">
    🌐 🚀 سوف يعمل قريبا
  </button>

  <!-- القائمة المنسدلة -->
  <div id="m3uDropdown" class="hidden absolute mt-2 w-full bg-gray-800 rounded shadow-lg z-30">
      <button onclick="loadFromUrl('https://example.com/all.m3u')" class="block w-full text-right px-4 py-2 hover:bg-gray-700">🌐 جميع القنوات</button>

    <button onclick="loadFromUrl('https://example.com/movies.m3u')" class="block w-full text-right px-4 py-2 hover:bg-gray-700">🎬 أفلام</button>

    <button onclick="loadFromUrl('https://example.com/sports.m3u')" class="block w-full text-right px-4 py-2 hover:bg-gray-700">⚽ رياضة</button>

    <button onclick="loadFromUrl('https://example.com/kids.m3u')" class="block w-full text-right px-4 py-2 hover:bg-gray-700">🧒 أطفال</button>

    <button onclick="loadFromUrl('https://example.com/news.m3u')" class="block w-full text-right px-4 py-2 hover:bg-gray-700">📰 أخبار</button>

  </div>
</div>



  <!-- ===== شريط البحث المحسن ===== -->
  <div class="relative mb-6 fade-in">
    <div class="relative">
      <input type="text" id="searchInput"
             placeholder="🔍 ابحث عن قناة أو برنامج..."
             class="w-full p-4 pr-12 bg-gradient-to-r from-gray-800 to-gray-700 rounded-xl border border-gray-600 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all duration-300 text-white placeholder-gray-400" />

      <!-- أيقونة البحث -->
      <div class="absolute right-4 top-1/2 transform -translate-y-1/2">
        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
        </svg>
      </div>

      <!-- زر مسح البحث -->
      <button id="clearSearch" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors duration-200 hidden">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <!-- نتائج البحث السريع -->
    <div id="searchResults" class="absolute top-full left-0 right-0 mt-2 bg-gray-800 rounded-xl shadow-2xl border border-gray-600 hidden z-30 max-h-60 overflow-y-auto">
      <div class="p-3 text-sm text-gray-400 border-b border-gray-700">نتائج البحث</div>
      <div id="searchResultsList" class="p-2"></div>
    </div>
  </div>

  <!-- قائمة التصنيفات المحسنة -->
  <div id="categoriesBar" class="dropdown-modern absolute hidden mt-2 w-64 max-h-80 overflow-y-auto z-20"></div>

  <!-- المشغل -->
 <!-- ✅ مشغل + قنوات بجانب بعض (المشغل في اليمين) -->
<div id="sideBySideContainer" class="flex flex-col md:flex-row-reverse gap-4 mb-6">




<!-- ✅ حركة اليد لتوضيح التكبير -->
<div id="zoomGesture" class="fixed inset-0 z-50 flex flex-col items-center justify-center bg-black bg-opacity-60 hidden">
  <img src="https://upload.wikimedia.org/wikipedia/commons/6/6b/Pinch-zoom-gesture.gif"
       alt="zoom gesture" class="w-32 h-32 rounded-xl shadow-lg" />
  <p class="mt-4 text-white text-center text-sm animate-bounce">📲 استخدم إصبعين للتكبير</p>
</div>


  <!-- ✅ المشغل المتجاوب -->
  <div id="playerContainer" class="relative w-full md:w-3/5 overflow-hidden hide-scrollbar hidden">

    <div id="loadingSpinner" class="hidden absolute inset-0 flex items-center justify-center bg-black bg-opacity-60 z-10">
      <div class="spinner"></div>
    </div>

   <!-- Plyr داخل Zoom Wrapper -->
<div id="plyrContainer" class="hide-scrollbar overflow-hidden flex" style="max-height: 100%; height: 100%;">
  <div id="zoomWrapper" class="touch-none w-full h-full" style="touch-action: none;">
  <video id="videoElement" controls playsinline style="width: 100%; height: 100%; object-fit: contain;"></video>
  <!-- زر التكبير فقط -->
  <button id="zoomButton"
    onclick="cycleZoom()"
    class="hidden absolute bottom-4 left-4 bg-white/80 text-black border border-gray-400 px-4 py-2 rounded-full shadow-md text-xs font-semibold backdrop-blur-sm z-50 transition-all duration-300">
    ✂️ Zoom ×1
  </button>
</div>
</div>


    <!-- Flowplayer -->
    <div id="flowContainer" class="w-full hidden hide-scrollbar overflow-hidden">
      <div id="flowInstance" class="flowplayer fixed-controls bg-black rounded" data-ratio="0.5625" data-embed="true">
        <video>
          <source id="flowSource" type="application/x-mpegurl" src="">
        </video>
      </div>
    </div>

    <!-- ===== أزرار التحكم المتجاوبة ===== -->
    <div class="mt-3 md:mt-4 flex flex-wrap gap-2 md:gap-3 justify-center">
      <!-- الصف الأول: أزرار أساسية -->
      <div class="flex gap-2 w-full md:w-auto">
        <!-- زر الإغلاق -->
        <button onclick="closePlayer()" class="btn-modern bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 px-3 md:px-4 py-2 rounded-lg flex items-center gap-1 md:gap-2 pulse flex-1 md:flex-none">
          <span class="text-sm md:text-base">❌</span>
          <span class="text-xs md:text-sm font-medium">إغلاق</span>
        </button>

        <!-- زر القناة السابقة -->
        <button onclick="playLastChannel()" class="btn-modern bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 px-3 md:px-4 py-2 rounded-lg flex items-center gap-1 md:gap-2 flex-1 md:flex-none">
          <span class="text-sm md:text-base">🔁</span>
          <span class="text-xs md:text-sm font-medium">السابقة</span>
        </button>

        <!-- زر تبديل العرض -->
        <button onclick="togglePlayerLayout()" id="toggleLayoutBtn" class="btn-modern bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 px-3 md:px-4 py-2 rounded-lg flex items-center gap-1 md:gap-2 flex-1 md:flex-none">
          <span class="text-sm md:text-base">🧭</span>
          <span class="text-xs md:text-sm font-medium">عرض</span>
        </button>
      </div>

      <!-- الصف الثاني: أزرار إضافية -->
      <div class="flex gap-2 w-full md:w-auto">
        <!-- زر مشاركة -->
        <button onclick="shareChannel()" class="btn-modern bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 px-3 md:px-4 py-2 rounded-lg flex items-center gap-1 md:gap-2 flex-1 md:flex-none">
          <span class="text-sm md:text-base">📤</span>
          <span class="text-xs md:text-sm font-medium">مشاركة</span>
        </button>

        <!-- زر الصوت -->
        <button onclick="toggleMute()" id="muteBtn" class="btn-modern bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 px-3 md:px-4 py-2 rounded-lg flex items-center gap-1 md:gap-2 flex-1 md:flex-none">
          <span id="muteIcon" class="text-sm md:text-base">🔊</span>
          <span class="text-xs md:text-sm font-medium">صوت</span>
        </button>

        <!-- زر ملء الشاشة للجوال -->
        <button onclick="toggleFullscreen()" class="btn-modern bg-gradient-to-r from-indigo-600 to-indigo-700 hover:from-indigo-700 hover:to-indigo-800 px-3 py-2 rounded-lg flex items-center gap-1 flex-1 md:hidden">
          <span class="text-sm">🖥️</span>
          <span class="text-xs font-medium">ملء الشاشة</span>
        </button>
      </div>
    </div>
  </div>

  <!-- ✅ القنوات المتجاوبة -->
  <div id="channelGroups" class="w-full md:w-2/5 space-y-4 md:space-y-6 overflow-y-auto max-h-[70vh] md:max-h-[80vh] pr-1 hide-scrollbar"></div>
</div>



  <!-- ===== الإشعارات المحسنة ===== -->
  <div id="notificationContainer" class="fixed bottom-6 right-6 z-50 space-y-3">
    <!-- إشعار المفضلة -->
    <div id="favNotification" class="notification bg-gradient-to-r from-yellow-500 to-yellow-600 text-white px-6 py-4 rounded-xl shadow-2xl hidden transform transition-all duration-300 slide-up">
      <div class="flex items-center gap-3">
        <span class="text-xl">⭐</span>
        <div>
          <div class="font-semibold">تم الإضافة إلى المفضلة</div>
          <div class="text-sm opacity-90">يمكنك الوصول إليها من قائمة المفضلة</div>
        </div>
      </div>
    </div>

    <!-- إشعار التحميل -->
    <div id="loadingNotice" class="notification bg-gradient-to-r from-blue-500 to-blue-600 text-white px-6 py-4 rounded-xl shadow-2xl hidden transform transition-all duration-300 slide-up">
      <div class="flex items-center gap-3">
        <div class="spinner w-5 h-5"></div>
        <div>
          <div class="font-semibold">جارٍ التحميل...</div>
          <div class="text-sm opacity-90">يرجى الانتظار</div>
        </div>
      </div>
    </div>

    <!-- إشعار النجاح -->
    <div id="successNotice" class="notification bg-gradient-to-r from-green-500 to-green-600 text-white px-6 py-4 rounded-xl shadow-2xl hidden transform transition-all duration-300 slide-up">
      <div class="flex items-center gap-3">
        <span class="text-xl">✅</span>
        <div>
          <div class="font-semibold">تم بنجاح!</div>
          <div class="text-sm opacity-90" id="successMessage">العملية تمت بنجاح</div>
        </div>
      </div>
    </div>

    <!-- إشعار الخطأ -->
    <div id="errorNotice" class="notification bg-gradient-to-r from-red-500 to-red-600 text-white px-6 py-4 rounded-xl shadow-2xl hidden transform transition-all duration-300 slide-up">
      <div class="flex items-center gap-3">
        <span class="text-xl">❌</span>
        <div>
          <div class="font-semibold">حدث خطأ!</div>
          <div class="text-sm opacity-90" id="errorMessage">يرجى المحاولة مرة أخرى</div>
        </div>
      </div>
    </div>
  </div>

  <!-- صوت عند التشغيل -->
  <audio id="startSound" src="https://cdn.pixabay.com/download/audio/2022/03/15/audio_f93c5643bf.mp3?filename=tweet.mp3" preload="auto"></audio>

  <!-- سكربتات -->
  <script src="https://cdn.plyr.io/3.7.8/plyr.polyfilled.js"></script>
  <script src="{{ asset('js/live-tv-enhanced.js') }}"></script>
  <script type='text/javascript' src='//pl20482749.profitableratecpm.com/9f/8d/83/9f8d8377cd0b12067194770cb79f68cf.js'></script>



  <!-- flowplayer -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/flowplayer/7.2.7/flowplayer.min.js"></script>
<script src="https://releases.flowplayer.org/hlsjs/flowplayer.hlsjs.min.js"></script>




  <script type='text/javascript' src='//pl20479207.profitableratecpm.com/b9/d4/b9/b9d4b953bed55faf387da628be9148d2.js'></script>
  <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
  <script>
    const player = new Plyr('#videoElement');

    // 🔧 إجبار fullscreen أن يشمل #zoomWrapper بالكامل
player.on('enterfullscreen', () => {
  const zoomBtn = document.getElementById('zoomButton');
  zoomBtn.classList.remove('hidden');
});

player.on('exitfullscreen', () => {
  const zoomBtn = document.getElementById('zoomButton');
  zoomBtn.classList.add('hidden');
  lastScale = 1;
  currentX = 0;
  currentY = 0;
  applyTransform();
  zoomBtn.innerText = '✂️ Zoom ×1';
  currentZoomIndex = 0;
});
    const video = document.getElementById('videoElement');

    const categoryBtn = document.getElementById('categoryDropdownBtn');
    const categoryMenu = document.getElementById("categoriesBar");
    const spinner = document.getElementById("loadingSpinner");
    let allChannels = [], lastChannelUrl = null, showingFavorites = false;

    function checkPassword() {
      const input = prompt("🔐 أدخل كلمة المرور:");
      if (input === "") {
        document.getElementById('uploadForm').classList.remove('hidden');
        document.getElementById('adminAccess').classList.add('hidden');
      } else {
        alert("❌ كلمة المرور غير صحيحة");
      }
    }

    function parseM3U(content) {
      const lines = content.split('\n');
      const channels = [];
      for (let i = 0; i < lines.length; i++) {
        if (lines[i].startsWith('#EXTINF')) {
          const title = lines[i].split(',')[1]?.trim() || 'بدون اسم';
          const logo = lines[i].match(/tvg-logo="(.*?)"/)?.[1] || 'https://via.placeholder.com/100x100';
          const group = lines[i].match(/group-title="(.*?)"/)?.[1] || 'غير مصنف';
          const url = lines[i + 1]?.trim();
          if (url?.startsWith('http')) channels.push({ title, logo, group, url });
        }
      }
      allChannels = channels;
      updateCategoryBar();
      displayChannels();
    }

    // تم نقل هذه الدالة إلى الأسفل مع التحسينات

    // تم نقل هذه الدالة إلى الأسفل مع التحسينات

    function checkAndPlayChannel(url) {
      console.log('🔍 فحص الرابط:', url);
      spinner.classList.remove("hidden");

      // تنظيف الرابط
      url = url.trim();

      // فحص أساسي للرابط
      if (!url || (!url.startsWith('http') && !url.startsWith('rtmp') && !url.startsWith('rtsp'))) {
        showNotification('❌ رابط غير صالح', 'error');
        spinner.classList.add("hidden");
        return;
      }

      // محاولة فحص الرابط (مع timeout)
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 ثوان timeout

      fetch(url, {
        method: 'HEAD',
        signal: controller.signal,
        mode: 'no-cors' // تجنب مشاكل CORS
      })
        .then(res => {
          clearTimeout(timeoutId);
          console.log('✅ الرابط متاح، بدء التشغيل...');
          playChannel(url);
        })
        .catch(error => {
          clearTimeout(timeoutId);
          console.warn('⚠️ فشل فحص الرابط، محاولة التشغيل المباشر:', error.message);

          // حتى لو فشل الفحص، نحاول التشغيل (قد يكون الخادم لا يدعم HEAD)
          if (error.name === 'AbortError') {
            showNotification('⏱️ انتهت مهلة الفحص، محاولة التشغيل...', 'warning');
          } else {
            showNotification('🔄 محاولة التشغيل بدون فحص...', 'info');
          }

          playChannel(url);
        })
        .finally(() => {
          // لا نخفي السبينر هنا، سيتم إخفاؤه في playChannel
        });
    }

  function playChannel(url) {
  lastChannelUrl = url;
  spinner.classList.remove("hidden");

  // إظهار المشغل والقنوات في تخطيط side-by-side
  document.getElementById('sideBySideContainer').classList.remove('hidden');
  document.getElementById('playerContainer').classList.remove('hidden');
  document.getElementById('channelGroups').classList.remove('hidden');

  // إظهار plyr وإخفاء flowplayer
  document.getElementById('plyrContainer').classList.remove('hidden');
  document.getElementById('flowContainer').classList.add('hidden');

  const video = document.getElementById("videoElement");

  // تنظيف المشغل
  video.pause();
  video.removeAttribute("src");
  video.load();
  if (window.hls) {
    window.hls.destroy();
    window.hls = null;
  }

  // تحسين كشف نوع الرابط
  const urlLower = url.toLowerCase();
  const ext = url.split('?')[0].split('.').pop().toLowerCase();

  console.log('🎯 محاولة تشغيل:', url);
  console.log('📄 نوع الملف:', ext);
  console.log('🔗 البروتوكول:', url.split(':')[0]);

  // 1. فحص البروتوكولات الخاصة أولاً
  if (urlLower.startsWith('rtmp://') || urlLower.startsWith('rtmps://')) {
    handleRTMPStream(url, video);
  }
  else if (urlLower.startsWith('rtsp://')) {
    handleRTSPStream(url, video);
  }
  // 2. محاولة HLS (أكثر شيوعاً في البث المباشر)
  else if (ext === "m3u8" || urlLower.includes('.m3u8') || urlLower.includes('hls')) {
    playWithHLS(url, video);
  }
  // 3. محاولة DASH
  else if (ext === "mpd" || urlLower.includes('.mpd') || urlLower.includes('dash')) {
    playWithDASH(url, video);
  }
  // 4. ملفات الفيديو العادية
  else if (["mp4", "ts", "webm", "ogg", "avi", "mkv", "flv", "mov", "wmv"].includes(ext)) {
    playDirectVideo(url, video);
  }
  // 5. ملفات الصوت
  else if (["mp3", "aac", "m4a", "wav", "flac", "ogg"].includes(ext)) {
    playDirectAudio(url, video);
  }
  // 6. روابط YouTube
  else if (urlLower.includes('youtube.com') || urlLower.includes('youtu.be')) {
    playYouTubeVideo(url, video);
  }
  // 7. روابط خاصة أخرى
  else if (urlLower.includes('twitch.tv') || urlLower.includes('dailymotion.com')) {
    handleSpecialPlatforms(url, video);
  }
  // 8. محاولة تشغيل مباشر (للروابط غير المعروفة)
  else {
    tryMultipleFormats(url, video);
  }

  document.getElementById('startSound').play().catch(() => {});
  isSideBySide = true;
  document.getElementById('playerContainer').scrollIntoView({ behavior: 'smooth' });
}

// دالة تشغيل HLS محسنة
function playWithHLS(url, video) {
  console.log('📺 محاولة تشغيل HLS:', url);

  if (Hls.isSupported()) {
    // إعدادات HLS محسنة
    const hlsConfig = {
      maxBufferLength: 30,
      maxRetry: 10,
      retryDelay: 1000,
      maxRetryDelay: 8000,
      enableWorker: true,
      lowLatencyMode: true,
      backBufferLength: 90,
      maxBufferSize: 60 * 1000 * 1000, // 60MB
      maxBufferHole: 0.5,
      highBufferWatchdogPeriod: 2,
      nudgeOffset: 0.1,
      nudgeMaxRetry: 3,
      maxFragLookUpTolerance: 0.25,
      liveSyncDurationCount: 3,
      liveMaxLatencyDurationCount: 10,
      liveDurationInfinity: false,
      enableSoftwareAES: true,
      manifestLoadingTimeOut: 10000,
      manifestLoadingMaxRetry: 3,
      manifestLoadingRetryDelay: 1000,
      levelLoadingTimeOut: 10000,
      levelLoadingMaxRetry: 4,
      levelLoadingRetryDelay: 1000,
      fragLoadingTimeOut: 20000,
      fragLoadingMaxRetry: 6,
      fragLoadingRetryDelay: 1000,
      startFragPrefetch: true,
      testBandwidth: true
    };

    const hls = new Hls(hlsConfig);

    hls.loadSource(url);
    hls.attachMedia(video);
    window.hls = hls;

    // معالج نجاح تحليل القائمة
    hls.on(Hls.Events.MANIFEST_PARSED, (event, data) => {
      console.log('✅ HLS: تم تحليل القائمة بنجاح');
      console.log('📊 مستويات الجودة المتاحة:', data.levels.length);

      video.play().then(() => {
        showZoomGesture();
        spinner.classList.add("hidden");
        showNotification('✅ تم بدء التشغيل بنجاح', 'success', 2000);
      }).catch(err => {
        console.warn('⚠️ HLS: فشل التشغيل التلقائي:', err);
        spinner.classList.add("hidden");
        showNotification('⚠️ اضغط زر التشغيل لبدء المشاهدة', 'warning');
      });
    });

    // معالج الأخطاء المحسن
    hls.on(Hls.Events.ERROR, (event, data) => {
      console.error('❌ HLS خطأ:', data);

      if (data.fatal) {
        switch(data.type) {
          case Hls.ErrorTypes.NETWORK_ERROR:
            console.warn("📛 خطأ شبكة، إعادة المحاولة...");
            showNotification('🔄 خطأ في الشبكة، جاري إعادة المحاولة...', 'warning');
            setTimeout(() => {
              hls.startLoad();
            }, 2000);
            break;

          case Hls.ErrorTypes.MEDIA_ERROR:
            console.warn("📛 خطأ وسائط، محاولة الإصلاح...");
            showNotification('🔧 خطأ في الوسائط، جاري الإصلاح...', 'warning');
            try {
              hls.recoverMediaError();
            } catch (err) {
              console.error('فشل إصلاح خطأ الوسائط:', err);
              playDirectVideo(url, video);
            }
            break;

          case Hls.ErrorTypes.KEY_SYSTEM_ERROR:
            console.warn("📛 خطأ في نظام المفاتيح");
            showNotification('🔐 خطأ في التشفير، محاولة طريقة أخرى...', 'error');
            playDirectVideo(url, video);
            break;

          default:
            console.warn("📛 خطأ قاتل غير معروف، محاولة تشغيل مباشر...");
            showNotification('❌ خطأ غير معروف، محاولة طريقة أخرى...', 'error');
            playDirectVideo(url, video);
            break;
        }
      } else {
        // أخطاء غير قاتلة
        console.warn('⚠️ HLS تحذير:', data);
      }
    });

    // معالجات إضافية للمراقبة
    hls.on(Hls.Events.FRAG_LOADING, () => {
      console.log('📥 تحميل جزء...');
    });

    hls.on(Hls.Events.FRAG_LOADED, () => {
      console.log('✅ تم تحميل جزء');
    });

    hls.on(Hls.Events.LEVEL_SWITCHED, (event, data) => {
      console.log('🔄 تم تغيير مستوى الجودة إلى:', data.level);
    });

  } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
    // دعم HLS الأصلي في Safari
    console.log('🍎 استخدام HLS الأصلي في Safari');
    video.src = url;
    video.load();

    video.play().then(() => {
      console.log('✅ Safari HLS: تم التشغيل بنجاح');
      spinner.classList.add("hidden");
      showNotification('✅ تم بدء التشغيل', 'success', 2000);
    }).catch((err) => {
      console.warn('❌ Safari HLS فشل:', err);
      showNotification('⚠️ فشل HLS، محاولة طريقة أخرى...', 'warning');
      playDirectVideo(url, video);
    });

  } else {
    console.warn('❌ HLS غير مدعوم في هذا المتصفح');
    showNotification('⚠️ HLS غير مدعوم، محاولة تشغيل مباشر...', 'warning');
    playDirectVideo(url, video);
  }
}

// دالة تشغيل DASH
function playWithDASH(url, video) {
  // للمستقبل: يمكن إضافة دعم DASH هنا
  console.log('📺 DASH: محاولة تشغيل مباشر...');
  playDirectVideo(url, video);
}

// دالة تشغيل الفيديو المباشر
function playDirectVideo(url, video) {
  console.log('🎬 تشغيل فيديو مباشر:', url);
  video.src = url;
  video.load();

  video.play().then(() => {
    console.log('✅ تم التشغيل بنجاح');
    spinner.classList.add("hidden");
  }).catch(err => {
    console.warn("📛 فشل التشغيل المباشر:", err);
    // محاولة مع إعدادات مختلفة
    video.crossOrigin = "anonymous";
    video.load();
    video.play().catch(() => {
      console.warn("📛 فشل مع CORS، محاولة fallback...");
      fallbackToFlowplayer(url);
    });
  });
}

// دالة تشغيل الصوت
function playDirectAudio(url, video) {
  console.log('🎵 تشغيل صوت:', url);
  video.src = url;
  video.load();
  video.play().then(() => {
    spinner.classList.add("hidden");
  }).catch(() => {
    fallbackToFlowplayer(url);
  });
}

// دالة التعامل مع RTMP
function handleRTMPStream(url, video) {
  console.log('📡 RTMP Stream:', url);
  showNotification('⚠️ RTMP يتطلب مشغل خارجي مثل VLC', 'warning');

  // RTMP لا يعمل مباشرة في المتصفحات الحديثة
  createFallbackPlayer(url);
}

// دالة التعامل مع RTSP
function handleRTSPStream(url, video) {
  console.log('📡 RTSP Stream:', url);
  showNotification('⚠️ RTSP يتطلب مشغل خارجي مثل VLC', 'warning');

  // RTSP لا يعمل مباشرة في المتصفحات
  createFallbackPlayer(url);
}

// دالة تشغيل YouTube (للمستقبل)
function playYouTubeVideo(url, video) {
  console.log('📺 رابط YouTube:', url);
  showNotification('⚠️ روابط YouTube تحتاج معالجة خاصة', 'warning');

  // يمكن إضافة دعم YouTube API هنا في المستقبل
  createFallbackPlayer(url);
}

// دالة التعامل مع منصات خاصة
function handleSpecialPlatforms(url, video) {
  console.log('🌐 منصة خاصة:', url);
  showNotification('⚠️ هذه المنصة تحتاج معالجة خاصة', 'warning');

  // محاولة تشغيل مباشر أولاً
  playDirectVideo(url, video);
}

// دالة محاولة أشكال متعددة
function tryMultipleFormats(url, video) {
  console.log('🔄 محاولة أشكال متعددة للرابط:', url);

  let attemptCount = 0;
  const maxAttempts = 4;

  function attemptPlay(method) {
    attemptCount++;
    console.log(`🎯 المحاولة ${attemptCount}/${maxAttempts}: ${method}`);

    switch(method) {
      case 'direct':
        return playDirectVideo(url, video);
      case 'hls':
        return playWithHLS(url, video);
      case 'cors':
        return playWithCORS(url, video);
      case 'fallback':
        return createFallbackPlayer(url);
    }
  }

  // محاولة 1: تشغيل مباشر
  video.src = url;
  video.load();

  const playPromise = video.play();

  if (playPromise !== undefined) {
    playPromise.then(() => {
      console.log('✅ نجح التشغيل المباشر');
      spinner.classList.add("hidden");
    }).catch((error) => {
      console.log('⚠️ فشل التشغيل المباشر:', error.message);

      // محاولة 2: كـ HLS
      if (attemptCount < maxAttempts) {
        setTimeout(() => {
          console.log('🔄 محاولة HLS...');
          playWithHLS(url, video);
        }, 1000);
      }
    });

    // فحص حالة التحميل
    video.addEventListener('loadstart', () => {
      console.log('📥 بدء التحميل...');
    });

    video.addEventListener('canplay', () => {
      console.log('✅ جاهز للتشغيل');
      spinner.classList.add("hidden");
    });

    video.addEventListener('error', (e) => {
      console.error('❌ خطأ في الفيديو:', e);
      if (attemptCount < maxAttempts) {
        setTimeout(() => {
          console.log('🔄 محاولة مع CORS...');
          playWithCORS(url, video);
        }, 2000);
      } else {
        console.log('🆘 فشل جميع المحاولات، عرض البديل...');
        createFallbackPlayer(url);
      }
    });

    // timeout للمحاولة
    setTimeout(() => {
      if (video.readyState < 2 && attemptCount < maxAttempts) {
        console.log('⏱️ انتهت مهلة المحاولة، جرب طريقة أخرى...');
        playWithCORS(url, video);
      }
    }, 5000);

  } else {
    // متصفح قديم
    console.log('🕰️ متصفح قديم، استخدام طريقة تقليدية...');
    setTimeout(() => {
      if (video.readyState >= 2) {
        spinner.classList.add("hidden");
      } else {
        createFallbackPlayer(url);
      }
    }, 3000);
  }
}

// دالة تشغيل مع إعدادات CORS
function playWithCORS(url, video) {
  console.log('🌐 محاولة مع إعدادات CORS مختلفة...');

  // تجربة إعدادات CORS مختلفة
  const corsSettings = ['anonymous', 'use-credentials', ''];
  let corsIndex = 0;

  function tryCORS() {
    if (corsIndex < corsSettings.length) {
      video.crossOrigin = corsSettings[corsIndex];
      video.src = url;
      video.load();

      video.play().then(() => {
        console.log(`✅ نجح مع CORS: ${corsSettings[corsIndex] || 'none'}`);
        spinner.classList.add("hidden");
      }).catch(() => {
        corsIndex++;
        if (corsIndex < corsSettings.length) {
          console.log(`⚠️ فشل CORS ${corsSettings[corsIndex-1]}, جرب ${corsSettings[corsIndex]}...`);
          setTimeout(tryCORS, 1000);
        } else {
          console.log('❌ فشل جميع إعدادات CORS');
          createFallbackPlayer(url);
        }
      });
    }
  }

  tryCORS();
}



function fallbackToFlowplayer(url) {
  console.log('🔄 التبديل إلى Flowplayer:', url);

  try {
    // إخفاء plyr، إظهار flowplayer
    document.getElementById("plyrContainer").classList.add("hidden");

    const flowContainer = document.getElementById("flowContainer");
    if (flowContainer) {
      flowContainer.classList.remove("hidden");

      const flowSource = document.getElementById("flowSource");
      if (flowSource) {
        flowSource.src = url;

        // إعادة تهيئة Flowplayer إذا كان متوفراً
        if (typeof flowplayer !== 'undefined') {
          flowplayer();
        }
      }
    } else {
      // إنشاء مشغل بديل إذا لم يكن Flowplayer متوفراً
      createFallbackPlayer(url);
    }

    spinner.classList.add("hidden");
  } catch (error) {
    console.error('❌ خطأ في Flowplayer:', error);
    createFallbackPlayer(url);
  }
}

// إنشاء مشغل بديل
function createFallbackPlayer(url) {
  console.log('🆘 إنشاء مشغل بديل');

  const container = document.getElementById("plyrContainer");
  container.innerHTML = `
    <div class="bg-gray-800 p-6 rounded-xl text-center">
      <h3 class="text-xl font-bold mb-4 text-yellow-400">⚠️ تعذر تشغيل الرابط في المتصفح</h3>
      <p class="text-gray-300 mb-4">هذا الرابط يتطلب مشغل خارجي مثل VLC</p>

      <div class="space-y-3">
        <!-- زر نسخ الرابط -->
        <button onclick="copyToClipboard('${url}')"
                class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg transition-colors">
          📋 نسخ الرابط
        </button>

        <!-- زر فتح في VLC (للجوال) -->
        <button onclick="openInVLC('${url}')"
                class="w-full bg-orange-600 hover:bg-orange-700 text-white py-3 px-4 rounded-lg transition-colors">
          🎬 فتح في VLC
        </button>

        <!-- زر فتح في تطبيق خارجي -->
        <button onclick="openInExternalApp('${url}')"
                class="w-full bg-green-600 hover:bg-green-700 text-white py-3 px-4 rounded-lg transition-colors">
          📱 فتح في تطبيق خارجي
        </button>

        <!-- عرض الرابط -->
        <div class="bg-gray-700 p-3 rounded-lg">
          <p class="text-xs text-gray-400 mb-2">الرابط:</p>
          <p class="text-sm text-white break-all">${url}</p>
        </div>

        <!-- نصائح -->
        <div class="bg-blue-900 p-3 rounded-lg text-left">
          <p class="text-sm text-blue-200 font-semibold mb-2">💡 نصائح:</p>
          <ul class="text-xs text-blue-300 space-y-1">
            <li>• انسخ الرابط وافتحه في VLC</li>
            <li>• استخدم تطبيق IPTV Player</li>
            <li>• جرب متصفح مختلف</li>
          </ul>
        </div>
      </div>
    </div>
  `;

  container.classList.remove("hidden");
  spinner.classList.add("hidden");
}

// نسخ الرابط إلى الحافظة
function copyToClipboard(url) {
  if (navigator.clipboard) {
    navigator.clipboard.writeText(url).then(() => {
      showNotification('✅ تم نسخ الرابط إلى الحافظة', 'success');
    }).catch(() => {
      fallbackCopyToClipboard(url);
    });
  } else {
    fallbackCopyToClipboard(url);
  }
}

// نسخ بديلة للمتصفحات القديمة
function fallbackCopyToClipboard(url) {
  const textArea = document.createElement('textarea');
  textArea.value = url;
  textArea.style.position = 'fixed';
  textArea.style.left = '-999999px';
  textArea.style.top = '-999999px';
  document.body.appendChild(textArea);
  textArea.focus();
  textArea.select();

  try {
    document.execCommand('copy');
    showNotification('✅ تم نسخ الرابط', 'success');
  } catch (err) {
    showNotification('❌ فشل نسخ الرابط', 'error');
  }

  document.body.removeChild(textArea);
}

// فتح في VLC
function openInVLC(url) {
  // محاولة فتح VLC على الجوال
  const vlcUrl = `vlc://${url}`;
  const vlcIntent = `intent://${url}#Intent;package=org.videolan.vlc;type=video/*;scheme=http;end`;

  // للأندرويد
  if (/Android/i.test(navigator.userAgent)) {
    window.location.href = vlcIntent;
    setTimeout(() => {
      showNotification('💡 إذا لم يفتح VLC، قم بتثبيته من متجر التطبيقات', 'info');
    }, 2000);
  }
  // للـ iOS
  else if (/iPhone|iPad|iPod/i.test(navigator.userAgent)) {
    window.location.href = vlcUrl;
    setTimeout(() => {
      showNotification('💡 إذا لم يفتح VLC، قم بتثبيته من App Store', 'info');
    }, 2000);
  }
  // للكمبيوتر
  else {
    copyToClipboard(url);
    showNotification('💻 تم نسخ الرابط. افتح VLC واضغط Ctrl+N والصق الرابط', 'info');
  }
}

// فتح في تطبيق خارجي
function openInExternalApp(url) {
  // محاولة فتح في تطبيقات مختلفة
  const apps = [
    `mxplayer://${url}`,
    `kodi://${url}`,
    `potplayer://${url}`,
    `mpv://${url}`
  ];

  if (/Android/i.test(navigator.userAgent)) {
    // للأندرويد - محاولة MX Player
    const mxIntent = `intent://${url}#Intent;package=com.mxtech.videoplayer.ad;type=video/*;scheme=http;end`;
    window.location.href = mxIntent;
  } else {
    // للأنظمة الأخرى - نسخ الرابط
    copyToClipboard(url);
    showNotification('📋 تم نسخ الرابط. استخدم مشغل الوسائط المفضل لديك', 'info');
  }
}




// دالة عرض الإشعارات
function showNotification(message, type = 'info', duration = 3000) {
  // إنشاء عنصر الإشعار
  const notification = document.createElement('div');
  notification.className = `notification fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transform translate-x-full transition-transform duration-300`;

  // تحديد لون الإشعار حسب النوع
  switch(type) {
    case 'success':
      notification.classList.add('bg-green-600', 'text-white');
      break;
    case 'error':
      notification.classList.add('bg-red-600', 'text-white');
      break;
    case 'warning':
      notification.classList.add('bg-yellow-600', 'text-white');
      break;
    default:
      notification.classList.add('bg-blue-600', 'text-white');
  }

  notification.innerHTML = `
    <div class="flex items-center justify-between">
      <span class="text-sm font-medium">${message}</span>
      <button onclick="this.parentElement.parentElement.remove()" class="ml-3 text-white hover:text-gray-200">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>
  `;

  // إضافة الإشعار إلى الصفحة
  document.body.appendChild(notification);

  // إظهار الإشعار
  setTimeout(() => {
    notification.classList.remove('translate-x-full');
  }, 100);

  // إخفاء الإشعار تلقائياً
  setTimeout(() => {
    notification.classList.add('translate-x-full');
    setTimeout(() => {
      if (notification.parentElement) {
        notification.remove();
      }
    }, 300);
  }, duration);
}

    function closePlayer() {
  // إيقاف التشغيل وتنظيف المشغل
  if (window.hls) {
    window.hls.destroy();
    window.hls = null;
  }

  const video = document.getElementById("videoElement");
  if (video) {
    video.pause();
    video.removeAttribute("src");
    video.load();
  }

  // إخفاء المشغل وإظهار القنوات فقط
  document.getElementById('playerContainer').classList.add('hidden');
  document.getElementById('sideBySideContainer').classList.add('hidden');
  document.getElementById('channelGroups').classList.remove('hidden');

  // إعادة تعيين المتغيرات
  isSideBySide = false;
  lastChannelUrl = null;

  // إخفاء السبينر إذا كان ظاهراً
  const spinner = document.getElementById("loadingSpinner");
  if (spinner) spinner.classList.add("hidden");
}


    function playLastChannel() {
      if (lastChannelUrl) {
        showNotification('🔄 إعادة تشغيل القناة السابقة...', 'info', 1500);
        checkAndPlayChannel(lastChannelUrl);
      } else {
        showNotification('❌ لا توجد قناة سابقة', 'warning', 2000);
      }
    }

    // دالة مشاركة القناة
    function shareChannel() {
      if (lastChannelUrl) {
        if (navigator.share) {
          navigator.share({
            title: 'مشاهدة القناة',
            text: 'شاهد هذه القناة على فلكسي TV',
            url: window.location.href
          }).catch(() => {
            copyToClipboard(window.location.href);
            showNotification('📋 تم نسخ رابط الصفحة', 'success');
          });
        } else {
          copyToClipboard(window.location.href);
          showNotification('📋 تم نسخ رابط الصفحة', 'success');
        }
      } else {
        showNotification('❌ لا توجد قناة نشطة للمشاركة', 'warning');
      }
    }

    // دالة كتم/إلغاء كتم الصوت
    function toggleMute() {
      const video = document.getElementById("videoElement");
      const muteIcon = document.getElementById("muteIcon");

      if (video) {
        video.muted = !video.muted;
        muteIcon.textContent = video.muted ? '🔇' : '🔊';
        showNotification(video.muted ? '🔇 تم كتم الصوت' : '🔊 تم إلغاء كتم الصوت', 'info', 1000);
      }
    }

    // دالة ملء الشاشة
    function toggleFullscreen() {
      const playerContainer = document.getElementById("playerContainer");

      if (!document.fullscreenElement) {
        playerContainer.requestFullscreen().then(() => {
          showNotification('🖥️ وضع ملء الشاشة', 'info', 1500);
        }).catch(() => {
          showNotification('❌ فشل في تفعيل ملء الشاشة', 'error');
        });
      } else {
        document.exitFullscreen().then(() => {
          showNotification('📱 الخروج من ملء الشاشة', 'info', 1500);
        });
      }
    }

    function getFavorites() {
      return JSON.parse(localStorage.getItem('favorites') || '[]');
    }

    // تم نقل هذه الدالة إلى الأسفل مع التحسينات

    function showFavoriteNotification(msg = '✅ تم الإضافة إلى المفضلة') {
      const notif = document.getElementById('favNotification');
      notif.textContent = msg;
      notif.classList.remove('hidden');
      setTimeout(() => notif.classList.add('hidden'), 2000);
    }

    function displayFavorites() {
      const btn = event.target;
      if (showingFavorites) {
        const savedContent = localStorage.getItem('savedM3UContent');
        const savedUrl = localStorage.getItem('savedM3UUrl');
        if (savedContent) parseM3U(savedContent);
        else if (savedUrl) fetch(savedUrl).then(r => r.text()).then(parseM3U);
        btn.textContent = '⭐ المفضلة';
        showingFavorites = false;
      } else {
        const favs = getFavorites();
        const filtered = allChannels.filter(c => favs.includes(c.url));
        allChannels = filtered;
        displayChannels('all');
        btn.textContent = '↩️ رجوع للتصنيفات';
        showingFavorites = true;
      }
    }

    function loadSavedChannels() {
      const notice = document.getElementById('loadingNotice');
      notice.classList.remove('hidden');
      const savedContent = localStorage.getItem('savedM3UContent');
      const savedUrl = localStorage.getItem('savedM3UUrl');
      const onComplete = () => setTimeout(() => notice.classList.add('hidden'), 1000);
      if (savedContent) {
        parseM3U(savedContent);
        onComplete();
      if (savedContent) {
  parseM3U(savedContent);
} else if (savedUrl) {
  fetch(savedUrl).then(res => res.text()).then(parseM3U);
} else {
  const defaultUrl = "https://www.arabseed.stream/storage/m3u/playlist_68203bbd9e6114.72123431.m3u";
  fetch(defaultUrl)
    .then(res => res.text())
    .then(content => {
      localStorage.setItem('savedM3UUrl', defaultUrl);
      parseM3U(content);
    });
}

      } else {
        alert("❌ لا توجد قنوات محفوظة");
        onComplete();
      }
    }

    document.getElementById('m3uFile').addEventListener('change', e => {
      const file = e.target.files[0];
      if (!file) return;
      const reader = new FileReader();
      reader.onload = e => {
        const content = e.target.result;
        localStorage.setItem('savedM3UContent', content);
        localStorage.removeItem('savedM3UUrl');
        parseM3U(content);
        const notif = document.getElementById('loadingNotice');
        notif.textContent = "✅ تم حفظ القنوات بنجاح";
        notif.classList.remove("hidden");
        setTimeout(() => {
          notif.classList.add("hidden");
          notif.textContent = "⏳ جارٍ تحميل القنوات...";
        }, 2000);
      };
      reader.readAsText(file);
    });

    document.getElementById('loadUrl').addEventListener('click', () => {
      const url = document.getElementById('m3uUrl').value.trim();
      if (!url) return alert('❌ أدخل رابط صالح');
      fetch(url)
        .then(res => res.text())
        .then(content => {
          localStorage.setItem('savedM3UUrl', url);
          localStorage.removeItem('savedM3UContent');
          parseM3U(content);
        })
        .catch(() => alert('❌ تعذر تحميل الرابط'));
    });

    // تم نقل مستمع البحث إلى الأسفل مع التحسينات




// ✅ المفضله web
function displayFilteredChannels(filteredList) {
  const container = document.getElementById('channelGroups');
  container.innerHTML = '';
  const grouped = {};

  filteredList.forEach(ch => {
    if (!grouped[ch.group]) grouped[ch.group] = [];
    grouped[ch.group].push(ch);
  });

  Object.keys(grouped).forEach(group => {
    const section = document.createElement('div');
    section.innerHTML = `<h2 class="text-xl font-bold mb-2">${group}</h2>`;
    const grid = document.createElement('div');
    grid.className = 'grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4';

    grouped[group].forEach(channel => {
      const card = document.createElement('div');
      card.className = 'bg-gray-800 p-2 rounded hover:scale-105 transition';
      const isFavorite = getFavorites().includes(channel.url);
      card.innerHTML = `
        <img src="${channel.logo}" class="w-full object-contain rounded mx-auto" style="max-height: 60px;" />
        <div class="text-sm mt-2 text-center truncate">${channel.title}</div>
        <button onclick="toggleFavorite(event, '${channel.url}')" class="mt-1 text-yellow-400 text-xs hover:underline">
          ${isFavorite ? '⭐ إزالة من المفضلة' : '☆ أضف إلى المفضلة'}
        </button>`;
      card.addEventListener('click', e => {
        if (e.target.tagName !== 'BUTTON') checkAndPlayChannel(channel.url);
      });
      grid.appendChild(card);
    });

    section.appendChild(grid);
    container.appendChild(section);
  });
}


  // ✅   <!-- زر قائمة م -->

function toggleM3UDropdown() {
  document.getElementById('m3uDropdown').classList.toggle('hidden');
}



    // ✅ تحميل تلقائي عند فتح الصفحة مع حقن رابط افتراضي
    window.onload = () => {
      const savedContent = localStorage.getItem('savedM3UContent');
      const savedUrl = localStorage.getItem('savedM3UUrl');

      if (savedContent) {
        parseM3U(savedContent);
      } else if (savedUrl) {
        fetch(savedUrl).then(res => res.text()).then(parseM3U);
      } else {
        const defaultUrl = "https://www.arabseed.stream/storage/m3u/playlist_68203bbd9e6114.72123431.m3u";
        fetch(defaultUrl)
          .then(res => res.text())
          .then(content => {
            localStorage.setItem('savedM3UUrl', defaultUrl);
            parseM3U(content);
          })
          .catch(() => alert("❌ تعذر تحميل القنوات من الرابط الافتراضي"));
      }
    };




    // تم نقل مستمع النقر إلى الأسفل مع التحسينات







// ✅ زر تحديث القنوات
function loadChannelsFromDefault() {
  const notice = document.getElementById('loadingNotice');
  notice.classList.remove('hidden');

  const defaultUrl = "https://www.arabseed.stream/storage/m3u/playlist_68203bbd9e6114.72123431.m3u";

  fetch(defaultUrl)
    .then(res => res.text())
    .then(content => {
      localStorage.setItem('savedM3UUrl', defaultUrl);  // اختياري إذا أردت الحفظ
      localStorage.removeItem('savedM3UContent');       // إلغاء أي ملف محفوظ
      parseM3U(content);
    })
    .catch(() => alert("❌ تعذر تحميل القنوات من الرابط"))
    .finally(() => setTimeout(() => notice.classList.add('hidden'), 1000));
}





// ✅ خاص app
function uploadApkToServer() {
  const input = document.getElementById('apkUploader');
  const file = input.files[0];
  const status = document.getElementById('apkUploadStatus');
  const progressBar = document.getElementById('apkProgressBar');
  const progressContainer = document.getElementById('apkProgressContainer');

  if (!file) {
    status.innerHTML = `<span class="text-red-400">❌ الرجاء اختيار ملف APK</span>`;
    return;
  }

  // ✅ حساب الحجم بالميغابايت
  const fileSizeMB = (file.size / (1024 * 1024)).toFixed(2);
  status.innerHTML = `⏳ جاري رفع ملف (${fileSizeMB} MB)...`;

  const formData = new FormData();
  formData.append('apk_file', file);

  const xhr = new XMLHttpRequest();
  xhr.open('POST', "/upload-apk", true);
  xhr.setRequestHeader('X-CSRF-TOKEN', document.querySelector('meta[name="csrf-token"]').content);

  // عرض شريط التقدم
  progressContainer.classList.remove('hidden');
  progressBar.style.width = '0%';

  xhr.upload.addEventListener('progress', (e) => {
    if (e.lengthComputable) {
      const percent = Math.round((e.loaded / e.total) * 100);
      progressBar.style.width = percent + '%';
    }
  });

  xhr.onload = () => {
    if (xhr.status === 200) {
      const data = JSON.parse(xhr.responseText);
      if (data.success) {
        progressBar.style.width = '100%';
        status.innerHTML = `<span class="text-green-400">✅ تم الرفع بنجاح</span><br>
          <a href="${data.url}" target="_blank" class="inline-block mt-2 bg-red-600 py-2 px-4 rounded">📥 تحميل التطبيق</a>`;
      } else {
        status.innerHTML = `<span class="text-red-400">❌ ${data.error || 'حدث خطأ أثناء الرفع'}</span>`;
      }
    } else {
      status.innerHTML = `<span class="text-red-400">❌ فشل الاتصال بالسيرفر</span>`;
    }
  };

  xhr.onerror = () => {
    status.innerHTML = `<span class="text-red-400">❌ حدث خطأ في الاتصال</span>`;
  };

  xhr.send(formData);
}





  function uploadM3UToServer() {
  const fileInput = document.getElementById('m3uFile');
  const file = fileInput.files[0];

  if (!file) {
    alert("❌ الرجاء اختيار ملف أولاً");
    return;
  }

  const formData = new FormData();
  formData.append('m3u_file', file);

  fetch('/Dashboard/Settings/upload-m3u', {
    method: 'POST',
    headers: {
      'X-CSRF-TOKEN': '{{ csrf_token() }}'
    },
    body: formData
  })
    .then(res => res.ok ? res.text() : Promise.reject(res))
    .then(() => {
      alert('✅ تم رفع الملف إلى السيرفر بنجاح!');
    })
    .catch(() => alert('❌ فشل رفع الملف'));
}




 // ✅  خاص  ازرار القائمة م
function loadFromUrl(url) {
  const notice = document.getElementById('loadingNotice');
  notice.classList.remove('hidden');

  fetch(url)
    .then(res => res.text())
    .then(content => {
      localStorage.setItem('savedM3UUrl', url);
      localStorage.removeItem('savedM3UContent');
      parseM3U(content);
    })
    .catch(() => alert("❌ تعذر تحميل القنوات من الرابط"))
    .finally(() => setTimeout(() => notice.classList.add('hidden'), 1000));
}











let isSideBySide = true;

function togglePlayerLayout() {
  const channelGroups = document.getElementById("channelGroups");
  const playerContainer = document.getElementById("playerContainer");
  const sideBySideContainer = document.getElementById("sideBySideContainer");
  const btn = document.getElementById("toggleLayoutBtn");

  isSideBySide = !isSideBySide;

  if (isSideBySide) {
    // عرض جنباً إلى جنب
    sideBySideContainer.classList.remove('hidden');
    channelGroups.classList.remove('hidden');
    playerContainer.classList.remove('hidden');

    // تحديث النص والأيقونة
    btn.innerHTML = `
      <span class="text-sm md:text-base">🧭</span>
      <span class="text-xs md:text-sm font-medium">عرض</span>
    `;

    showNotification('📺 عرض جنباً إلى جنب', 'info', 1500);
  } else {
    // عرض المشغل فقط
    channelGroups.classList.add('hidden');

    // تحديث النص والأيقونة
    btn.innerHTML = `
      <span class="text-sm md:text-base">📋</span>
      <span class="text-xs md:text-sm font-medium">قنوات</span>
    `;

    showNotification('🎬 عرض المشغل فقط', 'info', 1500);
  }
}













function applyTransform() {
  videoEl.style.transform = `scale(${lastScale}) translate(${currentX}px, ${currentY}px)`;
}



function distance(touches) {
  const dx = touches[0].clientX - touches[1].clientX;
  const dy = touches[0].clientY - touches[1].clientY;
  return Math.sqrt(dx * dx + dy * dy);
}




//<script>
// 🌟 متغيرات التكبير
let currentZoomIndex = 0;
const zoomLevels = [1, 1.3, 1.6, 2];
let initialDistance = null;
let lastScale = 1;
let currentX = 0;
let currentY = 0;
let lastX = 0;
let lastY = 0;

const wrapper = document.getElementById('zoomWrapper');
const videoEl = document.getElementById('videoElement');
const zoomBtn = document.getElementById('zoomButton');

// ✅ دالة تطبيق التحويل
function applyTransform() {
  videoEl.style.transform = `scale(${lastScale}) translate(${currentX}px, ${currentY}px)`;
}

// ✅ دالة حساب المسافة بين إصبعين
function distance(touches) {
  const dx = touches[0].clientX - touches[1].clientX;
  const dy = touches[0].clientY - touches[1].clientY;
  return Math.sqrt(dx * dx + dy * dy);
}

// ✅ التكبير اليدوي عبر الزر
function cycleZoom() {
  currentZoomIndex = (currentZoomIndex + 1) % zoomLevels.length;
  lastScale = zoomLevels[currentZoomIndex];
  currentX = 0;
  currentY = 0;
  applyTransform();
  zoomBtn.innerText = `✂️ Zoom ×${lastScale}`;
}

// ✅ إظهار زر التكبير فقط في وضع ملء الشاشة
document.addEventListener('fullscreenchange', () => {
  if (document.fullscreenElement) {
    zoomBtn.classList.remove('hidden');
  } else {
    zoomBtn.classList.add('hidden');
    lastScale = 1;
    currentX = 0;
    currentY = 0;
    applyTransform();
    zoomBtn.innerText = '✂️ Zoom ×1';
    currentZoomIndex = 0;
  }
});

// ✅ أحداث اللمس للتكبير
wrapper.addEventListener('touchstart', e => {
  if (e.touches.length === 2) {
    initialDistance = distance(e.touches);
  } else if (e.touches.length === 1 && lastScale > 1) {
    lastX = e.touches[0].clientX - currentX;
    lastY = e.touches[0].clientY - currentY;
  }
}, { passive: false });

wrapper.addEventListener('touchmove', e => {
  if (e.touches.length === 2 && initialDistance) {
    e.preventDefault();
    const newDistance = distance(e.touches);
    let scale = newDistance / initialDistance;

    const videoWidth = videoEl.offsetWidth;
    if (videoWidth === 0) return; // ✅ إصلاح التجمّد

    const screenWidth = window.innerWidth;
    const maxScale = (screenWidth / videoWidth) * 1.2;
    lastScale = Math.min(Math.max(scale, 1), maxScale);

    applyTransform();
  } else if (e.touches.length === 1 && lastScale > 1) {
    e.preventDefault();
    currentX = 0;
    currentY = 0;
    applyTransform();
  }
}, { passive: false });

// ✅ حركة اليد عند بدء التشغيل
function showZoomGesture() {
  if (!document.fullscreenElement) return;
  const gesture = document.getElementById('zoomGesture');
  if (!gesture) return;
  gesture.classList.remove('hidden');
  setTimeout(() => gesture.classList.add('hidden'), 3000);
}




//upload-xtream
function loadXtream() {
  const host = document.getElementById('xtreamHost').value.trim();
  const user = document.getElementById('xtreamUser').value.trim();
  const pass = document.getElementById('xtreamPass').value.trim();

  if (!host || !user || !pass) {
    alert("❌ الرجاء تعبئة جميع الحقول");
    return;
  }

  const port = host.split(':')[2] || '8080'; // محاولة استخراج البورت
  const cleanHost = host.split(':')[0] + '://' + host.split('/')[2]; // تنظيف الرابط

  const formData = new FormData();
  formData.append('host', cleanHost);
  formData.append('port', port);
  formData.append('username', user);
  formData.append('password', pass);

  const csrf = document.querySelector('meta[name="csrf-token"]').content;

  fetch('/upload-xtream', {
    method: 'POST',
    headers: {
      'X-CSRF-TOKEN': csrf
    },
    body: formData
  })
    .then(res => res.json())
    .then(data => {
      if (data.success) {
        alert(`✅ تم تحميل الملف:\n${data.download_url}`);
        // يمكنك الآن تشغيل تحليل الملف أو إعادة تحميل القنوات
      } else {
        alert(`❌ خطأ: ${data.error || "غير معروف"}`);
      }
    })
    .catch(err => {
      alert("❌ فشل الاتصال بالخادم");
      console.error(err);
    });
}



// ===== دوال جديدة لتحسين التفاعل =====

// دالة ملء الشاشة
function toggleFullscreen() {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen().catch(err => {
      showNotification('error', 'لا يمكن تفعيل ملء الشاشة');
    });
  } else {
    document.exitFullscreen();
  }
}

// دالة مشاركة القناة
function shareChannel() {
  if (lastChannelUrl) {
    if (navigator.share) {
      navigator.share({
        title: 'فلكسي TV - قناة مباشرة',
        text: 'شاهد هذه القناة المباشرة',
        url: window.location.href
      });
    } else {
      // نسخ الرابط للحافظة
      navigator.clipboard.writeText(window.location.href).then(() => {
        showNotification('success', 'تم نسخ الرابط إلى الحافظة');
      });
    }
  } else {
    showNotification('error', 'لا توجد قناة مشغلة حالياً');
  }
}

// دالة كتم الصوت
function toggleMute() {
  const video = document.getElementById('videoElement');
  const muteIcon = document.getElementById('muteIcon');

  if (video.muted) {
    video.muted = false;
    muteIcon.textContent = '🔊';
  } else {
    video.muted = true;
    muteIcon.textContent = '🔇';
  }
}

// دالة عرض الإشعارات المحسنة
function showNotification(type, message, duration = 3000) {
  const notifications = {
    success: document.getElementById('successNotice'),
    error: document.getElementById('errorNotice'),
    loading: document.getElementById('loadingNotice'),
    favorite: document.getElementById('favNotification')
  };

  const notification = notifications[type];
  if (!notification) return;

  // تحديث الرسالة
  if (type === 'success') {
    document.getElementById('successMessage').textContent = message;
  } else if (type === 'error') {
    document.getElementById('errorMessage').textContent = message;
  }

  // إظهار الإشعار
  notification.classList.remove('hidden');
  notification.classList.add('slide-up');

  // إخفاء الإشعار بعد المدة المحددة
  setTimeout(() => {
    notification.classList.add('hidden');
    notification.classList.remove('slide-up');
  }, duration);
}

// تحسين البحث السريع
document.getElementById('searchInput').addEventListener('input', function(e) {
  const term = e.target.value.toLowerCase().trim();
  const clearBtn = document.getElementById('clearSearch');
  const searchResults = document.getElementById('searchResults');
  const resultsList = document.getElementById('searchResultsList');

  // إظهار/إخفاء زر المسح
  if (term) {
    clearBtn.classList.remove('hidden');
  } else {
    clearBtn.classList.add('hidden');
    searchResults.classList.add('hidden');
    displayChannels('all');
    return;
  }

  // البحث في القنوات
  const filtered = allChannels.filter(ch =>
    ch.title.toLowerCase().includes(term) ||
    ch.group.toLowerCase().includes(term)
  );

  // عرض النتائج السريعة
  if (filtered.length > 0 && term.length > 1) {
    resultsList.innerHTML = '';
    filtered.slice(0, 5).forEach(channel => {
      const item = document.createElement('div');
      item.className = 'flex items-center gap-3 p-2 hover:bg-gray-700 rounded cursor-pointer';
      item.innerHTML = `
        <img src="${channel.logo}" class="w-8 h-8 rounded object-cover" />
        <div class="flex-1">
          <div class="text-sm font-medium">${channel.title}</div>
          <div class="text-xs text-gray-400">${channel.group}</div>
        </div>
      `;
      item.addEventListener('click', () => {
        checkAndPlayChannel(channel.url);
        searchResults.classList.add('hidden');
      });
      resultsList.appendChild(item);
    });
    searchResults.classList.remove('hidden');
  } else {
    searchResults.classList.add('hidden');
  }

  // تصفية القنوات المعروضة
  displayFilteredChannels(filtered);
});

// زر مسح البحث
document.getElementById('clearSearch').addEventListener('click', function() {
  document.getElementById('searchInput').value = '';
  this.classList.add('hidden');
  document.getElementById('searchResults').classList.add('hidden');
  displayChannels('all');
});

// تحسين عداد المفضلة
function updateFavoriteCount() {
  const favCount = document.getElementById('favCount');
  const favorites = getFavorites();

  if (favorites.length > 0) {
    favCount.textContent = favorites.length;
    favCount.classList.remove('hidden');
  } else {
    favCount.classList.add('hidden');
  }
}

// تحسين دالة إضافة/إزالة المفضلة
function toggleFavorite(event, url) {
  event.stopPropagation();
  let favorites = getFavorites();
  let message;

  if (favorites.includes(url)) {
    favorites = favorites.filter(f => f !== url);
    message = 'تمت الإزالة من المفضلة';
    showNotification('error', message);
  } else {
    favorites.push(url);
    message = 'تم الإضافة إلى المفضلة';
    showNotification('favorite', message);
  }

  localStorage.setItem('favorites', JSON.stringify(favorites));
  updateFavoriteCount();
  displayChannels();
}

// تحسين عرض القنوات مع الأنيميشن
function displayChannels(selected = 'all') {
  const container = document.getElementById('channelGroups');
  container.innerHTML = '';
  const groups = [...new Set(allChannels.map(c => c.group))];

  groups.forEach((group, groupIndex) => {
    if (selected !== 'all' && selected !== group) return;

    const section = document.createElement('div');
    section.className = 'fade-in';
    section.style.animationDelay = `${groupIndex * 0.1}s`;
    section.innerHTML = `<h2 class="text-xl font-bold mb-4 text-gradient bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">${group}</h2>`;

    const grid = document.createElement('div');
    grid.className = 'grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4';

    allChannels.filter(c => c.group === group).forEach((channel, index) => {
      const card = document.createElement('div');
      card.className = 'channel-card fade-in';
      card.style.animationDelay = `${(groupIndex * 0.1) + (index * 0.05)}s`;

      const isFavorite = getFavorites().includes(channel.url);
      card.innerHTML = `
        <div class="relative">
          <img src="${channel.logo}" class="w-full object-contain rounded-lg mx-auto transition-transform duration-300" style="max-height: 60px;" />
          <div class="absolute top-2 right-2">
            <button onclick="toggleFavorite(event, '${channel.url}')" class="text-yellow-400 hover:text-yellow-300 transition-colors duration-200">
              ${isFavorite ? '⭐' : '☆'}
            </button>
          </div>
        </div>
        <div class="text-sm mt-3 text-center font-medium truncate">${channel.title}</div>
        <div class="text-xs text-gray-400 text-center mt-1">${channel.group}</div>
      `;

      card.addEventListener('click', e => {
        if (e.target.tagName !== 'BUTTON') {
          checkAndPlayChannel(channel.url);
          // إضافة تأثير النقر
          card.style.transform = 'scale(0.95)';
          setTimeout(() => {
            card.style.transform = '';
          }, 150);
        }
      });

      grid.appendChild(card);
    });

    section.appendChild(grid);
    container.appendChild(section);
  });

  updateFavoriteCount();
}

// تحسين تحديث شريط التصنيفات
function updateCategoryBar() {
  categoryMenu.innerHTML = '';
  const groups = [...new Set(allChannels.map(c => c.group))];

  const allBtn = document.createElement('button');
  allBtn.textContent = '📺 كل القنوات';
  allBtn.className = 'block w-full text-right px-4 py-3 text-sm hover:bg-gray-700 transition-colors duration-200 rounded-lg mb-1';
  allBtn.onclick = () => {
    categoryBtn.innerHTML = `
      <span class="text-lg">📂</span>
      <span class="font-semibold">كل القنوات</span>
      <svg class="w-4 h-4 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
      </svg>
    `;
    displayChannels('all');
    categoryMenu.classList.add('hidden');
  };
  categoryMenu.appendChild(allBtn);

  groups.forEach(group => {
    const btn = document.createElement('button');
    btn.textContent = group;
    btn.className = 'block w-full text-right px-4 py-3 text-sm hover:bg-gray-700 transition-colors duration-200 rounded-lg mb-1';
    btn.onclick = () => {
      categoryBtn.innerHTML = `
        <span class="text-lg">📂</span>
        <span class="font-semibold">${group}</span>
        <svg class="w-4 h-4 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
        </svg>
      `;
      displayChannels(group);
      categoryMenu.classList.add('hidden');
    };
    categoryMenu.appendChild(btn);
  });
}

// تحسين دالة تبديل القائمة
categoryBtn.addEventListener('click', () => {
  const arrow = document.getElementById('categoryArrow');
  categoryMenu.classList.toggle('hidden');

  if (categoryMenu.classList.contains('hidden')) {
    arrow.style.transform = 'rotate(0deg)';
  } else {
    arrow.style.transform = 'rotate(180deg)';
  }
});

// إضافة مستمع للنقر خارج القائمة
document.addEventListener('click', (e) => {
  if (!categoryBtn.contains(e.target) && !categoryMenu.contains(e.target)) {
    categoryMenu.classList.add('hidden');
    document.getElementById('categoryArrow').style.transform = 'rotate(0deg)';
  }

  if (!document.getElementById('searchInput').contains(e.target) && !document.getElementById('searchResults').contains(e.target)) {
    document.getElementById('searchResults').classList.add('hidden');
  }
});

// تحديث العداد عند تحميل الصفحة
window.addEventListener('load', () => {
  updateFavoriteCount();
});

    </script>
  </body>
</html>
