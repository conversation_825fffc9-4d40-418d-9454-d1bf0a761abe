<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use C<PERSON>brock\EloquentSluggable\Sluggable;
use App\ImageUpload;
use App\Instagram;
use App\Category;
use App\Client;

class Serie extends Model
{

    use Sluggable;
    protected $table="series";
    /**
     * 
     * 
     * Return the sluggable configuration array for this Model.
     *
     * @return array
     * Google Drive.
     * 
     */
    public function sluggable(): array
    {
        return [
            'slug' => [
                'source' => 'Title_en'
            ]
        ];
    }
    /**
     * The attributes that are mass assignable.
     * Google Drive.
     * @var array.
     */
    protected $fillable = [
    'author_id', 'category_id','Instagram_id','audio_id',
    'Title_ar','Title_fr','Title_en',
    'body_fr','body_en','body_ar','ImageUpload_id',
    'slug','featured'
    ];

    // THIS function Category 
     public function Category()
    {
        return $this->belongsTo(Category::class,'category_id');
    }
    public function genres()
    {
        return $this->belongsToMany(Category::class, 'category_serie', 'serie_id', 'category_id');
    }

    // THIS function Client 
    public function Client()
    {
        return $this->belongsTo(Client::class,'author_id');
    } 

    public function ImageUpload()
    {
        return $this->belongsTo(ImageUpload::class,'ImageUpload_id');
    }

     public function Instagram()
    {
        return $this->belongsTo(Instagram::class,'Instagram_id');
    }

    public function Seasons()
    {
        return $this->hasMany(Season::class,"id_serie");
    }
}
