<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Post;
use App\Category;
use App\User;

class BasicDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        try {
            // Create a default user if none exists
            $user = User::first();
            if (!$user) {
                // Use DB insert to avoid sluggable issues
                DB::table('users')->insert([
                    'name' => 'Admin User',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('password'),
                    'email_verified_at' => now(),
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
                $user = User::first();
                $this->command->info('✅ Created default user');
            }

            // Create some categories
            $categories = [
                [
                    'Title_ar' => 'أفلام',
                    'Title_en' => 'Movies',
                    'Title_fr' => 'Films',
                    'slug' => 'movies',
                    'color' => '#ff0000',
                ],
                [
                    'Title_ar' => 'مسلسلات',
                    'Title_en' => 'Series',
                    'Title_fr' => 'Séries',
                    'slug' => 'series',
                    'color' => '#00ff00',
                ],
                [
                    'Title_ar' => 'بث مباشر',
                    'Title_en' => 'Live TV',
                    'Title_fr' => 'TV en direct',
                    'slug' => 'live-tv',
                    'color' => '#0000ff',
                ],
                [
                    'Title_ar' => 'رياضة',
                    'Title_en' => 'Sports',
                    'Title_fr' => 'Sports',
                    'slug' => 'sports',
                    'color' => '#ffff00',
                ],
                [
                    'Title_ar' => 'أخبار',
                    'Title_en' => 'News',
                    'Title_fr' => 'Actualités',
                    'slug' => 'news',
                    'color' => '#ff00ff',
                ],
            ];

            foreach ($categories as $categoryData) {
                Category::firstOrCreate(
                    ['slug' => $categoryData['slug']],
                    $categoryData
                );
            }
            $this->command->info('✅ Created ' . count($categories) . ' categories');

            // Create some sample posts
            $posts = [
                [
                    'Title_ar' => 'فيلم تجريبي 1',
                    'Title_en' => 'Sample Movie 1',
                    'Title_fr' => 'Film d\'exemple 1',
                    'body_ar' => 'وصف الفيلم التجريبي الأول',
                    'body_en' => 'Description of sample movie 1',
                    'body_fr' => 'Description du film d\'exemple 1',
                    'slug' => 'sample-movie-1',
                    'featured' => 1,
                    'slider' => 1,
                    'category_id' => 1,
                ],
                [
                    'Title_ar' => 'مسلسل تجريبي 1',
                    'Title_en' => 'Sample Series 1',
                    'Title_fr' => 'Série d\'exemple 1',
                    'body_ar' => 'وصف المسلسل التجريبي الأول',
                    'body_en' => 'Description of sample series 1',
                    'body_fr' => 'Description de la série d\'exemple 1',
                    'slug' => 'sample-series-1',
                    'featured' => 1,
                    'slider' => 2,
                    'category_id' => 2,
                ],
                [
                    'Title_ar' => 'قناة تجريبية 1',
                    'Title_en' => 'Sample Channel 1',
                    'Title_fr' => 'Chaîne d\'exemple 1',
                    'body_ar' => 'وصف القناة التجريبية الأولى',
                    'body_en' => 'Description of sample channel 1',
                    'body_fr' => 'Description de la chaîne d\'exemple 1',
                    'slug' => 'sample-channel-1',
                    'featured' => 1,
                    'slider' => 1,
                    'category_id' => 3,
                ],
            ];

            foreach ($posts as $postData) {
                $postData['author_id'] = $user->id;
                $postData['meta_description'] = $postData['body_en'];
                $postData['meta_keywords'] = 'sample, test, demo';

                Post::firstOrCreate(
                    ['slug' => $postData['slug']],
                    $postData
                );
            }
            $this->command->info('✅ Created ' . count($posts) . ' sample posts');

            // Now create relationships
            $categories = Category::all();
            $posts = Post::all();

            if ($categories->count() > 0 && $posts->count() > 0) {
                foreach ($posts as $index => $post) {
                    $categoryIndex = $index % $categories->count();
                    $category = $categories[$categoryIndex];

                    // Insert into category_movie pivot table
                    DB::table('category_movie')->insertOrIgnore([
                        'category_id' => $category->id,
                        'movie_id' => $post->id,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);

                    // Also insert some into category_serie for variety
                    if ($index % 2 == 0) {
                        DB::table('category_serie')->insertOrIgnore([
                            'category_id' => $category->id,
                            'serie_id' => $post->id,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    }
                }

                $this->command->info('✅ Created category-post relationships');
            }

            $this->command->info('🎉 Basic data seeding completed successfully!');

        } catch (\Exception $e) {
            $this->command->error('❌ Error seeding data: ' . $e->getMessage());
        }
    }
}
