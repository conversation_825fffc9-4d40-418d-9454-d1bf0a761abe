<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\User; 
use App\Post;
use App\Category;
use App\Serie;
use Auth;
use File;
use Validator;


class Categorys extends Controller
{

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        $Genres =  Category::all();
        return view('Pages.Cat.index',compact('Genres'));
    }
    /**
     * Display the specified resource.
     *
     * @param  int  $slug
     * @return \Illuminate\Http\Response
     */
    
 
     public function show($slug)
     {
         $queryType = request()->query('type'); // Get the query parameter from the URL
         
         if (request()->query('year')) {
            $Category = Category::firstOrFail();
            $queryYear = request()->query('year'); 
            // Retrieve series that match the category or genre
            $Movies = Post::where('featured', 'on')
                ->where("year",$queryYear)
                ->oldest()
                ->simplePaginate(50);
    
                return view('Pages.Cat.show', compact('Movies', 'Category'));
        }

     
         // Retrieve the category by slug
         $Category = Category::where('slug', $slug)->firstOrFail();
     
        if ($queryType === 'series') {
             // Retrieve series that match the category or genre
             $Series = Serie::WhereHas('genres', function ($query) use ($Category) {
                     $query->where('Title_en', $Category->Title_en);
                 })
                 ->where('featured', 'on')
                 ->oldest()
                 ->simplePaginate(50);
     
             return view('Pages.Series.index', compact('Series', 'Category'));
         }  else {
             // Retrieve movies that match the category or genre
             $Movies = Post::where('category_id', $Category->id)
                 ->orWhereHas('genres', function ($query) use ($Category) {
                     $query->where('Title_en', $Category->Title_en);
                 })
                 ->orWhere(function ($query) use ($Category) {
                     $query->whereHas('Season', function ($seasonQuery) use ($Category) {
                         $seasonQuery->whereHas('Serie.genres', function ($genreQuery) use ($Category) {
                             $genreQuery->where('Title_en', $Category->Title_en);
                         });
                     });
                 })
                 ->where('featured', 'on')
                 ->oldest()
                 ->simplePaginate(50);
     
             return view('Pages.Cat.show', compact('Movies', 'Category'));
         }
     }
     
}