<?php

namespace App\Http\Controllers;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\ImageUpload;
use App\User;
use App\Category;
use App\Instagram;
use App\Gallery;
use App\Post;
use App\Serie;
use Auth;
use File;
use Validator;
use Illuminate\Support\Facades\Cache;

class HomeController extends Controller
{
    /**
     * Helper method to get sliders with fallback
     *
     * @param string $order 'latest' or 'oldest'
     * @param int $sliderType 1 or 2
     * @param int $perPage number of items per page
     * @return \Illuminate\Pagination\Paginator
     */
    private function getSliders($order = 'latest', $sliderType = 1, $perPage = 15)
    {
        try {
            // Try to get sliders with slider column
            $query = Post::where("featured", "=", "on");

            if ($order === 'latest') {
                $query = $query->latest();
            } else {
                $query = $query->oldest();
            }

            return $query->where("slider", "=", $sliderType)->simplepaginate($perPage);

        } catch (\Exception $e) {
            // Fallback if slider column doesn't exist
            $query = Post::where("featured", "=", "on");

            if ($order === 'latest') {
                $query = $query->latest();
            } else {
                $query = $query->oldest();
            }

            return $query->simplepaginate($perPage);
        }
    }

    /**
     *
     * Show the application Dashboard
     *
     * @return \Illuminate\Contracts\Support\Renderable
     *
     */
    public function index()
    {
        // Get sliders using helper method
        $Latest_sliders = $this->getSliders('latest', 1, 15);
        $oldest_sliders = $this->getSliders('oldest', 2, 15);

    	$Genres = Category::has('movies')->orWhereHas('series')->get();
        $Locale = app()->getLocale(); // Get the current locale

        // Retrieve the latest 15 featured movies
        $Latest_Movies = Post::where('featured', 'on')
        ->whereDoesntHave('genres', function ($query) {
            $query->where('slug', 'live-tv');
        })
        ->latest()
        ->take(15); // Limit to 15 posts

        // Fetch the results
        $Latest_Movies = $Latest_Movies->get(); // Retrieve the posts

        // If the language is Arabic, we want to order by creation date ascending (oldest to latest)
        if ($Locale == 'ar') {
            $Latest_Movies = $Latest_Movies->sortBy('created_at'); // Sort by oldest to newest for Arabic
        } else {
            $Latest_Movies = $Latest_Movies->sortByDesc('created_at'); // Sort by newest to oldest for non-Arabic
        }
        return view('Pages/home',compact('Genres','Latest_Movies','Latest_sliders','oldest_sliders'));
    }
    /**
     *
     *
     * Show the application.
     * Dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable.
     *
     *
     */
    public function about()
    {
        $Albums_Latests = Instagram::oldest()->simplepaginate(12);
        return view('Pages/about',compact('Albums_Latests'));
    }

     /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function contacts()
    {
        return view('Pages/Contact');
    }

    /**
     * Show the Application
     * @return \Illuminate\Contracts\Support\Renderable.
     */
    public function live()
    {
        $Genres = Category::has('movies')->get();
        $Movies = Post::where("featured","=","on")->whereHas('genres', function ($query) {
            $query->where('slug', 'live-tv');
        })->latest()->simplePaginate(30);
        $Latest_Movies = Post::latest()->where("featured","=","on")->take(1)->get(); // Example query for latest movies

        // Get sliders using helper method
        $Latest_sliders = $this->getSliders('latest', 1, 15);
        $oldest_sliders = $this->getSliders('oldest', 2, 15);

        return view('Pages/live',compact('Genres','Latest_Movies','Movies','Latest_sliders','oldest_sliders'));
    }

}
