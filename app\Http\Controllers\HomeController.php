<?php

namespace App\Http\Controllers;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\ImageUpload;
use App\User; 
use App\Category;
use App\Instagram;
use App\Gallery;
use App\Post;
use App\Serie;
use Auth;
use File;
use Validator; 
use Illuminate\Support\Facades\Cache; 

class HomeController extends Controller
{

    /**
     * 
     * Show the application Dashboard
     *
     * @return \Illuminate\Contracts\Support\Renderable
     * 
     */
    public function index()
    {
        // This Area Sliders //
        $Latest_sliders =  Post::latest()->where("featured","=","on")->where("slider","=","1")->simplepaginate(15);
        $oldest_sliders =  Post::oldest()->where("featured","=","on")->where("slider","=","2")->simplepaginate(15);
    	$Genres = Category::has('movies')->orWhereHas('series')->get();
        $Locale = app()->getLocale(); // Get the current locale

        // Retrieve the latest 15 featured movies
        $Latest_Movies = Post::where('featured', 'on')
        ->whereDoesntHave('genres', function ($query) {
            $query->where('slug', 'live-tv');
        })
        ->latest()
        ->take(15); // Limit to 15 posts
        
        // Fetch the results
        $Latest_Movies = $Latest_Movies->get(); // Retrieve the posts
        
        // If the language is Arabic, we want to order by creation date ascending (oldest to latest)
        if ($Locale == 'ar') {
            $Latest_Movies = $Latest_Movies->sortBy('created_at'); // Sort by oldest to newest for Arabic
        } else {
            $Latest_Movies = $Latest_Movies->sortByDesc('created_at'); // Sort by newest to oldest for non-Arabic
        }
        return view('Pages/home',compact('Genres','Latest_Movies','Latest_sliders','oldest_sliders'));
    }
    /**
     * 
     * 
     * Show the application.
     * Dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable.
     * 
     * 
     */
    public function about()
    {
        $Albums_Latests = Instagram::oldest()->simplepaginate(12);
        return view('Pages/about',compact('Albums_Latests'));
    } 

     /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function contacts()
    {
        return view('Pages/Contact');
    }

    /**
     * Show the Application
     * @return \Illuminate\Contracts\Support\Renderable.
     */
    public function live()
    {
        $Genres = Category::has('movies')->get();
        $Movies = Post::where("featured","=","on")->whereHas('genres', function ($query) {
            $query->where('slug', 'live-tv');
        })->latest()->simplePaginate(30);
        $Latest_Movies = Post::latest()->where("featured","=","on")->take(1)->get(); // Example query for latest movies
        $Latest_sliders =  Post::latest()->where("featured","=","on")->where("slider","=","1")->simplepaginate(15);
        $oldest_sliders =  Post::oldest()->where("featured","=","on")->where("slider","=","2")->simplepaginate(15);
        return view('Pages/live',compact('Genres','Latest_Movies','Movies','Latest_sliders','oldest_sliders'));
    } 

}
